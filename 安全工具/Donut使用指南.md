# Donut Shellcode 生成工具使用指南

## ⚠️ 安全警告
**本工具仅供授权的安全测试和研究使用！**
- 请勿用于非法用途
- 仅在受控环境中使用
- 使用者需承担相应法律责任

## 概述
Donut 是一个用于将 .NET 程序集、PE 文件和脚本转换为 shellcode 的工具。本文档记录了常用的 Donut 命令示例。

## 基本语法
```bash
donut.exe [选项] -i <输入文件> -o <输出文件>
```

## 参数说明
- `-i` - 输入文件路径
- `-o` - 输出文件路径
- `-a` - 架构 (1=x86, 2=amd64, 3=x86+amd64)
- `-b` - 绕过技术 (1=跳过, 2=中止, 3=继续)
- `-e` - 退出方式 (1=退出线程, 2=退出进程, 3=不退出)
- `-z` - 压缩级别 (1=无, 2=aPLib, 3=LZNT1, 4=Xpress)

## 使用示例

### 基本 PE 文件转换
```bash
# 转换 s1368081s.exe 为 shellcode
donut.exe -i s1368081s.exe -a 2 -o winnexuS136 -b 3 -e 3 -z 3

# 转换 s8081s.exe 为 shellcode
donut.exe -i s8081s.exe -a 2 -o winnexus -b 3 -e 3 -z 3

# 转换 Go shellcode loader
donut.exe -i goshellcodeloader.exe -a 2 -o tecent -b 3 -e 3 -z 3
```

### 工具程序转换
```bash
# 转换读取工具
donut.exe -i read.exe -a 2 -o read -b 3 -e 3 -z 3

# 转换终止工具
donut.exe -i kill.exe -a 2 -o kill.bin -b 3 -e 3 -z 3
```

### 特殊用例
```bash
# 转换编号为 143 的文件
donut.exe -i 143 -a 2 -o 143tecent -b 3 -e 3 -z 3
```

## 推荐配置

### 标准配置
```bash
# 64位架构，继续执行，不退出，LZNT1压缩
donut.exe -i input.exe -a 2 -o output.bin -b 3 -e 3 -z 3
```

### 兼容性配置
```bash
# 支持32位和64位架构
donut.exe -i input.exe -a 3 -o output.bin -b 3 -e 3 -z 3
```

## 高级选项

### .NET 程序集
```bash
# 转换 .NET 程序集
donut.exe -i assembly.exe -c ClassName -m MethodName -o output.bin
```

### 脚本文件
```bash
# 转换 PowerShell 脚本
donut.exe -i script.ps1 -o output.bin

# 转换 VBScript
donut.exe -i script.vbs -o output.bin
```

## 输出格式

### 二进制格式
```bash
# 生成原始二进制 shellcode
donut.exe -i input.exe -f 1 -o output.bin
```

### C 数组格式
```bash
# 生成 C 语言数组格式
donut.exe -i input.exe -f 2 -o output.c
```

### Base64 格式
```bash
# 生成 Base64 编码格式
donut.exe -i input.exe -f 3 -o output.b64
```

## 注意事项
- 确保输入文件存在且可访问
- 输出文件路径必须可写
- 某些反病毒软件可能检测生成的 shellcode
- 测试生成的 shellcode 在目标环境中的兼容性

## 故障排除

### 常见错误
1. **文件不存在**: 检查输入文件路径
2. **权限不足**: 确保有读写权限
3. **格式不支持**: 验证输入文件格式

### 调试选项
```bash
# 启用详细输出
donut.exe -i input.exe -o output.bin -v
```

## 相关工具
- **Metasploit** - 可以使用生成的 shellcode
- **Cobalt Strike** - 支持 Donut 生成的载荷
- **Empire** - 可以集成 Donut 输出

## 参考资源
- [Donut 官方文档](https://github.com/TheWover/donut)
- [Shellcode 注入技术](https://attack.mitre.org/techniques/T1055/)

## 免责声明
本工具和文档仅供教育和授权的安全测试使用。任何非法使用造成的后果由使用者承担。