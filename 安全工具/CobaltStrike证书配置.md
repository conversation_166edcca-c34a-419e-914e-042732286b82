# Cobalt Strike 证书配置

## ⚠️ 安全警告
**本文档仅供授权的安全测试和研究使用！**
- 请勿用于非法用途
- 仅在受控环境中使用
- 使用者需承担相应法律责任

## 概述
本文档包含 Cobalt Strike 团队服务器的 HTTPS 证书配置方法。

## 生成 SSL 证书

### 使用 keytool 生成证书
```bash
keytool -genkeypair \
    -alias myserver \
    -keystore cobaltstrike.store \
    -storetype PKCS12 \
    -storepass "XvSaS7HEebz3tDdrN23zpux6AwK8ptcnnx8vA7jynExdXvRWdR" \
    -keypass "XvSaS7HEebz3tDdrN23zpux6AwK8ptcnnx8vA7jynExdXvRWdR" \
    -keyalg RSA \
    -keysize 2048 \
    -validity 365 \
    -dname "CN=cdn.digitalservices.global, OU=Infrastructure, O=Digital Services Global Ltd, L=Dublin, ST=Dublin, C=IE" \
    -ext SAN=dns:cdn.digitalservices.global,dns:static.digitalservices.global
```

## Malleable C2 配置

### HTTPS 证书配置
```c
https_certificate {
    set keystore "cobaltstrike.store";
    set password "XvSaS7HEebz3tDdrN23zpux6AwK8ptcnnx8vA7jynExdXvRWdR";
}
```

## 参数说明

### keytool 参数
- `-alias` - 证书别名
- `-keystore` - 密钥库文件名
- `-storetype` - 密钥库类型（PKCS12）
- `-storepass` - 密钥库密码
- `-keypass` - 私钥密码
- `-keyalg` - 密钥算法（RSA）
- `-keysize` - 密钥长度（2048位）
- `-validity` - 证书有效期（天）
- `-dname` - 证书主题信息
- `-ext SAN` - 主题备用名称

### 证书信息
- **CN**: cdn.digitalservices.global
- **OU**: Infrastructure
- **O**: Digital Services Global Ltd
- **L**: Dublin
- **ST**: Dublin
- **C**: IE

## 使用建议
1. 使用真实的域名和组织信息
2. 定期更新证书
3. 使用强密码保护密钥库
4. 配置适当的 SAN 扩展

## 注意事项
- 确保域名解析正确
- 证书信息应与目标环境匹配
- 定期检查证书有效期
- 妥善保管密钥库文件和密码