# 安全工具资源索引

## 概述
本文档收集了网络安全领域的各类工具和资源链接，来源于 [All-Defense-Tool](https://github.com/guchangan1/All-Defense-Tool/) 项目。

## ⚠️ 使用声明
- 本索引仅供安全研究和学习使用
- 请在授权环境中使用相关工具
- 使用者需承担相应法律责任

## 📁 工具分类目录

### 主要分类
- **半/全自动化利用工具** - 综合性渗透测试工具
- **信息收集工具** - 资产发现、子域名收集、目录扫描等
- **漏洞利用工具** - 各类漏洞扫描和利用工具
- **内网渗透工具** - 后渗透、横向移动、权限提升等
- **基础设施搭建** - 攻防环境部署和代理工具
- **防守方工具** - 应急响应、安全建设、查杀工具

## 📖 完整工具列表

由于工具列表非常庞大（包含数百个工具），完整的分类和详细信息请访问原始项目：

**🔗 [All-Defense-Tool 完整项目地址](https://github.com/guchangan1/All-Defense-Tool/)**

## 🔥 推荐工具（精选）

### 综合性工具
- **yakit** - 单兵作战武器库
- **fscan** - 内网综合扫描工具
- **nuclei** - 基于 YAML 的漏洞扫描器
- **xray** - 功能强大的安全评估工具

### 信息收集
- **OneForAll** - 功能强大的子域收集工具
- **subfinder** - 被动子域名发现工具
- **dirsearch** - Web 路径扫描工具
- **EHole** - 红队重点攻击系统指纹探测

### 漏洞利用
- **pocsuite3** - 开源的远程漏洞测试框架
- **vulmap** - web 漏洞扫描和验证工具
- **SpringBoot-Scan** - SpringBoot 渗透框架

### 内网渗透
- **Cobalt Strike** - 商业化渗透测试平台
- **Empire** - PowerShell 后渗透框架
- **BloodHound** - Active Directory 关系分析

## 📚 学习资源

### 在线资源
- **HackTricks** - 渗透测试技巧百科
- **PayloadsAllTheThings** - 各类 Payload 大全
- **SecLists** - 安全测试字典集合

### 靶场环境
- **DVWA** - Web 应用安全靶场
- **VulnHub** - 漏洞练习平台
- **HackTheBox** - 在线渗透测试平台

## ⚠️ 重要提醒

1. **合法使用** - 仅在授权环境中使用这些工具
2. **学习目的** - 主要用于安全研究和技能提升
3. **责任自负** - 使用者需承担相应法律责任
4. **及时更新** - 定期关注工具更新和安全公告

## 🤝 贡献说明

本索引基于开源项目整理，如需贡献或更新：
1. 访问原始项目提交 Issue 或 PR
2. 关注项目动态获取最新工具信息
3. 分享使用经验和最佳实践

## 📞 相关链接

- **原始项目**: [All-Defense-Tool](https://github.com/guchangan1/All-Defense-Tool/)
- **本地文档**: [安全工具使用指南](../安全工具/)
- **系统管理**: [系统管理命令](../常用命令/)

---

**最后更新**: 2025-07-28  
**工具总数**: 500+ 个安全工具  
**分类数量**: 50+ 个细分类别
