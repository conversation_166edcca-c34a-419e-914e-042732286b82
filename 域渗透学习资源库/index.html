<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏰 域渗透学习资源库</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .file-link {
            display: block;
            padding: 8px 15px;
            margin: 5px 0;
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
            text-decoration: none;
            color: #2c3e50;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .file-link:hover {
            background-color: #d5dbdb;
            border-left-color: #e74c3c;
            transform: translateX(5px);
        }
        .description {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-left: 10px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 10px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏰 域渗透学习资源库</h1>
        
        <div class="success">
            <strong>📚 资源库概述</strong><br>
            全面的Active Directory渗透测试学习资源集合，整合了多个权威来源的域渗透技术。
        </div>

        <h2>🗂️ 核心文档目录</h2>
        
        <h3>🎯 权威手册精华</h3>
        <a href="0range-x域渗透一条龙手册精华.md" class="file-link">
            📖 0range-x域渗透一条龙手册精华
            <span class="description">- 基于0range-x权威手册的技术精华整理</span>
        </a>
        <a href="0range-x内容整理完成报告.md" class="file-link">
            📋 0range-x内容整理完成报告
            <span class="description">- 整理工作总结报告</span>
        </a>

        <h3>📖 01-基础知识</h3>
        <a href="01-基础知识/Kerberos认证机制.md" class="file-link">
            🔐 Kerberos认证机制详解
            <span class="description">- 深入理解Kerberos协议和安全机制</span>
        </a>

        <h3>🔍 02-信息收集</h3>
        <a href="02-信息收集/域内信息收集完全指南.md" class="file-link">
            🕵️ 域内信息收集完全指南
            <span class="description">- 系统化的AD环境信息收集方法</span>
        </a>

        <h3>⬆️ 04-权限提升</h3>
        <a href="04-权限提升/Token窃取技术.md" class="file-link">
            🎭 Token窃取技术
            <span class="description">- Windows Token机制和窃取技术详解</span>
        </a>

        <h3>↔️ 05-横向移动</h3>
        <a href="05-横向移动/内网渗透常用技术详解.md" class="file-link">
            🌐 内网渗透常用技术详解
            <span class="description">- 内网渗透中最常用的技术和工具</span>
        </a>

        <h3>👑 06-域控获取</h3>
        <a href="06-域控获取/SPN扫描与Kerberoasting.md" class="file-link">
            🎯 SPN扫描与Kerberoasting
            <span class="description">- 完整的Kerberoasting攻击技术</span>
        </a>
        <a href="06-域控获取/委派攻击详解.md" class="file-link">
            🔄 委派攻击详解
            <span class="description">- 无约束/约束/RBCD委派攻击</span>
        </a>
        <a href="06-域控获取/Kerberos票据制作详解.md" class="file-link">
            🎫 Kerberos票据制作详解
            <span class="description">- 深入理解票据结构和制作方法</span>
        </a>
        <a href="06-域控获取/Zerologon-CVE-2020-1472.md" class="file-link">
            💥 Zerologon-CVE-2020-1472
            <span class="description">- Zerologon漏洞完整利用指南</span>
        </a>

        <h3>🛠️ 09-工具与脚本</h3>
        <a href="09-工具与脚本/Mimikatz使用大全.md" class="file-link">
            🔑 Mimikatz使用大全
            <span class="description">- Windows凭证提取的瑞士军刀</span>
        </a>
        <a href="09-工具与脚本/Fscan内网扫描工具详解.md" class="file-link">
            🔍 Fscan内网扫描工具详解
            <span class="description">- 一款内网综合扫描工具详解</span>
        </a>

        <h3>🎯 10-实战案例</h3>
        <a href="10-实战案例/完整域渗透案例.md" class="file-link">
            📊 完整域渗透案例
            <span class="description">- 从外网到域控的完整攻击链分析</span>
        </a>

        <h3>📋 快速导航</h3>
        <a href="快速索引.md" class="file-link">
            🚀 快速索引
            <span class="description">- 按技术类型和攻击阶段的快速查找指南</span>
        </a>

        <h2>🎓 学习路径推荐</h2>
        
        <div class="grid">
            <div class="card">
                <h4>🔰 初学者路径 (1-2个月)</h4>
                <div class="code">
第1周: 基础知识
├── Kerberos认证机制详解
├── Token基础概念
└── 域内信息收集

第2-3周: 工具使用
├── Fscan内网扫描
├── Mimikatz基础使用
└── 信息收集实践

第4-8周: 实践练习
├── 基础攻击练习
└── 简单案例分析
                </div>
            </div>
            
            <div class="card">
                <h4>🚀 进阶路径 (2-3个月)</h4>
                <div class="code">
第1-2周: 高级认证攻击
├── Kerberoasting深入
├── 委派攻击技术
└── 票据制作详解

第3-6周: 高级攻击
├── Zerologon漏洞利用
├── 高级横向移动
└── 综合攻击链

第7-8周: 实战演练
├── 完整渗透案例
└── 防护绕过研究
                </div>
            </div>
        </div>

        <h2>🔧 快速命令参考</h2>
        
        <h3>信息收集</h3>
        <div class="code">
# 域基本信息
Get-ADDomain
nltest /dclist:domain.com

# 用户枚举
Get-ADUser -Filter * -Properties *
net user /domain

# SPN发现
setspn -Q */*
        </div>

        <h3>内网扫描</h3>
        <div class="code">
# Fscan快速扫描
fscan -h 192.168.1.1/24

# 端口扫描
fscan -h 192.168.1.1/24 -p 22,80,135,139,443,445,1433,3306,3389

# 弱口令检测
fscan -h 192.168.1.1/24 -user admin -pwd "123456,password,admin"
        </div>

        <h3>攻击命令</h3>
        <div class="code">
# Kerberoasting
.\Rubeus.exe kerberoast /format:hashcat

# Token窃取
mimikatz # privilege::debug
mimikatz # sekurlsa::logonpasswords

# 票据注入
mimikatz # kerberos::ptt ticket.kirbi
        </div>

        <div class="warning">
            <strong>⚠️ 重要声明</strong><br>
            本资源库内容仅供安全研究、学习和授权的渗透测试使用。严禁用于未授权的网络攻击、恶意破坏活动或任何违法犯罪行为。使用者需严格遵守当地法律法规，确保获得明确授权后方可进行相关测试。
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p><strong>最后更新</strong>: 2025-07-28 | <strong>版本</strong>: v2.1 (0range-x增强版)</p>
            <p>🎯 <strong>学习目标</strong>: 通过系统学习本资源库，掌握完整的域渗透技术体系！</p>
        </div>
    </div>

    <script>
        // 检查链接是否有效
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('.file-link');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    // 如果是相对路径，尝试打开文件
                    if (!href.startsWith('http')) {
                        // 在新窗口中打开，避免导航问题
                        e.preventDefault();
                        window.open(href, '_blank');
                    }
                });
            });
        });
    </script>
</body>
</html>
