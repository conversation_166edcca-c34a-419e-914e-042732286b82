# 🔍 Fscan内网扫描工具详解

> **一款内网综合扫描工具，方便一键自动化、全方位漏洞扫描**

## 📋 目录

- [工具概述](#工具概述)
- [安装和配置](#安装和配置)
- [基础使用方法](#基础使用方法)
- [高级功能详解](#高级功能详解)
- [实战案例](#实战案例)
- [输出结果分析](#输出结果分析)
- [常见问题解决](#常见问题解决)

## 🔍 工具概述

### 什么是Fscan？
Fscan是由shadow1ng开发的一款**内网综合扫描工具**，专门用于内网渗透测试中的信息收集和漏洞发现。它集成了多种扫描功能，可以一键完成主机发现、端口扫描、服务识别、漏洞检测等任务。

### 核心功能
```
主要功能模块:
├── 主机存活探测 (ICMP/TCP/UDP)
├── 端口扫描 (TCP/UDP端口)
├── 服务识别 (Banner抓取)
├── 弱口令爆破 (SSH/RDP/SMB/MySQL等)
├── 漏洞检测 (MS17-010/CVE等)
├── Web应用扫描 (目录扫描/敏感文件)
├── 数据库扫描 (MySQL/MSSQL/Oracle等)
└── 结果输出 (多种格式)
```

### 工具特点
```
优势特点:
✅ 一键自动化扫描
✅ 多线程高效扫描
✅ 支持多种协议
✅ 内置常见漏洞检测
✅ 支持代理扫描
✅ 结果输出多样化
✅ 跨平台支持
✅ 开源免费
```

### 适用场景
```
使用场景:
🎯 内网渗透测试
🎯 安全评估
🎯 资产发现
🎯 漏洞扫描
🎯 弱口令检测
🎯 服务识别
🎯 网络拓扑发现
```

## 🚀 安装和配置

### 1. 下载安装

#### 官方下载
```bash
# GitHub官方仓库
https://github.com/shadow1ng/fscan

# 下载编译好的版本
wget https://github.com/shadow1ng/fscan/releases/download/1.8.4/fscan.exe

# Linux版本
wget https://github.com/shadow1ng/fscan/releases/download/1.8.4/fscan_linux_amd64

# macOS版本
wget https://github.com/shadow1ng/fscan/releases/download/1.8.4/fscan_darwin_amd64
```

#### 源码编译
```bash
# 克隆源码
git clone https://github.com/shadow1ng/fscan.git
cd fscan

# 编译 (需要Go环境)
go build -ldflags="-s -w" -trimpath main.go
mv main fscan

# 或使用Makefile
make build
```

### 2. 环境配置

#### Windows环境
```cmd
# 下载到指定目录
mkdir C:\Tools\fscan
cd C:\Tools\fscan
# 下载fscan.exe

# 添加到环境变量 (可选)
set PATH=%PATH%;C:\Tools\fscan

# 验证安装
fscan.exe -h
```

#### Linux环境
```bash
# 下载并设置权限
wget https://github.com/shadow1ng/fscan/releases/download/1.8.4/fscan_linux_amd64
chmod +x fscan_linux_amd64
mv fscan_linux_amd64 /usr/local/bin/fscan

# 验证安装
fscan -h
```

### 3. 依赖要求
```
系统要求:
✅ Windows 7+ / Linux / macOS
✅ 无需额外依赖
✅ 单文件运行
✅ 内存: 建议512MB+
✅ 网络: 需要目标网络连通性
```

## 📖 基础使用方法

### 1. 命令行参数详解

#### 基本语法
```bash
fscan [选项] [目标]

# 基本格式
fscan -h <target>
```

#### 核心参数
```bash
# 目标指定
-h string     目标IP: ************* | *************-255 | *************,*************
-hf string    从文件中读取目标
-u string     指定URL进行扫描

# 端口设置
-p string     设置端口: 22 | 1-65535 | 22,80,3389
-pa string    添加端口: -pa 3389 (在默认端口基础上增加)
-pn string    跳过端口扫描

# 扫描控制
-t int        扫描线程 (默认600)
-i string     网卡名称
-time int     端口扫描超时时间 (默认3s)
-np           跳过存活探测
-no           跳过端口扫描

# 爆破设置
-user string  指定爆破用户名
-pwd string   指定爆破密码
-pwdf string  指定密码字典文件
-userf string 指定用户名字典文件

# 输出控制
-o string     扫描结果输出到文件
-nobr         跳过爆破
-nopoc        跳过POC扫描
-silent       静默扫描
-debug        调试模式

# 代理设置
-proxy string 设置代理: -proxy http://127.0.0.1:8080
-socks5 string 设置socks5代理
```

### 2. 基础扫描示例

#### 单IP扫描
```bash
# 扫描单个IP
fscan -h ***********00

# 扫描单个IP的指定端口
fscan -h ***********00 -p 22,80,443,3389

# 扫描单个IP并输出到文件
fscan -h ***********00 -o result.txt
```

#### 网段扫描
```bash
# C段扫描
fscan -h ***********/24

# B段扫描 (谨慎使用)
fscan -h ***********/16

# 指定范围扫描
fscan -h ***********-************

# 多个IP扫描
fscan -h ***********,***********0,************
```

#### 从文件读取目标
```bash
# 创建目标文件
echo "***********/24" > targets.txt
echo "********-*********" >> targets.txt

# 从文件扫描
fscan -hf targets.txt
```

### 3. 常用扫描组合

#### 快速扫描
```bash
# 快速主机发现
fscan -h ***********/24 -np -no

# 快速端口扫描
fscan -h ***********/24 -p 22,80,135,139,443,445,1433,3306,3389 -t 1000

# 跳过爆破的快速扫描
fscan -h ***********/24 -nobr
```

#### 深度扫描
```bash
# 全端口扫描
fscan -h ***********00 -p 1-65535

# 包含爆破的深度扫描
fscan -h ***********/24 -pwdf passwords.txt

# 详细输出的深度扫描
fscan -h ***********/24 -debug -o detailed_result.txt
```

## 🔧 高级功能详解

### 1. 弱口令爆破

#### 支持的服务
```
爆破模块:
├── SSH (22)
├── RDP (3389)  
├── SMB (445)
├── MySQL (3306)
├── MSSQL (1433)
├── Oracle (1521)
├── PostgreSQL (5432)
├── MongoDB (27017)
├── Redis (6379)
├── FTP (21)
├── Telnet (23)
└── SNMP (161)
```

#### 自定义字典
```bash
# 创建用户名字典
cat > users.txt << EOF
administrator
admin
root
sa
postgres
oracle
test
guest
EOF

# 创建密码字典
cat > passwords.txt << EOF
123456
password
admin
root
123123
qwerty
test
guest
EOF

# 使用自定义字典
fscan -h ***********/24 -userf users.txt -pwdf passwords.txt
```

#### 指定服务爆破
```bash
# 只爆破SSH
fscan -h ***********/24 -p 22 -user root -pwd "123456,password,admin"

# 只爆破RDP
fscan -h ***********/24 -p 3389 -user administrator -pwdf passwords.txt

# 只爆破数据库
fscan -h ***********/24 -p 3306,1433,5432 -user "root,sa,postgres" -pwd "123456,password"
```

### 2. 漏洞检测

#### 内置POC
```
支持的漏洞:
├── MS17-010 (永恒之蓝)
├── SMBGhost (CVE-2020-0796)
├── 各种Web漏洞
├── 数据库漏洞
├── 中间件漏洞
└── 其他常见漏洞
```

#### 漏洞扫描示例
```bash
# 扫描MS17-010
fscan -h ***********/24 -p 445

# 跳过POC扫描 (仅端口扫描)
fscan -h ***********/24 -nopoc

# 只进行漏洞扫描 (跳过爆破)
fscan -h ***********/24 -nobr
```

### 3. Web应用扫描

#### Web扫描功能
```
Web模块:
├── 目录扫描
├── 敏感文件检测
├── 常见漏洞检测
├── 中间件识别
├── CMS识别
└── 备份文件检测
```

#### Web扫描示例
```bash
# 扫描Web应用
fscan -u http://***********00

# 扫描多个Web应用
fscan -u http://***********00,https://***********01

# 从文件读取URL
echo "http://***********00" > urls.txt
echo "https://***********01:8080" >> urls.txt
fscan -uf urls.txt
```

### 4. 代理扫描

#### HTTP代理
```bash
# 使用HTTP代理
fscan -h ***********/24 -proxy http://127.0.0.1:8080

# 使用认证代理
fscan -h ***********/24 -proxy *******************************
```

#### SOCKS5代理
```bash
# 使用SOCKS5代理
fscan -h ***********/24 -socks5 127.0.0.1:1080

# 通过代理扫描内网
fscan -h ********/24 -socks5 127.0.0.1:1080
```

## 🎯 实战案例

### 案例1: 内网资产发现

#### 场景描述
```
目标: 发现内网中的所有活跃主机和开放服务
网段: ***********/24
要求: 快速扫描，不进行爆破
```

#### 扫描命令
```bash
# 第一步: 主机发现
fscan -h ***********/24 -np -no -o hosts_discovery.txt

# 第二步: 端口扫描
fscan -h ***********/24 -p 21,22,23,25,53,80,110,135,139,143,443,445,993,995,1433,3306,3389,5432,6379,27017 -nobr -o port_scan.txt

# 第三步: 服务识别
fscan -h ***********/24 -nobr -nopoc -o service_scan.txt
```

#### 结果分析
```bash
# 查看发现的主机
grep "alive" hosts_discovery.txt

# 查看开放的端口
grep "open" port_scan.txt

# 查看识别的服务
grep -E "(ssh|http|smb|mysql|mssql)" service_scan.txt
```

### 案例2: 弱口令检测

#### 场景描述
```
目标: 检测内网中的弱口令
重点: SSH、RDP、数据库服务
策略: 使用常见弱口令字典
```

#### 准备字典
```bash
# 创建常见用户名
cat > common_users.txt << EOF
administrator
admin
root
sa
postgres
oracle
mysql
test
guest
user
EOF

# 创建常见密码
cat > common_passwords.txt << EOF
123456
password
admin
root
123123
qwerty
test
guest
123
1234
12345
password123
admin123
root123
EOF
```

#### 扫描命令
```bash
# 弱口令扫描
fscan -h ***********/24 -userf common_users.txt -pwdf common_passwords.txt -o weak_passwords.txt

# 只扫描特定服务
fscan -h ***********/24 -p 22,3389,3306,1433,5432 -userf common_users.txt -pwdf common_passwords.txt -o db_weak_passwords.txt
```

### 案例3: 漏洞扫描

#### 场景描述
```
目标: 检测内网中的常见漏洞
重点: MS17-010、SMBGhost等
要求: 全面扫描但跳过爆破
```

#### 扫描命令
```bash
# 漏洞扫描
fscan -h ***********/24 -nobr -o vulnerability_scan.txt

# 重点扫描SMB漏洞
fscan -h ***********/24 -p 445 -nobr -o smb_vulnerabilities.txt

# 详细模式扫描
fscan -h ***********/24 -nobr -debug -o detailed_vulnerability_scan.txt
```

### 案例4: 通过代理扫描

#### 场景描述
```
目标: 通过已获得的Web Shell扫描内网
代理: 通过reGeorg建立的SOCKS5代理
内网: 10.0.0.0/24
```

#### 建立代理
```bash
# 1. 上传reGeorg的tunnel.jsp到Web服务器
# 2. 启动代理客户端
python reGeorgSocksProxy.py -p 1080 -u http://target.com/tunnel.jsp
```

#### 通过代理扫描
```bash
# 通过SOCKS5代理扫描内网
fscan -h ********/24 -socks5 127.0.0.1:1080 -o internal_scan.txt

# 扫描特定服务
fscan -h ********/24 -p 22,80,135,139,445,1433,3306,3389 -socks5 127.0.0.1:1080 -nobr -o internal_services.txt
```

## 📊 输出结果分析

### 1. 输出格式

#### 标准输出格式
```
[*] ***********00:22 open
[*] ***********00:80 open
[*] ***********00:135 open
[*] ***********00:139 open
[*] ***********00:445 open
[+] ***********00:22 ssh
[+] ***********00:80 http title: Apache2 Ubuntu Default Page
[+] ***********00:135 rpc
[+] ***********00:139 netbios
[+] ***********00:445 microsoft-ds
[!] ***********00:445 MS17-010
[√] ***********00:22 ssh root:123456
```

#### 输出符号含义
```
符号说明:
[*] - 端口开放
[+] - 服务识别成功
[!] - 发现漏洞
[√] - 爆破成功
[-] - 扫描失败或无结果
```

### 2. 结果文件分析

#### 提取有用信息
```bash
# 提取所有开放端口
grep "\[*\]" result.txt | grep "open"

# 提取所有Web服务
grep "http" result.txt

# 提取所有数据库服务
grep -E "(mysql|mssql|oracle|postgres|mongodb)" result.txt

# 提取所有爆破成功的结果
grep "\[√\]" result.txt

# 提取所有漏洞
grep "\[!\]" result.txt
```

#### 统计分析
```bash
# 统计存活主机数量
grep "alive" result.txt | wc -l

# 统计开放端口数量
grep "open" result.txt | wc -l

# 统计发现的漏洞数量
grep "\[!\]" result.txt | wc -l

# 统计爆破成功数量
grep "\[√\]" result.txt | wc -l
```

### 3. 结果可视化

#### 生成报告脚本
```python
#!/usr/bin/env python3
# fscan_report.py - Fscan结果分析脚本

import re
import sys
from collections import defaultdict

def parse_fscan_result(filename):
    results = {
        'hosts': set(),
        'open_ports': defaultdict(list),
        'services': defaultdict(list),
        'vulnerabilities': defaultdict(list),
        'credentials': defaultdict(list)
    }
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            # 解析主机存活
            if 'alive' in line:
                host = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
                if host:
                    results['hosts'].add(host.group(1))
            
            # 解析开放端口
            if '[*]' in line and 'open' in line:
                match = re.search(r'(\d+\.\d+\.\d+\.\d+):(\d+)', line)
                if match:
                    host, port = match.groups()
                    results['open_ports'][host].append(port)
            
            # 解析服务
            if '[+]' in line:
                match = re.search(r'(\d+\.\d+\.\d+\.\d+):(\d+)\s+(\w+)', line)
                if match:
                    host, port, service = match.groups()
                    results['services'][host].append(f"{port}/{service}")
            
            # 解析漏洞
            if '[!]' in line:
                match = re.search(r'(\d+\.\d+\.\d+\.\d+):(\d+)\s+(.+)', line)
                if match:
                    host, port, vuln = match.groups()
                    results['vulnerabilities'][host].append(f"{port}/{vuln}")
            
            # 解析凭据
            if '[√]' in line:
                match = re.search(r'(\d+\.\d+\.\d+\.\d+):(\d+)\s+(\w+)\s+(.+)', line)
                if match:
                    host, port, service, cred = match.groups()
                    results['credentials'][host].append(f"{port}/{service}: {cred}")
    
    return results

def generate_report(results):
    print("=" * 60)
    print("FSCAN 扫描结果报告")
    print("=" * 60)
    
    print(f"\n📊 扫描统计:")
    print(f"存活主机: {len(results['hosts'])}")
    print(f"开放端口: {sum(len(ports) for ports in results['open_ports'].values())}")
    print(f"识别服务: {sum(len(services) for services in results['services'].values())}")
    print(f"发现漏洞: {sum(len(vulns) for vulns in results['vulnerabilities'].values())}")
    print(f"获得凭据: {sum(len(creds) for creds in results['credentials'].values())}")
    
    print(f"\n🖥️  存活主机列表:")
    for host in sorted(results['hosts']):
        print(f"  {host}")
    
    print(f"\n🔓 获得的凭据:")
    for host, creds in results['credentials'].items():
        print(f"  {host}:")
        for cred in creds:
            print(f"    {cred}")
    
    print(f"\n⚠️  发现的漏洞:")
    for host, vulns in results['vulnerabilities'].items():
        print(f"  {host}:")
        for vuln in vulns:
            print(f"    {vuln}")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python3 fscan_report.py <result_file>")
        sys.exit(1)
    
    results = parse_fscan_result(sys.argv[1])
    generate_report(results)
```

#### 使用报告脚本
```bash
# 生成报告
python3 fscan_report.py result.txt

# 保存报告到文件
python3 fscan_report.py result.txt > scan_report.txt
```

## ❓ 常见问题解决

### 1. 扫描速度问题

#### 问题: 扫描速度太慢
```bash
# 解决方案1: 增加线程数
fscan -h ***********/24 -t 1000

# 解决方案2: 减少端口范围
fscan -h ***********/24 -p 22,80,135,139,443,445,1433,3306,3389

# 解决方案3: 跳过某些模块
fscan -h ***********/24 -nobr -nopoc
```

#### 问题: 扫描超时
```bash
# 解决方案: 增加超时时间
fscan -h ***********/24 -time 10
```

### 2. 网络连接问题

#### 问题: 无法连接目标
```bash
# 检查网络连通性
ping ***********

# 检查防火墙设置
# Windows
netsh advfirewall show allprofiles

# Linux  
iptables -L
```

#### 问题: 代理连接失败
```bash
# 测试代理连接
curl --proxy http://127.0.0.1:8080 http://www.baidu.com
curl --socks5 127.0.0.1:1080 http://www.baidu.com

# 检查代理设置
fscan -h *********** -proxy http://127.0.0.1:8080 -debug
```

### 3. 权限问题

#### 问题: 权限不足
```bash
# Linux下需要root权限进行某些扫描
sudo ./fscan -h ***********/24

# Windows下以管理员身份运行
# 右键 -> 以管理员身份运行
```

### 4. 输出问题

#### 问题: 中文乱码
```bash
# Windows下设置编码
chcp 65001
fscan -h ***********/24 -o result.txt

# Linux下设置编码
export LANG=zh_CN.UTF-8
./fscan -h ***********/24 -o result.txt
```

## 📚 参考资料

### 官方资源
- [Fscan GitHub仓库](https://github.com/shadow1ng/fscan)
- [Fscan使用文档](https://github.com/shadow1ng/fscan/wiki)

### 相关工具
- [Nmap](https://nmap.org/) - 网络扫描工具
- [Masscan](https://github.com/robertdavidgraham/masscan) - 高速端口扫描
- [Zmap](https://zmap.io/) - 互联网扫描工具

---

> 🔒 **安全提醒**: Fscan仅供安全研究和授权测试使用，请在合法授权的环境中使用！
