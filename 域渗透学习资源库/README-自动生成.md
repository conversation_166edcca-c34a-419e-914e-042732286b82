# 🏰 域渗透学习资源库

> **全面的Active Directory渗透测试学习资源集合**

## 📚 资源库概述

本资源库整合了来自多个权威来源的域渗透技术，包括：
- [chriskaliX/AD-Pentest-Notes](https://github.com/chriskaliX/AD-Pentest-Notes)
- [blaCCkHatHacEEkr/PENTESTING-BIBLE](https://github.com/blaCCkHatHacEEkr/PENTESTING-BIBLE)
- [0range-x域渗透一条龙手册](https://0range-x.github.io/2022/01/26/Domain-penetration_one-stop/)
- MITRE ATT&CK框架
- 各大安全厂商研究报告

## 🗂️ 目录结构

### 🎯 权威手册精华
- [0range-x域渗透一条龙手册精华](0range-x域渗透一条龙手册精华.md) - 基于0range-x权威手册的技术精华整理
- [0range-x内容整理完成报告](0range-x内容整理完成报告.md) - 整理工作总结报告

### 📖 基础知识
- [Kerberos认证机制](01-基础知识/Kerberos认证机制.md) - 深入理解Kerberos协议和安全机制

### 🔍 信息收集
- [域内信息收集完全指南](02-信息收集/域内信息收集完全指南.md) - 系统化的AD环境信息收集方法

### ⬆️ 权限提升
- [Token窃取技术](04-权限提升/Token窃取技术.md) - Windows Token机制和窃取技术详解

### ↔️ 横向移动
- [内网渗透常用技术详解](05-横向移动/内网渗透常用技术详解.md) - 内网渗透中最常用的技术和工具

### 👑 域控获取
- [SPN扫描与Kerberoasting](06-域控获取/SPN扫描与Kerberoasting.md) - 完整的Kerberoasting攻击技术
- [委派攻击详解](06-域控获取/委派攻击详解.md) - 无约束/约束/RBCD委派攻击
- [Kerberos票据制作详解](06-域控获取/Kerberos票据制作详解.md) - 深入理解票据结构和制作方法
- [Zerologon-CVE-2020-1472](06-域控获取/Zerologon-CVE-2020-1472.md) - Zerologon漏洞完整利用指南

### 🛠️ 工具与脚本
- [Mimikatz使用大全](09-工具与脚本/Mimikatz使用大全.md) - Windows凭证提取的瑞士军刀
- [Fscan内网扫描工具详解](09-工具与脚本/Fscan内网扫描工具详解.md) - 一款内网综合扫描工具详解

### 🎯 实战案例
- [完整域渗透案例](10-实战案例/完整域渗透案例.md) - 从外网到域控的完整攻击链分析

### 📋 快速导航
- [快速索引](快速索引.md) - 按技术类型和攻击阶段的快速查找指南


## 🔒 安全提醒

⚠️ **重要声明**: 本资源库内容仅供安全研究、学习和授权的渗透测试使用。严禁用于未授权的网络攻击、恶意破坏活动或任何违法犯罪行为。

---

> 🎯 **学习目标**: 通过系统学习本资源库，掌握完整的域渗透技术体系！

**最后更新**: 2025-07-28  
**版本**: v2.1 (0range-x增强版)
