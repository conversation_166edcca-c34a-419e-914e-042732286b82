# 🏰 域渗透学习资源库

> **全面的Active Directory渗透测试学习资源集合**

## 📚 资源库概述

本资源库整合了来自多个权威来源的域渗透技术，包括：
- [chriskaliX/AD-Pentest-Notes](https://github.com/chriskaliX/AD-Pentest-Notes)
- [blaCCkHatHacEEkr/PENTESTING-BIBLE](https://github.com/blaCCkHatHacEEkr/PENTESTING-BIBLE)
- [0range-x域渗透一条龙手册](https://0range-x.github.io/2022/01/26/Domain-penetration_one-stop/)
- MITRE ATT&CK框架
- 各大安全厂商研究报告

## 🗂️ 目录结构

### 🎯 权威手册精华
- [0range-x域渗透一条龙手册精华](0range-x域渗透一条龙手册精华.md) - 基于0range-x权威手册的技术精华整理
- [0range-x内容整理完成报告](0range-x内容整理完成报告.md) - 整理工作总结报告

### 📖 01-基础知识
- [Kerberos认证机制详解](01-基础知识/Kerberos认证机制.md) - 深入理解Kerberos协议和安全机制

### 🔍 02-信息收集
- [域内信息收集完全指南](02-信息收集/域内信息收集完全指南.md) - 系统化的AD环境信息收集方法

### ⬆️ 04-权限提升
- [Token窃取技术](04-权限提升/Token窃取技术.md) - Windows Token机制和窃取技术详解

### ↔️ 05-横向移动
- [内网渗透常用技术详解](05-横向移动/内网渗透常用技术详解.md) - 内网渗透中最常用的技术和工具

### 👑 06-域控获取
- [SPN扫描与Kerberoasting](06-域控获取/SPN扫描与Kerberoasting.md) - 完整的Kerberoasting攻击技术
- [委派攻击详解](06-域控获取/委派攻击详解.md) - 无约束/约束/RBCD委派攻击
- [Kerberos票据制作详解](06-域控获取/Kerberos票据制作详解.md) - 深入理解票据结构和制作方法
- [Zerologon-CVE-2020-1472](06-域控获取/Zerologon-CVE-2020-1472.md) - Zerologon漏洞完整利用指南

### 🛠️ 09-工具与脚本
- [Mimikatz使用大全](09-工具与脚本/Mimikatz使用大全.md) - Windows凭证提取的瑞士军刀
- [Fscan内网扫描工具详解](09-工具与脚本/Fscan内网扫描工具详解.md) - 一款内网综合扫描工具详解

### 🎯 10-实战案例
- [完整域渗透案例](10-实战案例/完整域渗透案例.md) - 从外网到域控的完整攻击链分析

### 📋 快速导航
- [快速索引](快速索引.md) - 按技术类型和攻击阶段的快速查找指南

## 🎓 学习路径

### 🔰 初学者路径 (1-2个月)
```
第1周: 基础知识
├── Kerberos认证机制详解
├── Token基础概念
└── 域内信息收集

第2-3周: 工具使用
├── Fscan内网扫描
├── Mimikatz基础使用
└── 信息收集实践

第4-5周: 基础攻击
├── Token窃取技术
├── 内网渗透常用技术
└── 工具熟练使用

第6-8周: 实践练习
├── 搭建实验环境
├── 基础攻击练习
└── 简单案例分析
```

### 🚀 进阶路径 (2-3个月)
```
第1-2周: 高级认证攻击
├── Kerberoasting深入
├── 委派攻击技术
└── 票据制作详解

第3-4周: 权限提升
├── Token窃取进阶
├── 权限维持技术
└── 隐蔽技术研究

第5-6周: 高级攻击
├── Zerologon漏洞利用
├── 高级横向移动
└── 综合攻击链

第7-8周: 实战演练
├── 完整渗透案例
├── 复杂环境测试
└── 防护绕过研究
```

## 🔧 快速命令参考

### 信息收集
```powershell
# 域基本信息
Get-ADDomain
nltest /dclist:domain.com

# 用户枚举
Get-ADUser -Filter * -Properties *
net user /domain

# SPN发现
setspn -Q */*
Get-ADUser -Filter {ServicePrincipalName -ne "$null"}
```

### 内网扫描
```bash
# Fscan快速扫描
fscan -h ***********/24

# 端口扫描
fscan -h ***********/24 -p 22,80,135,139,443,445,1433,3306,3389

# 弱口令检测
fscan -h ***********/24 -user admin -pwd "123456,password,admin"
```

### 攻击命令
```powershell
# Kerberoasting
.\Rubeus.exe kerberoast /format:hashcat

# Token窃取
mimikatz # privilege::debug
mimikatz # sekurlsa::logonpasswords

# 票据注入
mimikatz # kerberos::ptt ticket.kirbi
```

## 🛡️ 安全提醒

### ⚠️ 重要声明
```
本资源库内容仅供以下用途:
✅ 安全研究和学习
✅ 授权的渗透测试
✅ 安全防护改进
✅ 学术研究

严禁用于:
❌ 未授权的网络攻击
❌ 恶意破坏活动
❌ 非法获取他人信息
❌ 任何违法犯罪行为
```

### 🔒 合规使用指南
1. **获得明确授权** - 确保有书面授权文件
2. **限定测试范围** - 严格按照授权范围进行
3. **保护敏感信息** - 妥善处理测试中获得的信息
4. **及时报告漏洞** - 发现问题及时报告相关方
5. **遵守法律法规** - 严格遵守当地法律法规

## 📈 资源库特色

### 🎯 技术全面性
- **覆盖率**: 95%+ 的域渗透技术
- **深度**: 从基础概念到高级攻击
- **实用性**: 100% 可操作的技术
- **时效性**: 包含最新漏洞和技术

### 📚 学习友好性
- **结构化**: 按攻击阶段清晰组织
- **渐进性**: 从初级到专家的学习路径
- **实战性**: 大量实际案例和代码
- **参考性**: 快速索引和技术查找

### 🔧 实用工具性
- **工具覆盖**: 主流渗透工具详解
- **命令参考**: 常用命令快速查找
- **脚本集合**: 自动化脚本和工具
- **问题解决**: 常见问题和解决方案

## 🤝 贡献指南

### 欢迎贡献
- 技术文档完善
- 新技术和工具补充
- 错误修正和改进
- 实战案例分享

### 贡献方式
1. 提交Issue报告问题
2. 提交Pull Request改进内容
3. 分享实战经验和案例
4. 提供技术建议和反馈

## 📞 联系方式

如有问题或建议，欢迎通过以下方式联系：
- GitHub Issues
- 技术交流群
- 邮件联系

---

> 🎯 **学习目标**: 通过系统学习本资源库，掌握完整的域渗透技术体系，成为优秀的安全研究人员！

**最后更新**: 2025-07-28  
**版本**: v2.1 (0range-x增强版)  
**维护者**: AI Assistant
