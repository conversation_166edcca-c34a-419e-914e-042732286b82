# 🔍 域内信息收集完全指南

> **系统化的Active Directory环境信息收集方法和技术**

## 📋 目录

- [信息收集概述](#信息收集概述)
- [域基础信息](#域基础信息)
- [用户和组信息](#用户和组信息)
- [计算机和服务](#计算机和服务)
- [权限和信任关系](#权限和信任关系)
- [网络和服务发现](#网络和服务发现)
- [高级信息收集](#高级信息收集)
- [自动化工具](#自动化工具)

## 🎯 信息收集概述

### 信息收集的重要性
在域渗透过程中，信息收集是最关键的阶段之一。充分的信息收集可以：
- 了解目标环境的整体架构
- 发现潜在的攻击路径
- 识别高价值目标
- 制定针对性的攻击策略

### 信息收集分类
```
按权限分类:
├── 无凭证信息收集
├── 域用户权限信息收集
├── 本地管理员权限信息收集
└── 域管理员权限信息收集

按技术分类:
├── 被动信息收集
├── 主动信息收集
├── 网络层信息收集
└── 应用层信息收集
```

### MITRE ATT&CK映射
| 技术 | ATT&CK ID | 描述 |
|------|-----------|------|
| 域信任发现 | T1482 | 发现域信任关系 |
| 账户发现 | T1087 | 枚举用户账户 |
| 权限组发现 | T1069 | 发现权限组成员 |
| 网络共享发现 | T1135 | 发现网络共享 |
| 远程系统发现 | T1018 | 发现远程系统 |

## 🏰 域基础信息

### 1. 域控制器信息
```powershell
# 查找域控制器
nltest /dclist:domain.com
nslookup -type=SRV _ldap._tcp.dc._msdcs.domain.com

# PowerShell方法
Get-ADDomainController -Filter *
[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().DomainControllers

# 使用ldapsearch (Linux)
ldapsearch -x -h dc.domain.com -s base -b "" "(objectclass=*)" dnsHostName
```

### 2. 域基本信息
```powershell
# 域信息
Get-ADDomain
[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()

# 域功能级别
Get-ADDomain | Select-Object DomainMode

# 域SID
Get-ADDomain | Select-Object DomainSID

# 域策略信息
Get-ADDefaultDomainPasswordPolicy
```

### 3. 林信息
```powershell
# 林信息
Get-ADForest
[System.DirectoryServices.ActiveDirectory.Forest]::GetCurrentForest()

# 林中的域
Get-ADForest | Select-Object -ExpandProperty Domains

# 全局编录服务器
Get-ADForest | Select-Object -ExpandProperty GlobalCatalogs
```

### 4. 域信任关系
```powershell
# 域信任
Get-ADTrust -Filter *
nltest /domain_trusts

# 详细信任信息
Get-ADTrust -Filter * | Select-Object Name,Direction,TrustType

# 使用PowerView
Get-DomainTrust
Get-DomainTrustMapping
```

## 👥 用户和组信息

### 1. 用户枚举
```powershell
# 所有用户
Get-ADUser -Filter * -Properties *
net user /domain

# 活跃用户
Get-ADUser -Filter {Enabled -eq $true} -Properties LastLogonDate

# 特权用户
Get-ADGroupMember "Domain Admins"
Get-ADGroupMember "Enterprise Admins"
Get-ADGroupMember "Schema Admins"

# 服务账户
Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName
```

### 2. 用户属性分析
```powershell
# 密码策略相关
Get-ADUser -Filter * -Properties PasswordNeverExpires,PasswordNotRequired,DoesNotRequirePreAuth

# 登录信息
Get-ADUser -Filter * -Properties LastLogonDate,LogonCount,BadLogonCount

# 账户状态
Get-ADUser -Filter * -Properties AccountExpirationDate,LockedOut,Enabled

# 描述字段 (可能包含密码)
Get-ADUser -Filter * -Properties Description | Where-Object {$_.Description -ne $null}
```

### 3. 组信息收集
```powershell
# 所有组
Get-ADGroup -Filter * -Properties *

# 特权组
$PrivilegedGroups = @(
    "Domain Admins",
    "Enterprise Admins", 
    "Schema Admins",
    "Administrators",
    "Backup Operators",
    "Print Operators",
    "Server Operators",
    "Account Operators"
)

foreach ($Group in $PrivilegedGroups) {
    Write-Host "=== $Group ===" -ForegroundColor Yellow
    Get-ADGroupMember $Group | Select-Object Name,ObjectClass
}

# 嵌套组分析
Get-ADGroup -Filter * | ForEach-Object {
    $members = Get-ADGroupMember $_.Name
    if ($members) {
        Write-Host "Group: $($_.Name)"
        $members | Select-Object Name,ObjectClass
    }
}
```

### 4. 权限委派分析
```powershell
# 查找委派权限
Get-ADUser -Filter * -Properties TrustedForDelegation,TrustedToAuthForDelegation
Get-ADComputer -Filter * -Properties TrustedForDelegation,TrustedToAuthForDelegation

# 约束委派
Get-ADObject -Filter {msDS-AllowedToDelegateTo -ne "$null"} -Properties msDS-AllowedToDelegateTo

# 基于资源的约束委派
Get-ADComputer -Filter * -Properties msDS-AllowedToActOnBehalfOfOtherIdentity
```

## 💻 计算机和服务

### 1. 计算机枚举
```powershell
# 所有计算机
Get-ADComputer -Filter * -Properties *

# 活跃计算机
Get-ADComputer -Filter {Enabled -eq $true} -Properties LastLogonDate

# 操作系统统计
Get-ADComputer -Filter * -Properties OperatingSystem | Group-Object OperatingSystem

# 服务器角色
Get-ADComputer -Filter {OperatingSystem -like "*Server*"} -Properties OperatingSystem,ServicePrincipalName
```

### 2. SPN (Service Principal Name) 发现
```powershell
# 所有SPN
setspn -Q */*

# 用户关联的SPN
Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName | 
    Select-Object Name,ServicePrincipalName

# 计算机关联的SPN
Get-ADComputer -Filter * -Properties ServicePrincipalName | 
    Where-Object {$_.ServicePrincipalName} | 
    Select-Object Name,ServicePrincipalName

# 特定服务类型
setspn -Q HTTP/*
setspn -Q MSSQL/*
setspn -Q CIFS/*
```

### 3. DNS记录枚举
```powershell
# DNS区域传送尝试
nslookup
> server dc.domain.com
> ls -d domain.com

# 常见子域名
$subdomains = @("www","mail","ftp","admin","test","dev","staging","vpn","remote")
foreach ($sub in $subdomains) {
    nslookup "$sub.domain.com"
}

# PowerShell DNS查询
Resolve-DnsName -Name domain.com -Type ALL
```

## 🔐 权限和信任关系

### 1. ACL (访问控制列表) 分析
```powershell
# 使用PowerView分析ACL
Get-DomainObjectAcl -Identity "Domain Admins" -ResolveGUIDs

# 查找有趣的ACE
Find-InterestingDomainAcl -ResolveGUIDs

# 特定对象的权限
Get-DomainObjectAcl -Identity "CN=AdminSDHolder,CN=System,DC=domain,DC=com" -ResolveGUIDs

# 查找可修改的对象
Find-DomainObjectPropertyOutlier
```

### 2. GPO (组策略对象) 分析
```powershell
# 所有GPO
Get-GPO -All

# GPO链接
Get-GPInheritance -Target "OU=Users,DC=domain,DC=com"

# GPO权限
Get-GPPermission -Name "Default Domain Policy" -All

# 查找可修改的GPO
Get-DomainGPO | Get-DomainObjectAcl -ResolveGUIDs | Where-Object {
    ($_.ActiveDirectoryRights -match "WriteProperty|GenericWrite|GenericAll|WriteDacl|WriteOwner") -and 
    ($_.SecurityIdentifier -match "S-1-5-21-.*-[1-9]\d{3,}")
}
```

### 3. OU (组织单位) 结构
```powershell
# OU结构
Get-ADOrganizationalUnit -Filter * | Select-Object Name,DistinguishedName

# OU中的对象
Get-ADObject -SearchBase "OU=Users,DC=domain,DC=com" -Filter *

# OU权限
Get-DomainOU | Get-DomainObjectAcl -ResolveGUIDs
```

## 🌐 网络和服务发现

### 1. 网络扫描
```bash
# Nmap扫描
nmap -sS -O -sV -sC ***********/24

# 常见端口扫描
nmap -p 21,22,23,25,53,80,88,135,139,389,443,445,464,593,636,3268,3269,3389,5985,5986 ***********/24

# SMB扫描
nmap --script smb-enum-shares,smb-enum-users ***********/24
```

### 2. 服务发现
```powershell
# 网络共享
net view /domain
Get-SmbShare

# 打印机
Get-Printer
net view \\printserver

# Web服务
# 使用gobuster或dirb进行目录扫描
gobuster dir -u http://target.com -w /usr/share/wordlists/dirb/common.txt
```

### 3. 网络邻居发现
```powershell
# ARP表
arp -a

# 网络邻居
Get-NetNeighbor

# 路由表
route print
Get-NetRoute

# 网络连接
netstat -an
Get-NetTCPConnection
```

## 🔬 高级信息收集

### 1. LDAP查询
```bash
# 基本LDAP查询
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" "(objectClass=user)"

# 查找特定属性
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" "(objectClass=user)" sAMAccountName mail

# 查找服务账户
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" "(&(objectClass=user)(servicePrincipalName=*))"

# 查找计算机账户
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" "(objectClass=computer)"
```

### 2. Kerberos信息收集
```powershell
# 当前票据
klist

# SPN扫描
.\Rubeus.exe kerberoast /stats

# AS-REP Roasting候选
.\Rubeus.exe asreproast

# 委派账户
.\Rubeus.exe triage
```

### 3. 证书服务发现
```powershell
# ADCS发现
certutil -config - -ping

# 证书模板
certutil -v -template

# 使用Certify
.\Certify.exe find /vulnerable
```

## 🤖 自动化工具

### 1. BloodHound数据收集
```powershell
# SharpHound收集
.\SharpHound.exe -c All -d domain.com

# BloodHound.py (Linux)
bloodhound-python -u username -p password -d domain.com -dc dc.domain.com -c All

# 分析路径
# 在BloodHound中查询:
# - 到域管的最短路径
# - Kerberoastable用户
# - AS-REP Roastable用户
# - 高权限组成员
```

### 2. PowerView脚本
```powershell
# 导入PowerView
Import-Module .\PowerView.ps1

# 域信息收集
Get-Domain
Get-DomainController
Get-DomainUser
Get-DomainComputer
Get-DomainGroup

# 信任关系
Get-DomainTrust
Get-DomainForeignUser
Get-DomainForeignGroupMember

# 权限分析
Find-LocalAdminAccess
Find-DomainUserLocation
Find-DomainProcess
```

### 3. ADRecon工具
```powershell
# ADRecon收集
.\ADRecon.ps1 -DomainController dc.domain.com -Credential $cred

# 生成详细报告
# - Excel格式报告
# - 包含所有域信息
# - 图表和统计
```

### 4. 自定义PowerShell脚本
```powershell
# 综合信息收集脚本
function Invoke-DomainRecon {
    Write-Host "[+] 开始域信息收集..." -ForegroundColor Green
    
    # 域基本信息
    Write-Host "[*] 收集域基本信息"
    $Domain = Get-ADDomain
    $Forest = Get-ADForest
    
    # 用户信息
    Write-Host "[*] 收集用户信息"
    $Users = Get-ADUser -Filter * -Properties *
    $PrivUsers = Get-ADGroupMember "Domain Admins"
    
    # 计算机信息
    Write-Host "[*] 收集计算机信息"
    $Computers = Get-ADComputer -Filter * -Properties *
    
    # SPN信息
    Write-Host "[*] 收集SPN信息"
    $SPNUsers = Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName
    
    # 信任关系
    Write-Host "[*] 收集信任关系"
    $Trusts = Get-ADTrust -Filter *
    
    # 输出结果
    $Results = @{
        Domain = $Domain
        Forest = $Forest
        Users = $Users
        PrivilegedUsers = $PrivUsers
        Computers = $Computers
        SPNUsers = $SPNUsers
        Trusts = $Trusts
    }
    
    return $Results
}

# 执行收集
$ReconData = Invoke-DomainRecon
```

## 📊 信息收集检查清单

### 基础信息 ✅
- [ ] 域名和FQDN
- [ ] 域控制器列表
- [ ] 域功能级别
- [ ] 林信息
- [ ] 域信任关系

### 用户和组 ✅
- [ ] 所有用户列表
- [ ] 特权用户
- [ ] 服务账户
- [ ] 组成员关系
- [ ] 用户属性分析

### 计算机和服务 ✅
- [ ] 计算机列表
- [ ] 操作系统版本
- [ ] SPN列表
- [ ] 网络服务
- [ ] 共享资源

### 权限和策略 ✅
- [ ] ACL分析
- [ ] GPO配置
- [ ] 委派权限
- [ ] 密码策略
- [ ] 审计策略

### 网络信息 ✅
- [ ] 网络拓扑
- [ ] 开放端口
- [ ] 网络共享
- [ ] DNS记录
- [ ] 证书服务

## 🔧 实用命令速查

### Windows命令
```cmd
# 域信息
echo %USERDOMAIN%
echo %LOGONSERVER%

# 用户信息
whoami /all
net user /domain
net group /domain

# 网络信息
ipconfig /all
nslookup domain.com
```

### PowerShell命令
```powershell
# 快速域信息
[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()

# 当前用户权限
whoami /priv

# 网络连接
Get-NetTCPConnection | Where-Object {$_.State -eq "Established"}
```

### Linux命令
```bash
# LDAP查询
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com"

# Kerberos
kinit <EMAIL>
klist

# SMB枚举
smbclient -L //dc.domain.com
enum4linux dc.domain.com
```

---

> 🔒 **安全提醒**: 信息收集应在合法授权的环境中进行，遵守相关法律法规和道德准则！
