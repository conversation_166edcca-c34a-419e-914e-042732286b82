#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
链接检查脚本 - 检查Markdown文档中的链接是否有效
"""

import os
import re
import sys
from pathlib import Path

def check_markdown_links(base_dir):
    """检查Markdown文档中的链接"""
    base_path = Path(base_dir)
    
    if not base_path.exists():
        print(f"❌ 目录不存在: {base_dir}")
        return
    
    print(f"🔍 开始检查目录: {base_dir}")
    print("=" * 60)
    
    # 查找所有Markdown文件
    md_files = list(base_path.rglob("*.md"))
    
    total_links = 0
    valid_links = 0
    invalid_links = 0
    
    for md_file in md_files:
        print(f"\n📄 检查文件: {md_file.relative_to(base_path)}")
        
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"   ❌ 无法读取文件: {e}")
            continue
        
        # 查找Markdown链接 [text](link)
        links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
        
        if not links:
            print("   ℹ️  未找到链接")
            continue
        
        for link_text, link_url in links:
            total_links += 1
            
            # 跳过外部链接
            if link_url.startswith(('http://', 'https://', 'ftp://', 'mailto:')):
                print(f"   🌐 外部链接: {link_text} -> {link_url}")
                valid_links += 1
                continue
            
            # 处理相对路径
            if link_url.startswith('./'):
                link_url = link_url[2:]
            
            # 构建完整路径
            if link_url.startswith('/'):
                # 绝对路径
                target_path = base_path / link_url[1:]
            else:
                # 相对路径
                target_path = md_file.parent / link_url
            
            # 检查文件是否存在
            if target_path.exists():
                print(f"   ✅ 有效链接: {link_text} -> {link_url}")
                valid_links += 1
            else:
                print(f"   ❌ 无效链接: {link_text} -> {link_url}")
                print(f"      目标路径: {target_path}")
                invalid_links += 1
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print("📊 检查结果统计:")
    print(f"   总链接数: {total_links}")
    print(f"   有效链接: {valid_links}")
    print(f"   无效链接: {invalid_links}")
    
    if invalid_links == 0:
        print("🎉 所有链接都有效！")
    else:
        print(f"⚠️  发现 {invalid_links} 个无效链接，需要修复")
    
    return invalid_links == 0

def list_existing_files(base_dir):
    """列出现有的文件结构"""
    base_path = Path(base_dir)
    
    print(f"\n📁 现有文件结构:")
    print("=" * 60)
    
    # 获取所有文件和目录
    all_items = []
    
    for item in base_path.rglob("*"):
        if item.is_file() and item.suffix in ['.md', '.html', '.py']:
            rel_path = item.relative_to(base_path)
            all_items.append((str(rel_path), item.is_dir()))
    
    # 排序并显示
    all_items.sort()
    
    for item_path, is_dir in all_items:
        if is_dir:
            print(f"📁 {item_path}/")
        else:
            print(f"📄 {item_path}")

def generate_correct_readme(base_dir):
    """生成正确的README.md"""
    base_path = Path(base_dir)
    
    # 检查实际存在的文件
    existing_files = {
        "0range-x域渗透一条龙手册精华.md": "基于0range-x权威手册的技术精华整理",
        "0range-x内容整理完成报告.md": "整理工作总结报告",
        "快速索引.md": "按技术类型和攻击阶段的快速查找指南",
        "01-基础知识/Kerberos认证机制.md": "深入理解Kerberos协议和安全机制",
        "02-信息收集/域内信息收集完全指南.md": "系统化的AD环境信息收集方法",
        "04-权限提升/Token窃取技术.md": "Windows Token机制和窃取技术详解",
        "05-横向移动/内网渗透常用技术详解.md": "内网渗透中最常用的技术和工具",
        "06-域控获取/SPN扫描与Kerberoasting.md": "完整的Kerberoasting攻击技术",
        "06-域控获取/委派攻击详解.md": "无约束/约束/RBCD委派攻击",
        "06-域控获取/Kerberos票据制作详解.md": "深入理解票据结构和制作方法",
        "06-域控获取/Zerologon-CVE-2020-1472.md": "Zerologon漏洞完整利用指南",
        "09-工具与脚本/Mimikatz使用大全.md": "Windows凭证提取的瑞士军刀",
        "09-工具与脚本/Fscan内网扫描工具详解.md": "一款内网综合扫描工具详解",
        "10-实战案例/完整域渗透案例.md": "从外网到域控的完整攻击链分析"
    }
    
    # 验证文件是否存在
    valid_files = {}
    for file_path, description in existing_files.items():
        full_path = base_path / file_path
        if full_path.exists():
            valid_files[file_path] = description
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print(f"\n✅ 找到 {len(valid_files)} 个有效文件")
    
    # 生成新的README内容
    readme_content = """# 🏰 域渗透学习资源库

> **全面的Active Directory渗透测试学习资源集合**

## 📚 资源库概述

本资源库整合了来自多个权威来源的域渗透技术，包括：
- [chriskaliX/AD-Pentest-Notes](https://github.com/chriskaliX/AD-Pentest-Notes)
- [blaCCkHatHacEEkr/PENTESTING-BIBLE](https://github.com/blaCCkHatHacEEkr/PENTESTING-BIBLE)
- [0range-x域渗透一条龙手册](https://0range-x.github.io/2022/01/26/Domain-penetration_one-stop/)
- MITRE ATT&CK框架
- 各大安全厂商研究报告

## 🗂️ 目录结构

"""
    
    # 按类别组织文件
    categories = {
        "🎯 权威手册精华": [
            "0range-x域渗透一条龙手册精华.md",
            "0range-x内容整理完成报告.md"
        ],
        "📖 基础知识": [
            "01-基础知识/Kerberos认证机制.md"
        ],
        "🔍 信息收集": [
            "02-信息收集/域内信息收集完全指南.md"
        ],
        "⬆️ 权限提升": [
            "04-权限提升/Token窃取技术.md"
        ],
        "↔️ 横向移动": [
            "05-横向移动/内网渗透常用技术详解.md"
        ],
        "👑 域控获取": [
            "06-域控获取/SPN扫描与Kerberoasting.md",
            "06-域控获取/委派攻击详解.md",
            "06-域控获取/Kerberos票据制作详解.md",
            "06-域控获取/Zerologon-CVE-2020-1472.md"
        ],
        "🛠️ 工具与脚本": [
            "09-工具与脚本/Mimikatz使用大全.md",
            "09-工具与脚本/Fscan内网扫描工具详解.md"
        ],
        "🎯 实战案例": [
            "10-实战案例/完整域渗透案例.md"
        ],
        "📋 快速导航": [
            "快速索引.md"
        ]
    }
    
    for category, files in categories.items():
        readme_content += f"### {category}\n"
        for file_path in files:
            if file_path in valid_files:
                description = valid_files[file_path]
                readme_content += f"- [{file_path.split('/')[-1].replace('.md', '')}]({file_path}) - {description}\n"
        readme_content += "\n"
    
    readme_content += """
## 🔒 安全提醒

⚠️ **重要声明**: 本资源库内容仅供安全研究、学习和授权的渗透测试使用。严禁用于未授权的网络攻击、恶意破坏活动或任何违法犯罪行为。

---

> 🎯 **学习目标**: 通过系统学习本资源库，掌握完整的域渗透技术体系！

**最后更新**: 2025-07-28  
**版本**: v2.1 (0range-x增强版)
"""
    
    # 保存新的README
    readme_path = base_path / "README-自动生成.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 已生成新的README: {readme_path}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_dir = sys.argv[1]
    else:
        base_dir = r"D:\笔记\域渗透学习资源库"
    
    print("🔗 Markdown链接检查工具")
    print("=" * 60)
    
    # 检查目录是否存在
    if not os.path.exists(base_dir):
        print(f"❌ 目录不存在: {base_dir}")
        return
    
    # 列出现有文件
    list_existing_files(base_dir)
    
    # 检查链接
    links_valid = check_markdown_links(base_dir)
    
    # 生成正确的README
    print("\n🔧 生成修复版README...")
    generate_correct_readme(base_dir)
    
    if links_valid:
        print("\n🎉 所有检查完成，链接都有效！")
    else:
        print("\n⚠️  发现无效链接，请查看上面的详细信息")
        print("💡 建议使用生成的 README-自动生成.md 替换原文件")

if __name__ == "__main__":
    main()
