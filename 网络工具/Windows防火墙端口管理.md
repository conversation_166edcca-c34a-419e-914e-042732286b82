# Windows 防火墙端口管理指南

## 概述
本文档介绍如何使用 Windows 防火墙命令管理入站和出站端口规则，包括 netsh 和 PowerShell 两种方法。

## 📁 目录
- [程序规则管理](#程序规则管理)
- [端口规则管理](#端口规则管理)
- [查看和删除规则](#查看和删除规则)
- [防火墙状态管理](#防火墙状态管理)

## 程序规则管理

### 使用 netsh 命令

#### ToDesk 远程桌面程序示例
```cmd
# 允许 ToDesk 出站连接
netsh advfirewall firewall add rule name="Allow ToDesk Outbound" dir=out action=allow program="C:\Program Files\ToDesk\ToDesk.exe" enable=yes

# 允许 ToDesk 入站连接
netsh advfirewall firewall add rule name="Allow ToDesk Inbound" dir=in action=allow program="C:\Program Files\ToDesk\ToDesk.exe" enable=yes
```

#### 其他程序示例
```cmd
# 允许浏览器出站
netsh advfirewall firewall add rule name="Allow Chrome" dir=out action=allow program="C:\Program Files\Google\Chrome\Application\chrome.exe"

# 允许服务器程序入站
netsh advfirewall firewall add rule name="Allow Server" dir=in action=allow program="C:\MyApp\server.exe"
```

### 使用 PowerShell 命令

#### ToDesk 程序规则
```powershell
# 添加出站规则
New-NetFirewallRule -DisplayName "Allow ToDesk Outbound" -Direction Outbound -Program "C:\Program Files\ToDesk\ToDesk.exe" -Action Allow

# 添加入站规则
New-NetFirewallRule -DisplayName "Allow ToDesk Inbound" -Direction Inbound -Program "C:\Program Files\ToDesk\ToDesk.exe" -Action Allow
```

#### 高级 PowerShell 规则
```powershell
# 创建带端口的程序规则
New-NetFirewallRule -DisplayName "MyApp HTTP" -Direction Inbound -Program "C:\MyApp\app.exe" -Protocol TCP -LocalPort 80 -Action Allow

# 创建带 IP 限制的规则
New-NetFirewallRule -DisplayName "Secure App" -Direction Inbound -Program "C:\MyApp\app.exe" -RemoteAddress "***********/24" -Action Allow
```

## 端口规则管理

### TCP 端口规则
```cmd
# 允许入站端口 8888
netsh advfirewall firewall add rule name="Allow Inbound Port 8888" dir=in action=allow protocol=TCP localport=8888

# 允许出站端口 8888
netsh advfirewall firewall add rule name="Allow Outbound Port 8888" dir=out action=allow protocol=TCP localport=8888

# 允许端口范围
netsh advfirewall firewall add rule name="Allow Ports 8000-9000" dir=in action=allow protocol=TCP localport=8000-9000
```

### UDP 端口规则
```cmd
# 允许 DNS 查询 (UDP 53)
netsh advfirewall firewall add rule name="Allow DNS UDP" dir=out action=allow protocol=UDP localport=53

# 允许 DHCP 客户端 (UDP 68)
netsh advfirewall firewall add rule name="Allow DHCP Client" dir=out action=allow protocol=UDP localport=68
```

### 常用服务端口
```cmd
# Web 服务
netsh advfirewall firewall add rule name="Allow HTTP" dir=in action=allow protocol=TCP localport=80
netsh advfirewall firewall add rule name="Allow HTTPS" dir=in action=allow protocol=TCP localport=443

# 远程桌面
netsh advfirewall firewall add rule name="Allow RDP" dir=in action=allow protocol=TCP localport=3389

# SSH
netsh advfirewall firewall add rule name="Allow SSH" dir=in action=allow protocol=TCP localport=22

# 数据库
netsh advfirewall firewall add rule name="Allow MySQL" dir=in action=allow protocol=TCP localport=3306
netsh advfirewall firewall add rule name="Allow SQL Server" dir=in action=allow protocol=TCP localport=1433
```

## 查看和删除规则

### 查看规则
```cmd
# 查看所有规则
netsh advfirewall firewall show rule name=all

# 查看入站规则
netsh advfirewall firewall show rule name=all dir=in

# 查看特定规则
netsh advfirewall firewall show rule name="Allow ToDesk Inbound"

# 使用 PowerShell 查看
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "*ToDesk*"}
```

### 删除规则
```cmd
# 按名称删除规则
netsh advfirewall firewall delete rule name="Allow ToDesk Inbound"

# 删除程序的所有规则
netsh advfirewall firewall delete rule program="C:\Program Files\ToDesk\ToDesk.exe"

# 删除端口规则
netsh advfirewall firewall delete rule protocol=TCP localport=8888

# 使用 PowerShell 删除
Remove-NetFirewallRule -DisplayName "Allow ToDesk Inbound"
```

## 防火墙状态管理

### 查看防火墙状态
```cmd
# 查看所有配置文件状态
netsh advfirewall show allprofiles

# 查看特定配置文件
netsh advfirewall show domainprofile
netsh advfirewall show privateprofile
netsh advfirewall show publicprofile
```

### 启用/禁用防火墙
```cmd
# 启用所有配置文件的防火墙
netsh advfirewall set allprofiles state on

# 禁用所有配置文件的防火墙
netsh advfirewall set allprofiles state off

# 启用特定配置文件
netsh advfirewall set domainprofile state on
netsh advfirewall set privateprofile state on
netsh advfirewall set publicprofile state on
```

### 重置防火墙
```cmd
# 重置到默认设置
netsh advfirewall reset

# 恢复默认策略
netsh advfirewall set allprofiles firewallpolicy blockinbound,allowoutbound
```

## 高级配置

### 基于 IP 地址的规则
```cmd
# 允许特定 IP
netsh advfirewall firewall add rule name="Allow Specific IP" dir=in action=allow remoteip=*************

# 阻止特定 IP
netsh advfirewall firewall add rule name="Block IP" dir=in action=block remoteip=*************

# 允许子网
netsh advfirewall firewall add rule name="Allow Subnet" dir=in action=allow remoteip=***********/24
```

### 基于时间的规则（PowerShell）
```powershell
# 创建工作时间规则
$timespan = New-ScheduledTaskTrigger -Daily -At "09:00"
New-NetFirewallRule -DisplayName "Work Hours Only" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
```

### 日志配置
```cmd
# 启用防火墙日志
netsh advfirewall set allprofiles logging filename "C:\Windows\System32\LogFiles\Firewall\pfirewall.log"
netsh advfirewall set allprofiles logging maxfilesize 4096
netsh advfirewall set allprofiles logging droppedconnections enable
netsh advfirewall set allprofiles logging allowedconnections enable
```

## 实用脚本

### 批量添加规则脚本
```batch
@echo off
echo 正在配置防火墙规则...

REM 添加常用端口规则
netsh advfirewall firewall add rule name="Allow HTTP" dir=in action=allow protocol=TCP localport=80
netsh advfirewall firewall add rule name="Allow HTTPS" dir=in action=allow protocol=TCP localport=443
netsh advfirewall firewall add rule name="Allow SSH" dir=in action=allow protocol=TCP localport=22

REM 添加程序规则
netsh advfirewall firewall add rule name="Allow ToDesk" dir=in action=allow program="C:\Program Files\ToDesk\ToDesk.exe"

echo 防火墙规则配置完成！
pause
```

### PowerShell 批量管理脚本
```powershell
# 批量添加端口规则
$ports = @(80, 443, 22, 3389, 8080)
foreach ($port in $ports) {
    New-NetFirewallRule -DisplayName "Allow Port $port" -Direction Inbound -Protocol TCP -LocalPort $port -Action Allow
}

# 批量删除规则
Get-NetFirewallRule | Where-Object {$_.DisplayName -like "Allow Port*"} | Remove-NetFirewallRule
```

## 注意事项
- 修改防火墙规则需要管理员权限
- 建议在生产环境中谨慎操作
- 定期审查和清理不需要的规则
- 测试规则的有效性和影响
- 备份重要的防火墙配置

## 故障排除
1. **规则不生效**: 检查规则优先级和语法
2. **程序路径错误**: 确认程序完整路径
3. **端口冲突**: 检查是否有其他程序占用端口
4. **权限问题**: 确保以管理员身份运行

## 相关文档
- [Windows 系统管理命令](../常用命令/Windows系统管理命令集合.md)
- [系统管理指南](../系统管理/Windows/)
- [网络工具使用指南](../网络工具/)
