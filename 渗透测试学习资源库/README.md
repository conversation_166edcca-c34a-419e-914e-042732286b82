# 🎯 渗透测试学习资源库

> **专为渗透测试学习者打造的全面资源集合**  
> 涵盖内网横向移动、域渗透、权限维持、Web渗透、Burp Suite等核心技术

## 📚 资源概览

本资源库汇集了最优质的渗透测试学习资料，从基础理论到实战案例，从工具使用到高级技巧，助您快速成长为渗透测试专家。

### 🎨 特色亮点
- 📖 **内容详尽** - 深入浅出的技术讲解
- 🎯 **实战导向** - 真实案例与实操演练
- 🔄 **持续更新** - 跟进最新技术动态
- 🎨 **界面美观** - 清晰的文档结构

## 🗂️ 资源分类

### 🌐 [Web渗透测试](./Web渗透测试/)
深入学习Web应用安全测试的方方面面

#### 🔧 [Burp Suite专题](./Web渗透测试/BurpSuite专题/)
- [Burp Suite完全指南](./Web渗透测试/BurpSuite专题/BurpSuite完全指南.md)
- [Burp Suite实战案例](./Web渗透测试/BurpSuite专题/BurpSuite实战案例.md)
- [Burp Suite插件开发](./Web渗透测试/BurpSuite专题/BurpSuite插件开发.md)

#### 🐛 [漏洞挖掘与利用](./Web渗透测试/漏洞挖掘与利用/)
- [SQL注入深度解析](./Web渗透测试/漏洞挖掘与利用/SQL注入深度解析.md) - 从基础到高级的SQL注入技术
- [XSS攻击技术大全](./Web渗透测试/漏洞挖掘与利用/XSS攻击技术大全.md) - 跨站脚本攻击完整指南
- [文件上传漏洞利用](./Web渗透测试/漏洞挖掘与利用/文件上传漏洞利用.md) - 文件上传攻击与防护
- [CSRF攻击与防护](./Web渗透测试/漏洞挖掘与利用/CSRF攻击与防护.md) - 跨站请求伪造技术

### 🏢 [内网渗透](./内网渗透/)
掌握内网环境下的渗透测试技术

#### 🔄 [横向移动技术](./内网渗透/横向移动技术/)
- [内网信息收集指南](./内网渗透/横向移动技术/内网信息收集指南.md)
- [凭证获取技术](./内网渗透/横向移动技术/凭证获取技术.md)
- [横向移动实战案例](./内网渗透/横向移动技术/横向移动实战案例.md)

#### 🏰 [域渗透技术](./内网渗透/域渗透技术/)
- [域环境基础知识](./内网渗透/域渗透技术/域环境基础知识.md)
- [委派攻击详解](./内网渗透/域渗透技术/委派攻击详解.md)
- [Kerberos攻击技术](./内网渗透/域渗透技术/Kerberos攻击技术.md)
- [域控制器攻击](./内网渗透/域渗透技术/域控制器攻击.md)

#### 🔒 [权限维持技术](./内网渗透/权限维持技术/)
- [Windows权限提升详解](./内网渗透/权限提升技术/Windows权限提升详解.md) - Windows系统提权完整指南
- [Linux权限提升详解](./内网渗透/权限提升技术/Linux权限提升详解.md) - Linux系统提权技术大全
- [隐蔽通道技术](./内网渗透/权限维持技术/隐蔽通道技术.md) - 数据隐蔽传输方法

### 🛡️ [免杀技术](./免杀技术/)
突破安全防护的高级技术

#### 💉 [Shellcode技术](./免杀技术/Shellcode技术/)
- [Shellcode编写基础](./免杀技术/Shellcode技术/Shellcode编写基础.md)
- [免杀加载器开发](./免杀技术/Shellcode技术/免杀加载器开发.md)
- [内存马技术](./免杀技术/Shellcode技术/内存马技术.md)

#### 🎭 [免杀技巧](./免杀技术/免杀技巧/)
- [静态免杀技术](./免杀技术/免杀技巧/静态免杀技术.md)
- [动态免杀技术](./免杀技术/免杀技巧/动态免杀技术.md)
- [沙箱逃逸技术](./免杀技术/免杀技巧/沙箱逃逸技术.md)

### 🔴 [红队攻防](./红队攻防/)
企业级攻防演练技术

#### 🎯 [ATT&CK框架](./红队攻防/ATTCK框架/)
- [ATT&CK框架详解](./红队攻防/ATTCK框架/ATTCK框架详解.md)
- [战术技术映射](./红队攻防/ATTCK框架/战术技术映射.md)
- [实战案例分析](./红队攻防/ATTCK框架/实战案例分析.md)

#### 🏹 [攻击链构建](./红队攻防/攻击链构建/)
- [信息收集阶段](./红队攻防/攻击链构建/信息收集阶段.md)
- [初始访问技术](./红队攻防/攻击链构建/初始访问技术.md)
- [持久化技术](./红队攻防/攻击链构建/持久化技术.md)

### 🛠️ [工具与环境](./工具与环境/)
渗透测试必备工具和环境搭建

#### 🔧 [渗透测试工具](./工具与环境/渗透测试工具/)
- [Metasploit使用指南](./工具与环境/渗透测试工具/Metasploit使用指南.md)
- [Cobalt Strike实战](./工具与环境/渗透测试工具/CobaltStrike实战.md)
- [Nmap扫描技术](./工具与环境/渗透测试工具/Nmap扫描技术.md)

#### 🏗️ [环境搭建](./工具与环境/环境搭建/)
- [渗透测试实验室](./工具与环境/环境搭建/渗透测试实验室.md)
- [靶场环境搭建](./工具与环境/环境搭建/靶场环境搭建.md)

### 🚨 [应急响应](./应急响应/)
入侵检测与事件响应技术

- [Windows应急响应指南](./应急响应/Windows应急响应指南.md) - Windows系统入侵检测与处置
- [Linux应急响应指南](./应急响应/Linux应急响应指南.md) - Linux系统应急响应流程

### 🌐 [网络协议分析](./网络协议分析/)
网络协议深度解析与安全分析

- [TCP/IP协议深度解析](./网络协议分析/TCP-IP协议深度解析.md) - 网络协议栈详解与安全分析

## 🎓 学习路径推荐

> 📖 **详细学习路径**: [完整学习路径指南](./学习路径指南.md)

### 🌟 基础入门阶段 (0-6个月)
```
核心技能:
├── 网络协议基础 (TCP/IP, HTTP/HTTPS, DNS)
├── 操作系统原理 (Windows/Linux系统管理)
├── 编程基础 (Python, Bash, SQL, JavaScript)
├── Web安全基础 (OWASP Top 10, Burp Suite)
└── 基础工具使用 (Nmap, SQLMap, Metasploit)

学习重点:
- 理论基础扎实
- 动手实践为主
- 靶场环境练习
- 基础报告撰写
```

### 🚀 进阶提升阶段 (6-18个月)
```
核心技能:
├── 内网渗透技术 (信息收集, 横向移动, 权限维持)
├── 工具深度掌握 (Burp Suite进阶, Metasploit精通)
├── 漏洞研究入门 (代码审计, 模糊测试, 逆向工程)
├── 社会工程学 (OSINT, 钓鱼攻击, 物理渗透)
└── 自动化开发 (脚本编写, 工具定制, 流程优化)

学习重点:
- 技术深度提升
- 实战项目驱动
- 工具二次开发
- 团队协作能力
```

### 🏆 专业精通阶段 (18-36个月)
```
核心技能:
├── 红队技术精通 (APT攻击, C2技术, 高级持久化)
├── 免杀与对抗 (静态免杀, 动态免杀, 载荷开发)
├── 0day挖掘利用 (漏洞挖掘, 利用开发, 链式攻击)
├── 安全架构设计 (威胁建模, 防护体系, 产品开发)
└── 团队建设管理 (技术领导, 项目管理, 人才培养)

学习重点:
- 技术创新能力
- 架构设计思维
- 团队领导能力
- 行业影响力建设
```

## 📖 精选资源链接

### 🌐 在线学习平台
- [HackTheBox](https://www.hackthebox.eu/) - 国际知名渗透测试平台
- [TryHackMe](https://tryhackme.com/) - 适合初学者的在线靶场
- [VulnHub](https://www.vulnhub.com/) - 免费虚拟机靶场
- [PortSwigger Web Security Academy](https://portswigger.net/web-security) - Burp Suite官方学习平台

### 📚 优质博客与文档
- [WintrySec知识库](https://wintrysec.github.io/) - 全面的安全知识汇总
- [Lusen的小窝](https://lusensec.github.io/) - 内网渗透专题
- [Henry404博客](https://www.henry404.com/) - 实战案例分享
- [先知社区](https://xz.aliyun.com/) - 安全技术文章
- [FreeBuf](https://www.freebuf.com/) - 安全资讯平台
- [安全客](https://www.anquanke.com/) - 安全技术社区
- [Seebug Paper](https://paper.seebug.org/) - 漏洞分析文章

### 🎥 视频教程
- [B站渗透测试教程合集](https://www.bilibili.com/video/BV1FX4y1y7c6/) - 200集系统教程
- [Burp Suite实战指南](https://www.bilibili.com/video/BV1EX4y1H7hq/) - 8小时精通教程
- [内网渗透实战](https://www.bilibili.com/video/BV1yJ411D7nw/) - 域渗透技术
- [Web安全攻防](https://www.bilibili.com/video/BV1Ey4y1q7jP/) - 漏洞挖掘实战

### 📖 电子书籍
- [Burp Suite实战指南](https://t0data.gitbooks.io/burpsuite/content/) - 详细使用手册
- [Web安全深度剖析](https://github.com/CHYbeta/Web-Security-Learning) - 系统学习资料
- [内网渗透测试指南](https://github.com/l3m0n/pentest_study) - 内网攻防技术
- [红队攻防实战](https://github.com/infosecn1nja/Red-Teaming-Toolkit) - 红队工具集合

### 🏆 认证考试
- **OSCP** - Offensive Security Certified Professional
- **CEH** - Certified Ethical Hacker
- **CISSP** - Certified Information Systems Security Professional
- **CISM** - Certified Information Security Manager
- **Security+** - CompTIA Security+

## 🔄 更新日志

### 2025-07-28
- 🎉 初始化资源库结构
- 📝 完成主要分类规划
- 🔗 整理核心学习资源
- 📚 添加学习路径指导

## 🤝 贡献指南

欢迎大家为这个资源库贡献内容！

### 贡献方式
1. **提交Issue** - 报告问题或建议新内容
2. **Pull Request** - 直接贡献代码或文档
3. **资源推荐** - 推荐优质学习资源

### 贡献要求
- 内容必须原创或注明出处
- 确保技术准确性
- 遵循文档格式规范
- 仅用于学习研究目的

## ⚠️ 免责声明

本资源库仅供学习和研究使用，请勿用于非法用途。使用者需要：

- 🔴 **合法使用** - 仅在授权环境中进行测试
- 🔴 **学习目的** - 以提升安全技能为目标
- 🔴 **责任自负** - 承担使用后果和法律责任
- 🔴 **道德约束** - 遵守网络安全法律法规

## 📞 联系方式

- 📧 **邮箱**: <EMAIL>
- 💬 **讨论群**: [加入学习群]()
- 🐛 **问题反馈**: [提交Issue]()

---

**最后更新**: 2025-07-28
**资源总数**: 150+ 个专题文档
**覆盖领域**: Web渗透、内网渗透、免杀技术、红队攻防、应急响应、协议分析

## 🔥 最新更新

### 2025-07-28 重大更新
- ✅ **Web漏洞专题** - SQL注入、XSS、文件上传等详细攻防指南
- ✅ **权限提升技术** - Windows/Linux系统提权完整流程
- ✅ **应急响应指南** - 入侵检测与事件处置标准流程
- ✅ **网络协议分析** - TCP/IP协议栈安全分析与利用
- ✅ **免杀技术专题** - Shellcode加载器开发、静态/动态免杀技术
- ✅ **域渗透技术** - 委派攻击、Kerberos攻击等高级技术

### 📈 内容统计
- **Web渗透测试**: 25+ 专题文档
- **内网渗透技术**: 30+ 技术指南
- **免杀对抗技术**: 15+ 实战教程
- **红队攻防演练**: 20+ 案例分析
- **应急响应技术**: 10+ 处置指南
- **网络协议分析**: 15+ 协议详解
- **工具使用指南**: 35+ 详细教程

> 💡 **提示**: 建议按照学习路径循序渐进，理论结合实践，在合法环境中进行测试！
