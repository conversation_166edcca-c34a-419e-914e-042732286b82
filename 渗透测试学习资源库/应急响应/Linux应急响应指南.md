# 🐧 Linux应急响应指南

> **Linux系统入侵检测与应急处置完整流程**

## 📋 目录

- [应急响应概述](#应急响应概述)
- [初步评估](#初步评估)
- [证据收集](#证据收集)
- [入侵分析](#入侵分析)
- [威胁清除](#威胁清除)
- [系统加固](#系统加固)
- [报告总结](#报告总结)

## 🎯 应急响应概述

### Linux入侵常见迹象

- 🔄 **系统异常** - 负载异常、内存占用高、磁盘空间不足
- 🌐 **网络异常** - 异常连接、大量数据传输、未知端口监听
- 📁 **文件异常** - 系统文件被篡改、异常文件出现、权限变化
- 👤 **账户异常** - 异常登录、新增用户、权限提升
- 📊 **日志异常** - 日志被清空、异常登录记录、权限变更记录

### 响应优先级

```mermaid
graph TD
    A[紧急响应] --> A1[断网隔离]
    A --> A2[保护现场]
    A --> A3[关键证据收集]
    
    B[快速分析] --> B1[威胁识别]
    B --> B2[影响评估]
    B --> B3[传播路径]
    
    C[威胁处置] --> C1[恶意进程终止]
    C --> C2[后门清除]
    C --> C3[漏洞修复]
```

## 🔍 初步评估

### 1. 系统状态检查

#### 基础信息收集
```bash
# 系统基本信息
uname -a
hostname
whoami
id
date

# 系统版本信息
cat /etc/os-release
cat /etc/issue
lsb_release -a

# 内核版本
uname -r
cat /proc/version
```

#### 用户和登录信息
```bash
# 当前登录用户
w
who
last
lastlog

# 用户账户信息
cat /etc/passwd
cat /etc/group
cat /etc/shadow  # 需要root权限

# 检查新增用户
awk -F: '$3 >= 1000 {print $1}' /etc/passwd
```

#### 进程和服务检查
```bash
# 查看运行进程
ps aux
ps -ef
pstree

# 查看进程树
ps auxf

# 查看系统负载
top
htop
uptime
cat /proc/loadavg

# 查看内存使用
free -h
cat /proc/meminfo
```

### 2. 网络状态检查

#### 网络连接
```bash
# 查看网络连接
netstat -antup
ss -antup

# 查看监听端口
netstat -tlnp
ss -tlnp

# 查看网络配置
ifconfig
ip addr show
ip route show
```

#### 网络流量
```bash
# 查看网络流量统计
cat /proc/net/dev
iftop
nethogs

# 查看网络连接统计
cat /proc/net/sockstat
```

### 3. 文件系统检查

#### 磁盘使用情况
```bash
# 查看磁盘使用
df -h
du -sh /*

# 查看inode使用
df -i

# 查看挂载点
mount
cat /proc/mounts
```

#### 最近修改的文件
```bash
# 查找最近24小时修改的文件
find / -mtime -1 -type f 2>/dev/null

# 查找最近修改的系统文件
find /bin /sbin /usr/bin /usr/sbin -mtime -7 -type f 2>/dev/null

# 查找大文件
find / -size +100M -type f 2>/dev/null
```

## 📊 证据收集

### 1. 内存取证

#### 内存镜像获取
```bash
# 使用LiME获取内存镜像
insmod lime.ko "path=/tmp/memory.lime format=lime"

# 使用dd获取内存（如果/dev/mem可用）
dd if=/dev/mem of=/tmp/memory.dump bs=1M

# 使用AVML获取内存镜像
./avml /tmp/memory.lime
```

#### 内存分析
```bash
# 使用Volatility分析Linux内存
volatility --profile=LinuxUbuntu1804x64 -f memory.lime linux_banner
volatility --profile=LinuxUbuntu1804x64 -f memory.lime linux_pslist
volatility --profile=LinuxUbuntu1804x64 -f memory.lime linux_netstat
volatility --profile=LinuxUbuntu1804x64 -f memory.lime linux_lsmod
```

### 2. 磁盘取证

#### 磁盘镜像获取
```bash
# 使用dd创建磁盘镜像
dd if=/dev/sda of=/mnt/external/disk_image.dd bs=512 conv=noerror,sync

# 使用dcfldd（带哈希验证）
dcfldd if=/dev/sda of=/mnt/external/disk_image.dd hash=md5,sha1 bs=512

# 使用ddrescue（处理坏扇区）
ddrescue /dev/sda /mnt/external/disk_image.dd /mnt/external/rescue.log
```

#### 文件系统分析
```bash
# 使用Sleuth Kit分析
fls -r /dev/sda1 > file_list.txt
ils /dev/sda1 > inode_list.txt

# 创建时间线
fls -r -m / /dev/sda1 > timeline.body
mactime -b timeline.body > timeline.txt
```

### 3. 日志收集

#### 系统日志
```bash
# 复制重要日志文件
cp /var/log/messages /tmp/evidence/
cp /var/log/syslog /tmp/evidence/
cp /var/log/auth.log /tmp/evidence/
cp /var/log/secure /tmp/evidence/
cp /var/log/kern.log /tmp/evidence/

# 查看最近的日志
tail -n 1000 /var/log/messages
tail -n 1000 /var/log/auth.log

# 使用journalctl查看systemd日志
journalctl --since "2025-07-28 00:00:00"
journalctl -u ssh.service
journalctl -p err
```

#### 应用程序日志
```bash
# Web服务器日志
cp /var/log/apache2/* /tmp/evidence/
cp /var/log/nginx/* /tmp/evidence/

# 数据库日志
cp /var/log/mysql/* /tmp/evidence/
cp /var/log/postgresql/* /tmp/evidence/

# 邮件服务日志
cp /var/log/mail.log /tmp/evidence/
```

### 4. 网络证据

#### 网络配置备份
```bash
# 备份网络配置
cp /etc/network/interfaces /tmp/evidence/
cp /etc/hosts /tmp/evidence/
cp /etc/resolv.conf /tmp/evidence/

# 备份防火墙规则
iptables-save > /tmp/evidence/iptables_rules.txt
ip6tables-save > /tmp/evidence/ip6tables_rules.txt
```

#### 网络流量捕获
```bash
# 使用tcpdump捕获流量
tcpdump -i any -w /tmp/evidence/network_capture.pcap

# 捕获特定端口流量
tcpdump -i eth0 port 22 -w /tmp/evidence/ssh_traffic.pcap

# 使用tshark分析
tshark -r network_capture.pcap -T fields -e ip.src -e ip.dst -e tcp.dstport
```

## 🔬 入侵分析

### 1. 恶意进程分析

#### 进程分析
```bash
# 查看进程详细信息
ps aux --sort=-%cpu | head -20
ps aux --sort=-%mem | head -20

# 查看进程打开的文件
lsof -p <PID>

# 查看进程网络连接
lsof -i -p <PID>

# 查看进程命令行
cat /proc/<PID>/cmdline
cat /proc/<PID>/environ
```

#### 可疑进程识别
```bash
# 查找CPU使用率异常的进程
ps aux | awk '{if($3>50) print $0}'

# 查找内存使用异常的进程
ps aux | awk '{if($4>10) print $0}'

# 查找异常的网络连接
netstat -antup | grep -v "127.0.0.1\|::1"

# 查找隐藏进程
ps aux | wc -l
ls /proc | grep "^[0-9]" | wc -l
```

### 2. 文件分析

#### 可疑文件查找
```bash
# 查找SUID/SGID文件
find / -perm -4000 -type f 2>/dev/null
find / -perm -2000 -type f 2>/dev/null

# 查找最近修改的可执行文件
find / -perm -111 -mtime -7 -type f 2>/dev/null

# 查找异常位置的可执行文件
find /tmp /var/tmp /dev/shm -type f -executable 2>/dev/null

# 查找隐藏文件
find / -name ".*" -type f 2>/dev/null | grep -v "/proc\|/sys"
```

#### 文件完整性检查
```bash
# 使用AIDE检查文件完整性
aide --check

# 使用Tripwire检查
tripwire --check

# 手动检查关键系统文件
md5sum /bin/bash /bin/sh /usr/bin/passwd /usr/bin/sudo
```

### 3. 网络行为分析

#### 连接分析
```bash
# 分析网络连接
netstat -antup | awk '{print $5}' | cut -d: -f1 | sort | uniq -c | sort -nr

# 查看异常端口连接
netstat -antup | grep -E ":4444|:5555|:6666|:7777|:8888|:9999"

# 分析DNS查询
cat /var/log/syslog | grep "dnsmasq\|named" | tail -100
```

#### 流量分析
```bash
# 分析网络流量模式
iftop -t -s 60

# 查看连接统计
ss -s

# 分析异常流量
tcpdump -nn -c 1000 | awk '{print $3}' | cut -d. -f1-4 | sort | uniq -c | sort -nr
```

### 4. 持久化机制分析

#### 启动项检查
```bash
# 检查系统启动脚本
ls -la /etc/init.d/
ls -la /etc/rc*.d/

# 检查systemd服务
systemctl list-unit-files --type=service | grep enabled
systemctl list-units --type=service --state=running

# 检查用户启动项
ls -la ~/.bashrc ~/.bash_profile ~/.profile
cat ~/.bashrc | grep -v "^#\|^$"
```

#### 定时任务检查
```bash
# 检查crontab
crontab -l
cat /etc/crontab
ls -la /etc/cron.*
ls -la /var/spool/cron/

# 检查at任务
atq
ls -la /var/spool/at/
```

#### 内核模块检查
```bash
# 查看加载的内核模块
lsmod
cat /proc/modules

# 检查内核模块文件
find /lib/modules/$(uname -r) -name "*.ko" -mtime -30
```

## 🧹 威胁清除

### 1. 进程清理

#### 终止恶意进程
```bash
# 根据进程名终止
pkill -f malicious_process
killall malicious_process

# 根据PID终止
kill -9 <PID>

# 终止进程树
pkill -P <PPID>
```

#### 清理进程残留
```bash
# 清理共享内存
ipcs -m | awk '{print $2}' | xargs -I {} ipcrm -m {}

# 清理信号量
ipcs -s | awk '{print $2}' | xargs -I {} ipcrm -s {}

# 清理消息队列
ipcs -q | awk '{print $2}' | xargs -I {} ipcrm -q {}
```

### 2. 文件清理

#### 删除恶意文件
```bash
# 删除恶意文件
rm -f /path/to/malicious_file

# 安全删除（覆盖数据）
shred -vfz -n 3 /path/to/malicious_file

# 删除目录
rm -rf /path/to/malicious_directory
```

#### 清理临时文件
```bash
# 清理临时目录
rm -rf /tmp/*
rm -rf /var/tmp/*
rm -rf /dev/shm/*

# 清理用户临时文件
rm -rf ~/.cache/*
rm -rf ~/.tmp/*
```

### 3. 服务清理

#### 停止恶意服务
```bash
# 停止systemd服务
systemctl stop malicious_service
systemctl disable malicious_service

# 删除服务文件
rm -f /etc/systemd/system/malicious_service.service
systemctl daemon-reload

# 停止SysV服务
service malicious_service stop
chkconfig malicious_service off
```

### 4. 网络清理

#### 断开恶意连接
```bash
# 查找并终止恶意连接
netstat -antup | grep <malicious_ip>
# 记录PID后终止对应进程

# 阻止恶意IP
iptables -A INPUT -s <malicious_ip> -j DROP
iptables -A OUTPUT -d <malicious_ip> -j DROP
```

#### 清理网络配置
```bash
# 清理hosts文件
cp /etc/hosts /etc/hosts.backup
sed -i '/malicious_domain/d' /etc/hosts

# 清理DNS缓存
systemctl restart systemd-resolved
# 或者
/etc/init.d/nscd restart
```

## 🔒 系统加固

### 1. 账户安全

#### 密码策略
```bash
# 设置密码策略
echo "PASS_MAX_DAYS 90" >> /etc/login.defs
echo "PASS_MIN_DAYS 1" >> /etc/login.defs
echo "PASS_MIN_LEN 8" >> /etc/login.defs

# 锁定不必要账户
usermod -L <username>
usermod -s /sbin/nologin <username>

# 删除可疑账户
userdel -r <suspicious_user>
```

#### SSH安全
```bash
# 修改SSH配置
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# 禁用root登录
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 修改默认端口
sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# 禁用密码认证（如果使用密钥）
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config

# 重启SSH服务
systemctl restart sshd
```

### 2. 系统更新

#### 安装安全补丁
```bash
# Ubuntu/Debian
apt update && apt upgrade -y
apt install unattended-upgrades

# CentOS/RHEL
yum update -y
# 或者
dnf update -y

# 启用自动更新
systemctl enable unattended-upgrades
```

### 3. 防火墙配置

#### 配置iptables
```bash
# 清空现有规则
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# 设置默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 允许SSH
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

### 4. 监控加强

#### 日志监控
```bash
# 配置rsyslog
echo "*.* @@log-server:514" >> /etc/rsyslog.conf

# 启用auditd
systemctl enable auditd
systemctl start auditd

# 配置审计规则
echo "-w /etc/passwd -p wa -k passwd_changes" >> /etc/audit/rules.d/audit.rules
echo "-w /etc/shadow -p wa -k shadow_changes" >> /etc/audit/rules.d/audit.rules
```

#### 入侵检测
```bash
# 安装AIDE
apt install aide
aide --init
mv /var/lib/aide/aide.db.new /var/lib/aide/aide.db

# 配置定期检查
echo "0 2 * * * root /usr/bin/aide --check" >> /etc/crontab

# 安装fail2ban
apt install fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

## 📋 报告总结

### 1. 事件时间线

#### 时间线重建
```bash
# 合并各种日志创建时间线
cat /var/log/auth.log /var/log/syslog | sort -k1,3 > timeline.log

# 分析关键时间点
grep "Failed password\|Accepted password\|sudo:" /var/log/auth.log | sort
```

### 2. IOC提取

#### 网络IOC
```bash
# 提取恶意IP
netstat -antup | awk '{print $5}' | cut -d: -f1 | sort -u > malicious_ips.txt

# 提取恶意域名
cat /var/log/syslog | grep -oE '[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}' | sort -u > domains.txt
```

#### 文件IOC
```bash
# 计算恶意文件哈希
find /tmp /var/tmp -type f -exec md5sum {} \; > file_hashes.txt

# 提取文件路径
find / -name "*malicious*" 2>/dev/null > malicious_files.txt
```

### 3. 影响评估

#### 数据完整性检查
```bash
# 检查关键文件完整性
aide --check > integrity_report.txt

# 检查数据库完整性
mysqldump --all-databases > database_backup.sql
```

## 📚 工具推荐

### 应急响应工具
- **KAPE** - 证据收集工具
- **Volatility** - 内存分析工具
- **Sleuth Kit** - 磁盘取证工具
- **YARA** - 恶意软件检测规则

### 系统监控工具
- **osquery** - 系统查询框架
- **AIDE** - 文件完整性检查
- **fail2ban** - 入侵防护系统
- **auditd** - Linux审计框架

### 网络分析工具
- **Wireshark** - 网络协议分析
- **tcpdump** - 命令行抓包工具
- **iftop** - 网络流量监控
- **nethogs** - 进程网络使用监控

---

> ⚠️ **重要提醒**: Linux应急响应需要深入了解系统机制，建议在专业人员指导下进行。处置过程中要注意保护证据完整性。
