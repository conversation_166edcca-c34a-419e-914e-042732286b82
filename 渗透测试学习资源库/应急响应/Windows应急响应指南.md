# 🚨 Windows应急响应指南

> **Windows系统入侵检测与应急处置完整流程**

## 📋 目录

- [应急响应概述](#应急响应概述)
- [初步评估](#初步评估)
- [证据收集](#证据收集)
- [入侵分析](#入侵分析)
- [威胁清除](#威胁清除)
- [系统加固](#系统加固)
- [报告总结](#报告总结)

## 🎯 应急响应概述

### 应急响应流程

```mermaid
graph TD
    A[事件发现] --> B[初步评估]
    B --> C[证据收集]
    C --> D[入侵分析]
    D --> E[威胁清除]
    E --> F[系统恢复]
    F --> G[系统加固]
    G --> H[报告总结]
    H --> I[经验总结]
```

### 响应原则

- 🔒 **保护现场** - 最小化对系统的改动
- ⚡ **快速响应** - 及时控制事件扩散
- 📊 **证据保全** - 完整收集分析证据
- 🎯 **精准处置** - 准确识别和清除威胁
- 📝 **详细记录** - 完整记录处置过程

### 常见入侵迹象

- 🖥️ **系统异常** - 运行缓慢、频繁死机、异常重启
- 🌐 **网络异常** - 异常流量、未知连接、DNS劫持
- 📁 **文件异常** - 文件被加密、删除、篡改
- 👤 **账户异常** - 异常登录、权限变化、新增账户
- 🔧 **进程异常** - 未知进程、高CPU占用、异常服务

## 🔍 初步评估

### 1. 事件确认

#### 基础信息收集
```cmd
# 系统基本信息
systeminfo
hostname
whoami
date /t && time /t

# 当前用户和登录信息
query user
qwinsta
net user
net localgroup administrators
```

#### 网络连接检查
```cmd
# 查看网络连接
netstat -ano
netstat -an | findstr ESTABLISHED
netstat -an | findstr LISTENING

# 查看路由表
route print

# 查看ARP表
arp -a

# 查看DNS缓存
ipconfig /displaydns
```

#### 进程检查
```cmd
# 查看运行进程
tasklist
tasklist /svc
wmic process list full

# 查看进程树
wmic process get Name,ParentProcessId,ProcessId,CommandLine

# 查看服务
sc query
net start
wmic service list brief
```

### 2. 威胁评估

#### 系统完整性检查
```cmd
# 检查系统文件完整性
sfc /scannow

# 检查启动项
wmic startup list full
reg query HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run
reg query HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run

# 检查计划任务
schtasks /query /fo LIST /v
```

#### 安全软件状态
```cmd
# 检查Windows Defender状态
Get-MpComputerStatus
Get-MpPreference

# 检查防火墙状态
netsh advfirewall show allprofiles

# 检查更新状态
wuauclt /detectnow
Get-HotFix | Sort-Object InstalledOn -Descending
```

## 📊 证据收集

### 1. 内存取证

#### 内存镜像获取
```cmd
# 使用DumpIt获取内存镜像
DumpIt.exe

# 使用WinPmem获取内存镜像
winpmem_mini_x64.exe physmem.raw

# 使用FTK Imager获取内存镜像
# 通过GUI操作获取内存镜像
```

#### 内存分析
```bash
# 使用Volatility分析内存镜像
volatility -f memory.raw imageinfo
volatility -f memory.raw --profile=Win10x64_19041 pslist
volatility -f memory.raw --profile=Win10x64_19041 psscan
volatility -f memory.raw --profile=Win10x64_19041 netscan
volatility -f memory.raw --profile=Win10x64_19041 malfind
```

### 2. 磁盘取证

#### 磁盘镜像获取
```cmd
# 使用dd命令（需要dd for Windows）
dd if=\\.\PhysicalDrive0 of=E:\disk_image.dd bs=512

# 使用FTK Imager创建磁盘镜像
# 通过GUI操作创建E01格式镜像

# 使用DCFLDD
dcfldd if=\\.\PhysicalDrive0 of=disk_image.dd hash=md5,sha1 bs=512
```

#### 文件系统分析
```bash
# 使用Autopsy分析磁盘镜像
# 导入镜像文件进行分析

# 使用Sleuth Kit分析
fls -r -m C: disk_image.dd > timeline.txt
mactime -b timeline.txt > timeline_readable.txt
```

### 3. 日志收集

#### Windows事件日志
```cmd
# 导出系统日志
wevtutil epl System C:\logs\System.evtx
wevtutil epl Security C:\logs\Security.evtx
wevtutil epl Application C:\logs\Application.evtx

# 查看特定事件
wevtutil qe Security /q:"*[System[EventID=4624]]" /f:text
wevtutil qe Security /q:"*[System[EventID=4625]]" /f:text

# PowerShell查看日志
Get-WinEvent -LogName Security | Where-Object {$_.Id -eq 4624} | Select-Object TimeCreated, Id, LevelDisplayName, Message
```

#### 应用程序日志
```cmd
# IIS日志位置
dir C:\inetpub\logs\LogFiles\W3SVC1\

# Apache日志位置
dir C:\Apache24\logs\

# 数据库日志
# SQL Server: C:\Program Files\Microsoft SQL Server\MSSQL\Log\
# MySQL: C:\ProgramData\MySQL\MySQL Server 8.0\Data\
```

### 4. 网络证据

#### 网络流量捕获
```cmd
# 使用netsh捕获网络流量
netsh trace start capture=yes tracefile=C:\capture.etl provider=Microsoft-Windows-TCPIP

# 停止捕获
netsh trace stop

# 使用Wireshark分析
# 导入capture.etl文件进行分析
```

#### 防火墙日志
```cmd
# 启用防火墙日志
netsh advfirewall set allprofiles logging filename C:\Windows\System32\LogFiles\Firewall\pfirewall.log
netsh advfirewall set allprofiles logging maxfilesize 4096
netsh advfirewall set allprofiles logging droppedconnections enable

# 查看防火墙日志
type C:\Windows\System32\LogFiles\Firewall\pfirewall.log
```

## 🔬 入侵分析

### 1. 恶意软件分析

#### 文件哈希检查
```cmd
# 计算文件哈希
certutil -hashfile suspicious_file.exe MD5
certutil -hashfile suspicious_file.exe SHA1
certutil -hashfile suspicious_file.exe SHA256

# PowerShell计算哈希
Get-FileHash suspicious_file.exe -Algorithm MD5
Get-FileHash suspicious_file.exe -Algorithm SHA1
Get-FileHash suspicious_file.exe -Algorithm SHA256
```

#### 在线查毒
```bash
# VirusTotal API查询
curl -X POST 'https://www.virustotal.com/vtapi/v2/file/scan' \
  --form 'apikey=YOUR_API_KEY' \
  --form 'file=@suspicious_file.exe'

# 或者通过Web界面上传文件
# https://www.virustotal.com/
```

#### 静态分析
```cmd
# 查看文件属性
wmic datafile where name="C:\\path\\to\\file.exe" get Description,Version,Manufacturer

# 查看文件字符串
strings suspicious_file.exe > strings_output.txt

# 查看PE文件信息
PEview.exe suspicious_file.exe
```

### 2. 网络行为分析

#### 连接分析
```cmd
# 分析网络连接
netstat -ano > connections.txt

# 查看进程网络连接
netstat -ano | findstr :80
netstat -ano | findstr :443
netstat -ano | findstr :4444

# 使用TCPView实时监控
TCPView.exe
```

#### DNS分析
```cmd
# 查看DNS缓存
ipconfig /displaydns > dns_cache.txt

# 清空DNS缓存
ipconfig /flushdns

# 分析DNS查询日志
# 需要启用DNS客户端日志
```

### 3. 时间线分析

#### 文件时间线
```cmd
# 查看文件修改时间
forfiles /p C:\ /s /m *.exe /c "cmd /c echo @path @fdate @ftime"

# PowerShell查看文件时间
Get-ChildItem C:\ -Recurse -File | Select-Object FullName, CreationTime, LastWriteTime, LastAccessTime
```

#### 事件时间线
```powershell
# 创建事件时间线
$Events = @()
$Events += Get-WinEvent -LogName Security | Where-Object {$_.Id -eq 4624 -or $_.Id -eq 4625}
$Events += Get-WinEvent -LogName System | Where-Object {$_.Id -eq 7045}
$Events | Sort-Object TimeCreated | Export-Csv timeline.csv
```

### 4. 持久化机制分析

#### 启动项检查
```cmd
# 注册表启动项
reg query HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run
reg query HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run
reg query HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce

# 启动文件夹
dir "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
dir "C:\ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
```

#### 服务检查
```cmd
# 查看可疑服务
sc query type= service state= all
wmic service where "StartMode='Auto'" get Name,PathName,StartMode,State

# 检查服务可执行文件
for /f "tokens=2 delims='='" %a in ('wmic service list full^|find /i "pathname"^|find /i /v "system32"') do @echo %a
```

#### 计划任务检查
```cmd
# 查看计划任务
schtasks /query /fo LIST /v > scheduled_tasks.txt

# 查看任务文件
dir C:\Windows\System32\Tasks /s
```

## 🧹 威胁清除

### 1. 进程终止

#### 终止恶意进程
```cmd
# 根据进程名终止
taskkill /f /im malicious_process.exe

# 根据PID终止
taskkill /f /pid 1234

# 终止进程树
taskkill /f /t /im parent_process.exe
```

#### 服务停止和删除
```cmd
# 停止服务
sc stop "MaliciousService"

# 删除服务
sc delete "MaliciousService"

# 或者使用注册表删除
reg delete HKLM\SYSTEM\CurrentControlSet\Services\MaliciousService /f
```

### 2. 文件清理

#### 删除恶意文件
```cmd
# 删除文件
del /f /q "C:\path\to\malicious_file.exe"

# 删除目录
rmdir /s /q "C:\path\to\malicious_directory"

# 强制删除（如果文件被占用）
handle.exe "C:\path\to\malicious_file.exe"
# 然后终止占用进程再删除
```

#### 清理临时文件
```cmd
# 清理临时目录
del /f /s /q %TEMP%\*
del /f /s /q C:\Windows\Temp\*

# 清理回收站
rd /s /q C:\$Recycle.Bin

# 清理预取文件
del /f /q C:\Windows\Prefetch\*
```

### 3. 注册表清理

#### 清理启动项
```cmd
# 删除恶意启动项
reg delete HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run /v "MaliciousEntry" /f
reg delete HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run /v "MaliciousEntry" /f
```

#### 清理文件关联
```cmd
# 恢复文件关联
reg delete HKCR\.exe /f
reg add HKCR\.exe /ve /d "exefile" /f
```

### 4. 网络清理

#### 断开恶意连接
```cmd
# 查看并终止连接
netstat -ano | findstr :4444
# 记录PID后终止对应进程

# 阻止恶意IP
netsh advfirewall firewall add rule name="Block Malicious IP" dir=out action=block remoteip=*************
```

#### 清理DNS缓存
```cmd
# 清空DNS缓存
ipconfig /flushdns

# 重置网络配置
netsh winsock reset
netsh int ip reset
```

## 🔒 系统加固

### 1. 账户安全

#### 密码策略
```cmd
# 设置密码策略
net accounts /minpwlen:8
net accounts /maxpwage:90
net accounts /minpwage:1
net accounts /uniquepw:5

# 禁用Guest账户
net user guest /active:no

# 重命名Administrator账户
wmic useraccount where name='Administrator' call rename name='NewAdminName'
```

#### 账户审计
```cmd
# 启用登录审计
auditpol /set /category:"Logon/Logoff" /success:enable /failure:enable

# 启用账户管理审计
auditpol /set /category:"Account Management" /success:enable /failure:enable
```

### 2. 系统更新

#### 安装补丁
```cmd
# 检查更新
wuauclt /detectnow
wuauclt /updatenow

# PowerShell安装更新
Install-Module PSWindowsUpdate
Get-WUInstall -AcceptAll -AutoReboot
```

#### 关键补丁
```cmd
# 检查关键安全补丁
wmic qfe where "Description='Security Update'" get HotFixID,InstalledOn
```

### 3. 服务加固

#### 禁用不必要服务
```cmd
# 禁用Telnet服务
sc config tlntsvr start= disabled
sc stop tlntsvr

# 禁用远程注册表服务
sc config RemoteRegistry start= disabled
sc stop RemoteRegistry

# 禁用文件共享服务（如不需要）
sc config lanmanserver start= disabled
sc stop lanmanserver
```

### 4. 防火墙配置

#### 配置防火墙规则
```cmd
# 启用防火墙
netsh advfirewall set allprofiles state on

# 设置默认策略
netsh advfirewall set allprofiles firewallpolicy blockinbound,allowoutbound

# 允许必要服务
netsh advfirewall firewall add rule name="Allow RDP" dir=in action=allow protocol=TCP localport=3389
```

## 📋 报告总结

### 1. 事件总结

#### 报告模板
```markdown
# 安全事件应急响应报告

## 事件概述
- 事件发现时间: 
- 事件类型: 
- 影响范围: 
- 处置状态: 

## 事件详情
### 攻击时间线
- [时间] 事件描述

### 攻击手法
- 初始访问方式: 
- 权限提升方法: 
- 持久化机制: 
- 数据窃取情况: 

### 影响评估
- 受影响系统: 
- 数据泄露情况: 
- 业务影响: 
- 经济损失: 

## 处置措施
### 应急处置
- 威胁清除: 
- 系统恢复: 
- 证据保全: 

### 加固措施
- 系统加固: 
- 安全配置: 
- 监控加强: 

## 经验教训
### 问题分析
- 安全漏洞: 
- 管理缺陷: 
- 技术不足: 

### 改进建议
- 技术改进: 
- 管理改进: 
- 培训需求: 
```

### 2. IOC整理

#### 威胁指标
```yaml
# IOC (Indicators of Compromise)
file_hashes:
  - md5: "d41d8cd98f00b204e9800998ecf8427e"
    sha1: "da39a3ee5e6b4b0d3255bfef95601890afd80709"
    sha256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
    filename: "malicious.exe"

ip_addresses:
  - "*************"
  - "*********"

domains:
  - "malicious-domain.com"
  - "c2-server.evil"

registry_keys:
  - "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\\Malicious"

file_paths:
  - "C:\\Windows\\Temp\\malicious.exe"
  - "C:\\Users\\<USER>\\backdoor.dll"
```

## 📚 工具推荐

### 应急响应工具
- **Volatility** - 内存取证分析
- **Autopsy** - 磁盘取证分析
- **KAPE** - 证据收集工具
- **Eric Zimmerman Tools** - Windows取证工具集

### 恶意软件分析
- **PEview** - PE文件分析
- **Process Monitor** - 进程监控
- **Wireshark** - 网络流量分析
- **Sysinternals Suite** - 系统分析工具集

### 在线资源
- [SANS DFIR](https://www.sans.org/digital-forensics-incident-response/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [MITRE ATT&CK](https://attack.mitre.org/)

---

> ⚠️ **重要提醒**: 应急响应过程中要严格遵循法律法规，确保证据的完整性和合法性。建议在专业人员指导下进行操作。
