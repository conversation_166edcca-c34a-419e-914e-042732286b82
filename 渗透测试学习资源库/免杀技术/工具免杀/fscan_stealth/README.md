# 🛡️ Fscan免杀版本 - 完整使用指南

> **基于fscan-2.0.1的深度免杀改造版本**

## 📋 项目概述

本项目是对原版fscan工具的深度免杀改造，通过多种技术手段实现对主流杀毒软件的绕过，同时保持原有的扫描功能。

### 🎯 主要特性

- ✅ **字符串加密** - 所有敏感字符串均经过AES加密
- ✅ **函数名混淆** - 核心函数名完全重命名
- ✅ **反调试检测** - 自动检测调试环境并退出
- ✅ **动态Banner** - 随机生成Banner避免特征识别
- ✅ **控制流混淆** - 添加虚假控制流干扰分析
- ✅ **多平台支持** - 支持Windows/Linux/macOS交叉编译
- ✅ **配置加密** - 支持配置文件加密存储
- ✅ **隐蔽扫描** - 随机延时和User-Agent轮换

## 🚀 快速开始

### 环境要求

- Go 1.19+
- Git
- UPX (可选，用于压缩)

### 编译步骤

#### Windows环境
```cmd
# 1. 克隆项目
git clone <repository_url>
cd fscan_stealth

# 2. 安装依赖
go mod tidy

# 3. 运行编译脚本
build.bat
```

#### Linux/macOS环境
```bash
# 1. 克隆项目
git clone <repository_url>
cd fscan_stealth

# 2. 安装依赖
go mod tidy

# 3. 设置执行权限并编译
chmod +x build.sh
./build.sh
```

### 编译输出

编译完成后会生成以下文件：
```
scanner_[timestamp]_windows_amd64.exe    # Windows版本
scanner_[timestamp]_linux_amd64          # Linux版本
scanner_[timestamp]_darwin_amd64         # macOS版本
network_tool_[timestamp]_*               # 变种1
security_scanner_[timestamp]_*           # 变种2
hashes_[timestamp].txt                   # 文件哈希
readme_[timestamp].txt                   # 使用说明
```

## 📖 使用方法

### 基本扫描

```bash
# 扫描单个主机
./scanner_linux_amd64 192.168.1.100

# 扫描网段
./scanner_linux_amd64 ***********/24

# 指定端口扫描
./scanner_linux_amd64 -p 80,443,22 192.168.1.100

# 详细输出
./scanner_linux_amd64 -v 192.168.1.100
```

### 高级功能

```bash
# 使用配置文件
./scanner_linux_amd64 -c config.json ***********/24

# 隐蔽扫描模式
./scanner_linux_amd64 -stealth ***********/24

# 输出到文件
./scanner_linux_amd64 -o results.json ***********/24

# 加密输出
./scanner_linux_amd64 -o results.enc -encrypt ***********/24
```

## ⚙️ 配置文件

### 配置文件格式

```json
{
  "scan": {
    "threads": 50,
    "timeout": 3,
    "delay": 100,
    "ports": [21, 22, 23, 25, 53, 80, 443, 3389],
    "services": ["http", "https", "ssh", "ftp"],
    "randomize": true
  },
  "network": {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "proxy_list": [],
    "dns_servers": ["*******", "*******"],
    "max_retries": 3,
    "keep_alive": false
  },
  "output": {
    "format": "json",
    "file": "",
    "encrypt": false,
    "verbose": false,
    "no_color": false
  },
  "security": {
    "anti_debug": true,
    "stealth": true,
    "obfuscation": true,
    "encrypt_comm": false,
    "secret_key": "your_secret_key_here"
  }
}
```

### 环境变量配置

```bash
# 扫描配置
export SCAN_THREADS=100
export SCAN_TIMEOUT=5
export SCAN_DELAY=200
export SCAN_PORTS="80,443,22,21,25"

# 网络配置
export NET_USER_AGENT="Custom User Agent"
export NET_PROXIES="proxy1:8080,proxy2:8080"
export NET_DNS="*******,*******"

# 输出配置
export OUTPUT_FORMAT="json"
export OUTPUT_FILE="scan_results.json"
export OUTPUT_VERBOSE="true"

# 安全配置
export SEC_ANTI_DEBUG="true"
export SEC_STEALTH="true"
export SEC_SECRET_KEY="your_secret_key"
```

## 🧪 免杀测试

### 自动化测试

```bash
# 安装Python依赖
pip3 install requests

# 测试单个文件
python3 test_evasion.py -f scanner_linux_amd64

# 测试整个目录
python3 test_evasion.py -d . -o test_report.json

# 使用VirusTotal API测试
python3 test_evasion.py -f scanner.exe --vt-api-key YOUR_API_KEY
```

### 手动测试

```bash
# 1. 基本功能测试
./scanner_linux_amd64 --help
./scanner_linux_amd64 --version

# 2. 扫描测试
./scanner_linux_amd64 127.0.0.1

# 3. 性能测试
time ./scanner_linux_amd64 ***********/24
```

## 🔧 二次开发

### 添加新的扫描模块

```go
// 1. 实现NetworkProcessor接口
type CustomScanner struct {
    target string
    result map[string]interface{}
}

func (c *CustomScanner) Process(target string) error {
    // 实现扫描逻辑
    return nil
}

func (c *CustomScanner) GetResult() interface{} {
    return c.result
}

func (c *CustomScanner) Cleanup() {
    c.result = make(map[string]interface{})
}

// 2. 注册到插件管理器
scanner.pluginManager.RegisterProcessor("custom", NewCustomScanner())
```

### 修改混淆策略

```go
// 1. 更新加密密钥
var globalKey = []byte("YourNewSecretKey")

// 2. 添加新的字符串混淆
var encryptedStrings = map[string]string{
    "newString": encryptString("your_new_string"),
}

// 3. 修改反调试检测
func customAntiDebug() bool {
    // 添加自定义检测逻辑
    return false
}
```

### 自定义编译参数

```bash
# 修改build.sh中的编译参数
LDFLAGS="-s -w -X main.version=custom -X main.author=yourname"
GCFLAGS="-trimpath -N -l"  # 添加调试信息（仅开发时）

# 添加自定义标签
go build -tags="custom,production" -ldflags="$LDFLAGS" main.go
```

## 📊 性能优化

### 扫描性能调优

```json
{
  "scan": {
    "threads": 200,        // 增加线程数
    "timeout": 1,          // 减少超时时间
    "delay": 50,           // 减少延时
    "randomize": false     // 关闭随机化提高速度
  }
}
```

### 内存优化

```go
// 在config.go中调整缓冲区大小
const (
    MaxBufferSize = 1024 * 1024  // 1MB
    MaxConcurrent = 1000         // 最大并发数
)
```

## 🛡️ 安全注意事项

### 使用限制

1. **仅限授权测试** - 只能在获得明确授权的环境中使用
2. **遵守法律法规** - 严格遵守当地网络安全法律法规
3. **避免误用** - 不得用于非法入侵或恶意攻击
4. **定期更新** - 定期更新编译版本以保持免杀效果

### 检测规避

1. **避免批量使用** - 不要在短时间内大量使用相同版本
2. **定制化编译** - 根据目标环境定制编译参数
3. **混合使用** - 与其他工具混合使用避免单一特征
4. **环境隔离** - 在隔离环境中进行测试

## 🔍 故障排除

### 常见问题

#### 编译失败
```bash
# 检查Go版本
go version

# 清理模块缓存
go clean -modcache
go mod tidy

# 重新编译
go build -v main.go
```

#### 运行时错误
```bash
# 检查文件权限
chmod +x scanner_linux_amd64

# 检查依赖库
ldd scanner_linux_amd64

# 调试模式运行
./scanner_linux_amd64 -debug 127.0.0.1
```

#### 免杀失效
```bash
# 重新编译新版本
./build.sh

# 修改编译参数
export LDFLAGS="-s -w -X main.buildTime=$(date +%s)"

# 使用不同的混淆策略
sed -i 's/globalKey = .*/globalKey = []byte("NewKey123456")/' main.go
```

## 📚 参考资源

### 相关文档
- [Go语言官方文档](https://golang.org/doc/)
- [免杀技术研究](../免杀技术/)
- [渗透测试工具](../../工具与环境/)

### 工具推荐
- **UPX** - 可执行文件压缩
- **Garble** - Go代码混淆器
- **GoReleaser** - 自动化发布工具

### 学习资源
- [免杀技术详解](../免杀加载器开发.md)
- [Go语言安全编程](https://github.com/securego/gosec)
- [恶意软件分析](https://malware.news/)

## 📄 许可证

本项目仅供安全研究和教育用途，使用者需自行承担使用风险。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

---

> ⚠️ **重要提醒**: 本工具仅供安全研究和授权测试使用，请遵守相关法律法规，不得用于非法用途！

**项目维护者**: Security Research Team  
**最后更新**: 2025-07-28  
**版本**: 1.0.0
