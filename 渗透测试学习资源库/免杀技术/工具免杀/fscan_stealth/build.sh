#!/bin/bash

# Fscan免杀版本编译脚本 (Linux/macOS)
# 作者: Security Researcher
# 版本: 1.0

echo "========================================"
echo "     Fscan Stealth Version Builder"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        log_error "Go编译器未找到，请先安装Go"
        exit 1
    fi
    
    go_version=$(go version | awk '{print $3}')
    log_info "Go版本: $go_version"
}

# 设置编译环境
setup_build_env() {
    export CGO_ENABLED=0
    
    # 获取时间戳
    timestamp=$(date +%Y%m%d%H%M%S)
    
    log_info "编译时间戳: $timestamp"
    echo
}

# 编译函数
build_variant() {
    local variant_name=$1
    local output_name=$2
    local version=$3
    local variant_type=$4
    
    local ldflags="-s -w -X main.version=$version -X main.buildTime=$timestamp"
    if [ ! -z "$variant_type" ]; then
        ldflags="$ldflags -X main.variant=$variant_type"
    fi
    
    local gcflags="-trimpath"
    
    log_info "编译 $variant_name..."
    
    # Windows版本
    GOOS=windows GOARCH=amd64 go build -ldflags="$ldflags" -gcflags="$gcflags" -o "${output_name}_windows_amd64.exe" main.go
    if [ $? -eq 0 ]; then
        log_success "Windows版本编译完成: ${output_name}_windows_amd64.exe"
    else
        log_error "Windows版本编译失败"
        return 1
    fi
    
    # Linux版本
    GOOS=linux GOARCH=amd64 go build -ldflags="$ldflags" -gcflags="$gcflags" -o "${output_name}_linux_amd64" main.go
    if [ $? -eq 0 ]; then
        log_success "Linux版本编译完成: ${output_name}_linux_amd64"
    else
        log_error "Linux版本编译失败"
        return 1
    fi
    
    # macOS版本
    GOOS=darwin GOARCH=amd64 go build -ldflags="$ldflags" -gcflags="$gcflags" -o "${output_name}_darwin_amd64" main.go
    if [ $? -eq 0 ]; then
        log_success "macOS版本编译完成: ${output_name}_darwin_amd64"
    else
        log_error "macOS版本编译失败"
        return 1
    fi
    
    echo
}

# 压缩文件
compress_files() {
    log_info "检查UPX压缩工具..."
    
    if command -v upx &> /dev/null; then
        log_info "发现UPX，开始压缩文件..."
        
        for file in scanner_*_amd64.exe network_tool_*_amd64.exe security_scanner_*_amd64.exe; do
            if [ -f "$file" ]; then
                log_info "压缩 $file..."
                upx --best "$file" 2>/dev/null
                if [ $? -eq 0 ]; then
                    log_success "$file 压缩完成"
                else
                    log_warning "$file 压缩失败"
                fi
            fi
        done
    else
        log_warning "未找到UPX压缩工具，跳过压缩步骤"
        log_info "可以通过以下命令安装UPX:"
        log_info "  Ubuntu/Debian: sudo apt-get install upx"
        log_info "  CentOS/RHEL: sudo yum install upx"
        log_info "  macOS: brew install upx"
    fi
    echo
}

# 生成哈希值
generate_hashes() {
    log_info "生成文件哈希..."
    
    hash_file="hashes_$timestamp.txt"
    
    {
        echo "文件哈希信息"
        echo "===================="
        echo "生成时间: $(date)"
        echo
        
        for file in scanner_* network_tool_* security_scanner_*; do
            if [ -f "$file" ]; then
                echo "文件: $file"
                echo "大小: $(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null) bytes"
                
                if command -v md5sum &> /dev/null; then
                    echo "MD5: $(md5sum "$file" | awk '{print $1}')"
                elif command -v md5 &> /dev/null; then
                    echo "MD5: $(md5 -q "$file")"
                fi
                
                if command -v sha1sum &> /dev/null; then
                    echo "SHA1: $(sha1sum "$file" | awk '{print $1}')"
                elif command -v shasum &> /dev/null; then
                    echo "SHA1: $(shasum -a 1 "$file" | awk '{print $1}')"
                fi
                
                if command -v sha256sum &> /dev/null; then
                    echo "SHA256: $(sha256sum "$file" | awk '{print $1}')"
                elif command -v shasum &> /dev/null; then
                    echo "SHA256: $(shasum -a 256 "$file" | awk '{print $1}')"
                fi
                
                echo
            fi
        done
    } > "$hash_file"
    
    log_success "哈希文件生成完成: $hash_file"
    echo
}

# 生成使用说明
generate_readme() {
    log_info "生成使用说明..."
    
    readme_file="readme_$timestamp.txt"
    
    {
        echo "免杀版本使用说明"
        echo "=================="
        echo
        echo "编译时间: $timestamp"
        echo "编译平台: 多平台交叉编译"
        echo
        echo "生成的文件:"
        echo "Windows版本:"
        ls scanner_*_windows_amd64.exe network_tool_*_windows_amd64.exe security_scanner_*_windows_amd64.exe 2>/dev/null | sed 's/^/  - /'
        echo
        echo "Linux版本:"
        ls scanner_*_linux_amd64 network_tool_*_linux_amd64 security_scanner_*_linux_amd64 2>/dev/null | sed 's/^/  - /'
        echo
        echo "macOS版本:"
        ls scanner_*_darwin_amd64 network_tool_*_darwin_amd64 security_scanner_*_darwin_amd64 2>/dev/null | sed 's/^/  - /'
        echo
        echo "使用方法:"
        echo "  ./scanner_linux_amd64 [目标IP]"
        echo "  ./scanner_darwin_amd64 [目标IP]"
        echo "  scanner_windows_amd64.exe [目标IP]"
        echo
        echo "注意事项:"
        echo "1. 仅在授权环境中使用"
        echo "2. 定期更新编译版本"
        echo "3. 避免在生产环境中测试"
        echo "4. 遵守相关法律法规"
        echo
        echo "免杀特性:"
        echo "- 字符串加密"
        echo "- 函数名混淆"
        echo "- 反调试检测"
        echo "- 动态Banner生成"
        echo "- 控制流混淆"
        echo "- 多平台支持"
        echo
        echo "技术支持:"
        echo "如有问题请查看源码注释或联系开发者"
    } > "$readme_file"
    
    log_success "使用说明生成完成: $readme_file"
    echo
}

# 显示编译统计
show_build_stats() {
    echo "========================================"
    echo "           编译完成统计"
    echo "========================================"
    echo
    
    total_files=0
    total_size=0
    
    for file in scanner_* network_tool_* security_scanner_*; do
        if [ -f "$file" ]; then
            size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            echo "文件: $file"
            echo "大小: $size bytes ($(echo "scale=2; $size/1024/1024" | bc 2>/dev/null || echo "N/A") MB)"
            echo
            
            total_files=$((total_files + 1))
            total_size=$((total_size + size))
        fi
    done
    
    echo "总计: $total_files 个文件, $total_size bytes"
    echo
}

# 基本功能测试
test_functionality() {
    echo -n "是否进行基本功能测试? (y/n): "
    read test_choice
    
    if [ "$test_choice" = "y" ] || [ "$test_choice" = "Y" ]; then
        echo
        log_info "开始基本功能测试..."
        
        # 测试Linux版本（如果存在）
        linux_binary=$(ls scanner_*_linux_amd64 2>/dev/null | head -1)
        if [ -f "$linux_binary" ]; then
            chmod +x "$linux_binary"
            if ./"$linux_binary" --help >/dev/null 2>&1; then
                log_success "Linux版本基本功能测试通过"
            else
                log_warning "Linux版本基本功能测试异常"
            fi
        fi
        
        # 测试macOS版本（如果在macOS上且存在）
        if [ "$(uname)" = "Darwin" ]; then
            macos_binary=$(ls scanner_*_darwin_amd64 2>/dev/null | head -1)
            if [ -f "$macos_binary" ]; then
                chmod +x "$macos_binary"
                if ./"$macos_binary" --help >/dev/null 2>&1; then
                    log_success "macOS版本基本功能测试通过"
                else
                    log_warning "macOS版本基本功能测试异常"
                fi
            fi
        fi
        
        echo
    fi
}

# 主函数
main() {
    # 检查Go环境
    check_go
    
    # 设置编译环境
    setup_build_env
    
    # 编译不同变种
    log_info "开始编译免杀版本..."
    echo
    
    build_variant "主程序" "scanner_$timestamp" "2.1.$timestamp" ""
    build_variant "变种1" "network_tool_$timestamp" "1.5.$timestamp" "alpha"
    build_variant "变种2" "security_scanner_$timestamp" "3.0.$timestamp" "beta"
    
    # 压缩文件
    compress_files
    
    # 生成哈希值
    generate_hashes
    
    # 生成使用说明
    generate_readme
    
    # 显示统计信息
    show_build_stats
    
    # 基本功能测试
    test_functionality
    
    echo "========================================"
    echo "     编译脚本执行完成"
    echo "========================================"
    echo
    log_success "所有文件已生成在当前目录"
    log_info "请查看 readme_$timestamp.txt 了解使用方法"
    echo
    log_warning "提醒: 请在合法授权的环境中使用这些工具！"
    echo
}

# 执行主函数
main "$@"
