@echo off
REM Fscan免杀版本编译脚本
REM 作者: Security Researcher
REM 版本: 1.0

echo ========================================
echo     Fscan Stealth Version Builder
echo ========================================
echo.

REM 设置编译环境
set CGO_ENABLED=0
set GOOS=windows
set GOARCH=amd64

REM 获取当前时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%%HH%%Min%%Sec%"

REM 设置编译参数
set "LDFLAGS=-s -w -X main.version=2.1.%timestamp% -X main.buildTime=%timestamp%"
set "GCFLAGS=-trimpath"

echo [INFO] 开始编译免杀版本...
echo [INFO] 目标平台: %GOOS%/%GOARCH%
echo [INFO] 编译时间: %timestamp%
echo.

REM 编译主程序
echo [STEP 1] 编译主程序...
go build -ldflags="%LDFLAGS%" -gcflags="%GCFLAGS%" -o scanner_%timestamp%.exe main.go

if %ERRORLEVEL% neq 0 (
    echo [ERROR] 编译失败！
    pause
    exit /b 1
)

echo [SUCCESS] 主程序编译完成: scanner_%timestamp%.exe
echo.

REM 检查UPX是否存在
echo [STEP 2] 检查压缩工具...
where upx >nul 2>nul
if %ERRORLEVEL% equ 0 (
    echo [INFO] 发现UPX，开始压缩...
    upx --best scanner_%timestamp%.exe
    if %ERRORLEVEL% equ 0 (
        echo [SUCCESS] 文件压缩完成
    ) else (
        echo [WARNING] 文件压缩失败，但不影响使用
    )
) else (
    echo [WARNING] 未找到UPX压缩工具，跳过压缩步骤
)
echo.

REM 生成多个变种
echo [STEP 3] 生成变种版本...

REM 变种1: 修改随机种子
set "LDFLAGS_V1=-s -w -X main.version=1.5.%timestamp% -X main.buildTime=%timestamp% -X main.variant=alpha"
go build -ldflags="%LDFLAGS_V1%" -gcflags="%GCFLAGS%" -o network_tool_%timestamp%.exe main.go

REM 变种2: 不同的版本号
set "LDFLAGS_V2=-s -w -X main.version=3.0.%timestamp% -X main.buildTime=%timestamp% -X main.variant=beta"
go build -ldflags="%LDFLAGS_V2%" -gcflags="%GCFLAGS%" -o security_scanner_%timestamp%.exe main.go

echo [SUCCESS] 变种版本生成完成
echo.

REM 生成哈希值
echo [STEP 4] 生成文件哈希...
echo 文件哈希信息: > hashes_%timestamp%.txt
echo ==================== >> hashes_%timestamp%.txt

for %%f in (scanner_%timestamp%.exe network_tool_%timestamp%.exe security_scanner_%timestamp%.exe) do (
    if exist %%f (
        echo 文件: %%f >> hashes_%timestamp%.txt
        certutil -hashfile %%f MD5 | findstr /v ":" >> hashes_%timestamp%.txt
        certutil -hashfile %%f SHA1 | findstr /v ":" >> hashes_%timestamp%.txt
        certutil -hashfile %%f SHA256 | findstr /v ":" >> hashes_%timestamp%.txt
        echo. >> hashes_%timestamp%.txt
    )
)

echo [SUCCESS] 哈希文件生成完成: hashes_%timestamp%.txt
echo.

REM 创建使用说明
echo [STEP 5] 生成使用说明...
(
echo 免杀版本使用说明
echo ==================
echo.
echo 编译时间: %timestamp%
echo 编译平台: %GOOS%/%GOARCH%
echo.
echo 生成的文件:
echo - scanner_%timestamp%.exe        ^(主程序^)
echo - network_tool_%timestamp%.exe   ^(变种1^)
echo - security_scanner_%timestamp%.exe ^(变种2^)
echo - hashes_%timestamp%.txt         ^(文件哈希^)
echo.
echo 使用方法:
echo scanner_%timestamp%.exe [目标IP]
echo.
echo 注意事项:
echo 1. 仅在授权环境中使用
echo 2. 定期更新编译版本
echo 3. 避免在生产环境中测试
echo 4. 遵守相关法律法规
echo.
echo 免杀特性:
echo - 字符串加密
echo - 函数名混淆
echo - 反调试检测
echo - 动态Banner生成
echo - 控制流混淆
) > readme_%timestamp%.txt

echo [SUCCESS] 使用说明生成完成: readme_%timestamp%.txt
echo.

REM 显示编译结果
echo ========================================
echo           编译完成统计
echo ========================================
echo.

for %%f in (scanner_%timestamp%.exe network_tool_%timestamp%.exe security_scanner_%timestamp%.exe) do (
    if exist %%f (
        echo 文件: %%f
        for %%s in (%%f) do echo 大小: %%~zs bytes
        echo.
    )
)

echo [INFO] 所有文件已生成在当前目录
echo [INFO] 请查看 readme_%timestamp%.txt 了解使用方法
echo.

REM 可选：自动测试
set /p test_choice="是否进行基本功能测试? (y/n): "
if /i "%test_choice%"=="y" (
    echo.
    echo [TEST] 开始基本功能测试...
    scanner_%timestamp%.exe --help
    if %ERRORLEVEL% equ 0 (
        echo [SUCCESS] 基本功能测试通过
    ) else (
        echo [WARNING] 基本功能测试异常
    )
)

echo.
echo ========================================
echo     编译脚本执行完成
echo ========================================
echo.
echo 提醒: 请在合法授权的环境中使用这些工具！
echo.
pause
