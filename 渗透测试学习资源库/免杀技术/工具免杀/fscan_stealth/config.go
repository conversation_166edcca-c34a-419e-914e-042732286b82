package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"time"
)

// 配置结构体
type Config struct {
	Scan     <PERSON>anConfig     `json:"scan"`
	Network  NetworkConfig  `json:"network"`
	Output   OutputConfig   `json:"output"`
	Security SecurityConfig `json:"security"`
}

type ScanConfig struct {
	Threads    int      `json:"threads"`
	Timeout    int      `json:"timeout"`
	Delay      int      `json:"delay"`
	Ports      []int    `json:"ports"`
	Services   []string `json:"services"`
	Randomize  bool     `json:"randomize"`
}

type NetworkConfig struct {
	UserAgent   string   `json:"user_agent"`
	ProxyList   []string `json:"proxy_list"`
	DNSServers  []string `json:"dns_servers"`
	MaxRetries  int      `json:"max_retries"`
	KeepAlive   bool     `json:"keep_alive"`
}

type OutputConfig struct {
	Format    string `json:"format"`
	File      string `json:"file"`
	Encrypt   bool   `json:"encrypt"`
	Verbose   bool   `json:"verbose"`
	NoColor   bool   `json:"no_color"`
}

type SecurityConfig struct {
	AntiDebug    bool   `json:"anti_debug"`
	Stealth      bool   `json:"stealth"`
	Obfuscation  bool   `json:"obfuscation"`
	EncryptComm  bool   `json:"encrypt_comm"`
	SecretKey    string `json:"secret_key"`
}

// 默认配置
func getDefaultConfig() *Config {
	return &Config{
		Scan: ScanConfig{
			Threads:   50,
			Timeout:   3,
			Delay:     100,
			Ports:     []int{21, 22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 993, 995, 1433, 3306, 3389, 5432, 5900, 6379, 8080},
			Services:  []string{"http", "https", "ssh", "ftp", "smtp", "mysql", "mssql", "redis", "vnc"},
			Randomize: true,
		},
		Network: NetworkConfig{
			UserAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			ProxyList:  []string{},
			DNSServers: []string{"8.8.8.8", "1.1.1.1"},
			MaxRetries: 3,
			KeepAlive:  false,
		},
		Output: OutputConfig{
			Format:  "json",
			File:    "",
			Encrypt: false,
			Verbose: false,
			NoColor: false,
		},
		Security: SecurityConfig{
			AntiDebug:   true,
			Stealth:     true,
			Obfuscation: true,
			EncryptComm: false,
			SecretKey:   generateRandomKey(),
		},
	}
}

// 生成随机密钥
func generateRandomKey() string {
	key := make([]byte, 32)
	rand.Read(key)
	return base64.StdEncoding.EncodeToString(key)
}

// 加密配置文件
func encryptConfig(config *Config, password string) ([]byte, error) {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return nil, err
	}

	key := []byte(password)
	if len(key) < 32 {
		// 填充密钥到32字节
		padded := make([]byte, 32)
		copy(padded, key)
		key = padded
	} else if len(key) > 32 {
		key = key[:32]
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// 解密配置文件
func decryptConfig(encryptedData []byte, password string) (*Config, error) {
	key := []byte(password)
	if len(key) < 32 {
		padded := make([]byte, 32)
		copy(padded, key)
		key = padded
	} else if len(key) > 32 {
		key = key[:32]
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, err
	}

	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	var config Config
	err = json.Unmarshal(plaintext, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// 保存配置文件
func saveConfig(config *Config, filename string, encrypt bool, password string) error {
	var data []byte
	var err error

	if encrypt && password != "" {
		data, err = encryptConfig(config, password)
		if err != nil {
			return err
		}
	} else {
		data, err = json.MarshalIndent(config, "", "  ")
		if err != nil {
			return err
		}
	}

	return ioutil.WriteFile(filename, data, 0600)
}

// 加载配置文件
func loadConfig(filename string, password string) (*Config, error) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		// 如果配置文件不存在，返回默认配置
		return getDefaultConfig(), nil
	}

	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	// 尝试作为加密文件解析
	if password != "" {
		config, err := decryptConfig(data, password)
		if err == nil {
			return config, nil
		}
	}

	// 尝试作为普通JSON文件解析
	var config Config
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

// 从环境变量加载配置
func loadConfigFromEnv(config *Config) {
	// 扫描配置
	if threads := os.Getenv("SCAN_THREADS"); threads != "" {
		if t, err := strconv.Atoi(threads); err == nil {
			config.Scan.Threads = t
		}
	}

	if timeout := os.Getenv("SCAN_TIMEOUT"); timeout != "" {
		if t, err := strconv.Atoi(timeout); err == nil {
			config.Scan.Timeout = t
		}
	}

	if delay := os.Getenv("SCAN_DELAY"); delay != "" {
		if d, err := strconv.Atoi(delay); err == nil {
			config.Scan.Delay = d
		}
	}

	if ports := os.Getenv("SCAN_PORTS"); ports != "" {
		var portList []int
		for _, p := range strings.Split(ports, ",") {
			if port, err := strconv.Atoi(strings.TrimSpace(p)); err == nil {
				portList = append(portList, port)
			}
		}
		if len(portList) > 0 {
			config.Scan.Ports = portList
		}
	}

	// 网络配置
	if userAgent := os.Getenv("NET_USER_AGENT"); userAgent != "" {
		config.Network.UserAgent = userAgent
	}

	if proxies := os.Getenv("NET_PROXIES"); proxies != "" {
		config.Network.ProxyList = strings.Split(proxies, ",")
	}

	if dns := os.Getenv("NET_DNS"); dns != "" {
		config.Network.DNSServers = strings.Split(dns, ",")
	}

	// 输出配置
	if format := os.Getenv("OUTPUT_FORMAT"); format != "" {
		config.Output.Format = format
	}

	if file := os.Getenv("OUTPUT_FILE"); file != "" {
		config.Output.File = file
	}

	if verbose := os.Getenv("OUTPUT_VERBOSE"); verbose == "true" {
		config.Output.Verbose = true
	}

	// 安全配置
	if antiDebug := os.Getenv("SEC_ANTI_DEBUG"); antiDebug == "false" {
		config.Security.AntiDebug = false
	}

	if stealth := os.Getenv("SEC_STEALTH"); stealth == "false" {
		config.Security.Stealth = false
	}

	if secretKey := os.Getenv("SEC_SECRET_KEY"); secretKey != "" {
		config.Security.SecretKey = secretKey
	}
}

// 验证配置
func validateConfig(config *Config) error {
	// 验证扫描配置
	if config.Scan.Threads <= 0 || config.Scan.Threads > 1000 {
		config.Scan.Threads = 50
	}

	if config.Scan.Timeout <= 0 || config.Scan.Timeout > 60 {
		config.Scan.Timeout = 3
	}

	if config.Scan.Delay < 0 || config.Scan.Delay > 10000 {
		config.Scan.Delay = 100
	}

	// 验证端口列表
	if len(config.Scan.Ports) == 0 {
		config.Scan.Ports = []int{80, 443, 22, 21, 25, 53, 110, 143, 993, 995}
	}

	// 验证网络配置
	if config.Network.UserAgent == "" {
		config.Network.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	}

	if config.Network.MaxRetries <= 0 || config.Network.MaxRetries > 10 {
		config.Network.MaxRetries = 3
	}

	// 验证输出格式
	validFormats := []string{"json", "xml", "csv", "txt"}
	isValidFormat := false
	for _, format := range validFormats {
		if config.Output.Format == format {
			isValidFormat = true
			break
		}
	}
	if !isValidFormat {
		config.Output.Format = "json"
	}

	return nil
}

// 配置管理器
type ConfigManager struct {
	config   *Config
	filename string
	password string
}

// 创建配置管理器
func NewConfigManager(filename, password string) *ConfigManager {
	return &ConfigManager{
		filename: filename,
		password: password,
	}
}

// 加载配置
func (cm *ConfigManager) Load() error {
	config, err := loadConfig(cm.filename, cm.password)
	if err != nil {
		return err
	}

	// 从环境变量加载配置
	loadConfigFromEnv(config)

	// 验证配置
	err = validateConfig(config)
	if err != nil {
		return err
	}

	cm.config = config
	return nil
}

// 保存配置
func (cm *ConfigManager) Save() error {
	if cm.config == nil {
		return nil
	}

	encrypt := cm.password != ""
	return saveConfig(cm.config, cm.filename, encrypt, cm.password)
}

// 获取配置
func (cm *ConfigManager) GetConfig() *Config {
	if cm.config == nil {
		cm.config = getDefaultConfig()
	}
	return cm.config
}

// 更新配置
func (cm *ConfigManager) UpdateConfig(config *Config) {
	cm.config = config
}

// 重置为默认配置
func (cm *ConfigManager) Reset() {
	cm.config = getDefaultConfig()
}

// 获取扫描延时（带随机化）
func (cm *ConfigManager) GetScanDelay() time.Duration {
	config := cm.GetConfig()
	delay := time.Duration(config.Scan.Delay) * time.Millisecond

	if config.Scan.Randomize {
		// 添加随机延时（±50%）
		randomFactor := 0.5 + rand.Float64() // 0.5 到 1.5
		delay = time.Duration(float64(delay) * randomFactor)
	}

	return delay
}

// 获取随机User-Agent
func (cm *ConfigManager) GetRandomUserAgent() string {
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
	}

	config := cm.GetConfig()
	if config.Network.UserAgent != "" {
		userAgents = append([]string{config.Network.UserAgent}, userAgents...)
	}

	return userAgents[rand.Intn(len(userAgents))]
}
