package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"math/rand"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"
)

// 加密密钥 - 在实际使用中应该动态生成
var globalKey = []byte("MySecretKey12345")

// 加密后的敏感字符串
var encryptedStrings = map[string]string{
	"toolName":    "8f2a7b9c1d3e4f5a6b7c8d9e0f1a2b3c",
	"version":     "1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d",
	"banner":      "2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e",
	"exploitName": "3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f",
}

// 字符串解密函数
func decryptString(encrypted string) string {
	data, err := hex.DecodeString(encrypted)
	if err != nil {
		return ""
	}

	block, err := aes.NewCipher(globalKey)
	if err != nil {
		return ""
	}

	if len(data) < aes.BlockSize {
		return ""
	}

	iv := data[:aes.BlockSize]
	data = data[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(data, data)

	return string(data)
}

// 字符串加密函数（用于生成加密字符串）
func encryptString(plaintext string) string {
	block, err := aes.NewCipher(globalKey)
	if err != nil {
		return ""
	}

	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return ""
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], []byte(plaintext))

	return hex.EncodeToString(ciphertext)
}

// 动态字符串构造
func buildString(parts ...string) string {
	var result strings.Builder
	for _, part := range parts {
		result.WriteString(part)
	}
	return result.String()
}

// 获取工具名称
func getToolName() string {
	return buildString("N", "e", "t", "w", "o", "r", "k", " ", "S", "c", "a", "n", "n", "e", "r")
}

// 获取版本信息
func getVersionInfo() string {
	versions := []string{
		"Network Scanner v2.1.0",
		"Security Tool v1.5.3",
		"Port Scanner v3.0.1",
		"System Analyzer v2.0.5",
	}
	return versions[rand.Intn(len(versions))]
}

// 反调试检测
func isDebugging() bool {
	if runtime.GOOS == "windows" {
		return isWindowsDebugging()
	}
	return isUnixDebugging()
}

func isWindowsDebugging() bool {
	start := time.Now()
	time.Sleep(time.Millisecond * 10)
	elapsed := time.Since(start)

	// 如果睡眠时间异常，可能被调试
	if elapsed > time.Millisecond*50 {
		return true
	}

	// 检查父进程
	ppid := os.Getppid()
	if ppid == 1 {
		return true
	}

	return false
}

func isUnixDebugging() bool {
	if os.Getenv("TERM") == "" {
		return true
	}
	return false
}

// 反调试主函数
func antiDebug() {
	if isDebugging() {
		fmt.Println("System check completed.")
		os.Exit(0)
	}
}

// 混淆的Banner生成
func generateBanner() {
	bannerVariants := [][]string{
		{
			"   ___                              _    ",
			"  / _ \\     ___  ___ _ __ __ _  ___| | __ ",
			" / /_\\/____/ __|/ __| '__/ _` |/ __| |/ /",
			"/ /_\\\\_____\\__ \\ (__| | | (_| | (__|   <    ",
			"\\____/     |___/\\___|_|  \\__,_|\\___|_|\\_\\   ",
		},
		{
			"╔═══════════════════════════════════════╗",
			"║          Network Security Tool        ║",
			"║              Version 2.0              ║",
			"╚═══════════════════════════════════════╝",
		},
	}

	variant := bannerVariants[rand.Intn(len(bannerVariants))]
	for _, line := range variant {
		fmt.Println(line)
	}
	fmt.Printf("      %s\n\n", getVersionInfo())
}

// 网络扫描器接口
type NetworkProcessor interface {
	Process(target string) error
	GetResult() interface{}
	Cleanup()
}

// 系统分析器（原MS17010扫描器的混淆版本）
type SystemAnalyzer struct {
	target string
	result map[string]interface{}
	mutex  sync.RWMutex
}

func NewSystemAnalyzer() *SystemAnalyzer {
	return &SystemAnalyzer{
		result: make(map[string]interface{}),
	}
}

func (s *SystemAnalyzer) Process(target string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.target = target
	
	// 添加虚假操作混淆真实意图
	dummy := rand.Intn(100)
	if dummy > 50 {
		_ = fmt.Sprintf("processing %s", target)
	}

	// 实际的扫描逻辑（简化版本）
	s.result["target"] = target
	s.result["status"] = "analyzed"
	s.result["timestamp"] = time.Now().Unix()

	// 模拟网络延时
	time.Sleep(time.Millisecond * time.Duration(rand.Intn(100)+50))

	return nil
}

func (s *SystemAnalyzer) GetResult() interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.result
}

func (s *SystemAnalyzer) Cleanup() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.result = make(map[string]interface{})
}

// 连接池处理器（原SmbGhost扫描器的混淆版本）
type ConnectionPoolHandler struct {
	target     string
	result     map[string]interface{}
	maxRetries int
}

func NewConnectionPoolHandler() *ConnectionPoolHandler {
	return &ConnectionPoolHandler{
		result:     make(map[string]interface{}),
		maxRetries: 3,
	}
}

func (c *ConnectionPoolHandler) Process(target string) error {
	c.target = target

	// 虚假的重试逻辑
	for i := 0; i < c.maxRetries; i++ {
		if rand.Intn(10) > 7 { // 30%概率"失败"
			continue
		}
		break
	}

	c.result["target"] = target
	c.result["connection_status"] = "established"
	c.result["pool_size"] = rand.Intn(10) + 1

	return nil
}

func (c *ConnectionPoolHandler) GetResult() interface{} {
	return c.result
}

func (c *ConnectionPoolHandler) Cleanup() {
	c.result = make(map[string]interface{})
}

// 资源信息获取器（原WebTitle扫描器的混淆版本）
type ResourceInfoFetcher struct {
	target   string
	result   map[string]interface{}
	userAgent string
}

func NewResourceInfoFetcher() *ResourceInfoFetcher {
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
	}

	return &ResourceInfoFetcher{
		result:    make(map[string]interface{}),
		userAgent: userAgents[rand.Intn(len(userAgents))],
	}
}

func (r *ResourceInfoFetcher) Process(target string) error {
	r.target = target

	// 模拟HTTP请求
	r.result["target"] = target
	r.result["user_agent"] = r.userAgent
	r.result["response_code"] = 200
	r.result["content_length"] = rand.Intn(10000) + 1000

	return nil
}

func (r *ResourceInfoFetcher) GetResult() interface{} {
	return r.result
}

func (r *ResourceInfoFetcher) Cleanup() {
	r.result = make(map[string]interface{})
}

// 插件管理器
type PluginManager struct {
	processors map[string]NetworkProcessor
	mutex      sync.RWMutex
}

func NewPluginManager() *PluginManager {
	return &PluginManager{
		processors: make(map[string]NetworkProcessor),
	}
}

func (pm *PluginManager) RegisterProcessor(name string, processor NetworkProcessor) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 使用哈希值作为key，避免明文插件名
	hasher := sha256.New()
	hasher.Write([]byte(name))
	key := hex.EncodeToString(hasher.Sum(nil))[:16]

	pm.processors[key] = processor
}

func (pm *PluginManager) GetProcessor(name string) (NetworkProcessor, bool) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	hasher := sha256.New()
	hasher.Write([]byte(name))
	key := hex.EncodeToString(hasher.Sum(nil))[:16]

	processor, exists := pm.processors[key]
	return processor, exists
}

func (pm *PluginManager) ListProcessors() []string {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	var keys []string
	for key := range pm.processors {
		keys = append(keys, key)
	}
	return keys
}

// 主扫描器
type StealthScanner struct {
	pluginManager *PluginManager
	delay         time.Duration
	randomize     bool
}

func NewStealthScanner() *StealthScanner {
	scanner := &StealthScanner{
		pluginManager: NewPluginManager(),
		delay:         time.Second * 2,
		randomize:     true,
	}

	// 注册处理器
	scanner.pluginManager.RegisterProcessor("system", NewSystemAnalyzer())
	scanner.pluginManager.RegisterProcessor("connection", NewConnectionPoolHandler())
	scanner.pluginManager.RegisterProcessor("resource", NewResourceInfoFetcher())

	return scanner
}

func (s *StealthScanner) ScanTarget(target string) {
	fmt.Printf("Scanning target: %s\n", target)

	processors := []string{"system", "connection", "resource"}

	for _, processorName := range processors {
		if s.randomize {
			randomDelay := time.Duration(rand.Intn(3)+1) * time.Second
			time.Sleep(randomDelay)
		} else {
			time.Sleep(s.delay)
		}

		processor, exists := s.pluginManager.GetProcessor(processorName)
		if !exists {
			continue
		}

		err := processor.Process(target)
		if err != nil {
			fmt.Printf("Error processing %s: %v\n", target, err)
			continue
		}

		result := processor.GetResult()
		fmt.Printf("Result from %s: %v\n", processorName, result)

		processor.Cleanup()
	}
}

func main() {
	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())

	// 反调试检测
	antiDebug()

	// 显示Banner
	generateBanner()

	// 创建扫描器
	scanner := NewStealthScanner()

	// 示例扫描目标
	targets := []string{
		"192.168.1.1",
		"192.168.1.100",
		"example.com",
	}

	fmt.Printf("Starting %s...\n\n", getToolName())

	for _, target := range targets {
		scanner.ScanTarget(target)
		fmt.Println(strings.Repeat("-", 50))
	}

	fmt.Println("Scan completed.")
}
