#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Fscan免杀效果测试脚本
作者: Security Researcher
版本: 1.0
功能: 自动化测试编译后的免杀效果
"""

import os
import sys
import time
import hashlib
import requests
import subprocess
import json
import argparse
from pathlib import Path
from typing import List, Dict, Optional

class Colors:
    """终端颜色定义"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class Logger:
    """日志记录器"""
    
    @staticmethod
    def info(message: str):
        print(f"{Colors.BLUE}[INFO]{Colors.END} {message}")
    
    @staticmethod
    def success(message: str):
        print(f"{Colors.GREEN}[SUCCESS]{Colors.END} {message}")
    
    @staticmethod
    def warning(message: str):
        print(f"{Colors.YELLOW}[WARNING]{Colors.END} {message}")
    
    @staticmethod
    def error(message: str):
        print(f"{Colors.RED}[ERROR]{Colors.END} {message}")
    
    @staticmethod
    def debug(message: str):
        print(f"{Colors.PURPLE}[DEBUG]{Colors.END} {message}")

class FileAnalyzer:
    """文件分析器"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.file_size = 0
        self.file_hash = {}
        
    def analyze(self) -> Dict:
        """分析文件基本信息"""
        if not self.file_path.exists():
            raise FileNotFoundError(f"文件不存在: {self.file_path}")
        
        # 获取文件大小
        self.file_size = self.file_path.stat().st_size
        
        # 计算文件哈希
        with open(self.file_path, 'rb') as f:
            content = f.read()
            self.file_hash = {
                'md5': hashlib.md5(content).hexdigest(),
                'sha1': hashlib.sha1(content).hexdigest(),
                'sha256': hashlib.sha256(content).hexdigest()
            }
        
        return {
            'path': str(self.file_path),
            'size': self.file_size,
            'hashes': self.file_hash
        }
    
    def check_strings(self, suspicious_strings: List[str]) -> List[str]:
        """检查文件中的可疑字符串"""
        found_strings = []
        
        try:
            with open(self.file_path, 'rb') as f:
                content = f.read().decode('utf-8', errors='ignore')
                
                for string in suspicious_strings:
                    if string.lower() in content.lower():
                        found_strings.append(string)
        except Exception as e:
            Logger.warning(f"字符串检查失败: {e}")
        
        return found_strings

class VirusTotalChecker:
    """VirusTotal检查器"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.base_url = "https://www.virustotal.com/vtapi/v2"
        
    def check_file_hash(self, file_hash: str) -> Dict:
        """通过哈希值检查文件"""
        if not self.api_key:
            Logger.warning("未提供VirusTotal API密钥，跳过在线检查")
            return {}
        
        url = f"{self.base_url}/file/report"
        params = {
            'apikey': self.api_key,
            'resource': file_hash
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('response_code') == 1:
                return {
                    'scan_date': result.get('scan_date'),
                    'positives': result.get('positives', 0),
                    'total': result.get('total', 0),
                    'detection_ratio': f"{result.get('positives', 0)}/{result.get('total', 0)}",
                    'permalink': result.get('permalink', ''),
                    'scans': result.get('scans', {})
                }
            else:
                Logger.info("文件未在VirusTotal数据库中找到")
                return {}
                
        except requests.RequestException as e:
            Logger.error(f"VirusTotal API请求失败: {e}")
            return {}
        except Exception as e:
            Logger.error(f"VirusTotal检查出错: {e}")
            return {}

class LocalAVTester:
    """本地杀毒软件测试器"""
    
    def __init__(self):
        self.test_commands = [
            "scanner.exe --help",
            "scanner.exe -h 127.0.0.1",
            "scanner.exe --version"
        ]
    
    def test_execution(self, file_path: str) -> Dict:
        """测试文件执行"""
        results = {}
        
        for cmd in self.test_commands:
            # 替换命令中的文件名
            actual_cmd = cmd.replace("scanner.exe", file_path)
            
            try:
                Logger.info(f"执行测试命令: {actual_cmd}")
                
                start_time = time.time()
                result = subprocess.run(
                    actual_cmd.split(),
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                end_time = time.time()
                
                results[cmd] = {
                    'return_code': result.returncode,
                    'stdout': result.stdout[:500],  # 限制输出长度
                    'stderr': result.stderr[:500],
                    'execution_time': end_time - start_time,
                    'success': result.returncode == 0
                }
                
                time.sleep(2)  # 避免频繁执行
                
            except subprocess.TimeoutExpired:
                Logger.warning(f"命令执行超时: {actual_cmd}")
                results[cmd] = {
                    'return_code': -1,
                    'stdout': '',
                    'stderr': 'Timeout',
                    'execution_time': 10,
                    'success': False
                }
            except Exception as e:
                Logger.error(f"命令执行失败: {e}")
                results[cmd] = {
                    'return_code': -1,
                    'stdout': '',
                    'stderr': str(e),
                    'execution_time': 0,
                    'success': False
                }
        
        return results

class EvasionTester:
    """免杀测试主类"""
    
    def __init__(self, vt_api_key: Optional[str] = None):
        self.vt_checker = VirusTotalChecker(vt_api_key)
        self.av_tester = LocalAVTester()
        self.suspicious_strings = [
            'fscan', 'shadow1ng', 'MS17010', 'SmbGhost', 'WebTitle',
            'NetBIOS', 'BruteForce', 'github.com/shadow1ng',
            'exploit', 'payload', 'shellcode', 'backdoor'
        ]
    
    def test_file(self, file_path: str) -> Dict:
        """测试单个文件的免杀效果"""
        Logger.info(f"开始测试文件: {file_path}")
        
        # 文件分析
        analyzer = FileAnalyzer(file_path)
        file_info = analyzer.analyze()
        
        Logger.info(f"文件大小: {file_info['size']} bytes")
        Logger.info(f"MD5: {file_info['hashes']['md5']}")
        Logger.info(f"SHA256: {file_info['hashes']['sha256']}")
        
        # 字符串检查
        found_strings = analyzer.check_strings(self.suspicious_strings)
        if found_strings:
            Logger.warning(f"发现可疑字符串: {found_strings}")
        else:
            Logger.success("未发现明显的可疑字符串")
        
        # VirusTotal检查
        vt_result = self.vt_checker.check_file_hash(file_info['hashes']['sha256'])
        if vt_result:
            detection_ratio = vt_result.get('detection_ratio', '0/0')
            Logger.info(f"VirusTotal检测结果: {detection_ratio}")
            
            if vt_result.get('positives', 0) == 0:
                Logger.success("✅ 通过VirusTotal检测")
            else:
                Logger.warning(f"❌ VirusTotal检测到威胁: {vt_result.get('positives')} 个引擎")
        
        # 本地执行测试
        Logger.info("开始本地执行测试...")
        execution_results = self.av_tester.test_execution(file_path)
        
        successful_executions = sum(1 for r in execution_results.values() if r['success'])
        total_tests = len(execution_results)
        
        Logger.info(f"执行测试结果: {successful_executions}/{total_tests} 成功")
        
        return {
            'file_info': file_info,
            'suspicious_strings': found_strings,
            'virustotal': vt_result,
            'execution_tests': execution_results,
            'summary': {
                'file_size': file_info['size'],
                'suspicious_strings_found': len(found_strings),
                'vt_detections': vt_result.get('positives', 0) if vt_result else 0,
                'vt_total_engines': vt_result.get('total', 0) if vt_result else 0,
                'execution_success_rate': f"{successful_executions}/{total_tests}"
            }
        }
    
    def test_directory(self, directory: str) -> Dict:
        """测试目录中的所有可执行文件"""
        Logger.info(f"扫描目录: {directory}")
        
        directory_path = Path(directory)
        if not directory_path.exists():
            raise FileNotFoundError(f"目录不存在: {directory}")
        
        # 查找可执行文件
        executable_files = []
        for pattern in ['*.exe', '*scanner*', '*tool*']:
            executable_files.extend(directory_path.glob(pattern))
        
        # 过滤掉非文件
        executable_files = [f for f in executable_files if f.is_file()]
        
        Logger.info(f"找到 {len(executable_files)} 个可执行文件")
        
        results = {}
        for file_path in executable_files:
            try:
                results[str(file_path)] = self.test_file(str(file_path))
                Logger.info("-" * 60)
            except Exception as e:
                Logger.error(f"测试文件 {file_path} 时出错: {e}")
                results[str(file_path)] = {'error': str(e)}
        
        return results
    
    def generate_report(self, results: Dict, output_file: str):
        """生成测试报告"""
        Logger.info(f"生成测试报告: {output_file}")
        
        report = {
            'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_files': len(results),
            'results': results,
            'summary': self._generate_summary(results)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        Logger.success(f"报告已保存到: {output_file}")
        
        # 打印摘要
        self._print_summary(report['summary'])
    
    def _generate_summary(self, results: Dict) -> Dict:
        """生成测试摘要"""
        total_files = len(results)
        clean_files = 0
        detected_files = 0
        error_files = 0
        
        total_vt_detections = 0
        total_vt_engines = 0
        
        for file_path, result in results.items():
            if 'error' in result:
                error_files += 1
                continue
            
            vt_result = result.get('virustotal', {})
            if vt_result:
                detections = vt_result.get('positives', 0)
                total_vt_detections += detections
                total_vt_engines += vt_result.get('total', 0)
                
                if detections == 0:
                    clean_files += 1
                else:
                    detected_files += 1
        
        return {
            'total_files': total_files,
            'clean_files': clean_files,
            'detected_files': detected_files,
            'error_files': error_files,
            'overall_detection_rate': f"{total_vt_detections}/{total_vt_engines}" if total_vt_engines > 0 else "N/A",
            'success_rate': f"{clean_files}/{total_files - error_files}" if total_files > error_files else "N/A"
        }
    
    def _print_summary(self, summary: Dict):
        """打印测试摘要"""
        print("\n" + "=" * 60)
        print(f"{Colors.BOLD}测试摘要{Colors.END}")
        print("=" * 60)
        print(f"总文件数: {summary['total_files']}")
        print(f"通过检测: {Colors.GREEN}{summary['clean_files']}{Colors.END}")
        print(f"被检测到: {Colors.RED}{summary['detected_files']}{Colors.END}")
        print(f"测试错误: {Colors.YELLOW}{summary['error_files']}{Colors.END}")
        print(f"整体检测率: {summary['overall_detection_rate']}")
        print(f"成功率: {summary['success_rate']}")
        print("=" * 60)

def main():
    parser = argparse.ArgumentParser(description='Fscan免杀效果测试工具')
    parser.add_argument('-f', '--file', help='测试单个文件')
    parser.add_argument('-d', '--directory', help='测试目录中的所有文件')
    parser.add_argument('-o', '--output', default='evasion_test_report.json', help='输出报告文件')
    parser.add_argument('--vt-api-key', help='VirusTotal API密钥')
    parser.add_argument('--no-vt', action='store_true', help='跳过VirusTotal检查')
    
    args = parser.parse_args()
    
    if not args.file and not args.directory:
        parser.print_help()
        sys.exit(1)
    
    # 创建测试器
    vt_api_key = args.vt_api_key if not args.no_vt else None
    tester = EvasionTester(vt_api_key)
    
    try:
        if args.file:
            # 测试单个文件
            results = {args.file: tester.test_file(args.file)}
        else:
            # 测试目录
            results = tester.test_directory(args.directory)
        
        # 生成报告
        tester.generate_report(results, args.output)
        
    except KeyboardInterrupt:
        Logger.warning("用户中断测试")
        sys.exit(1)
    except Exception as e:
        Logger.error(f"测试过程中出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
