# Debian 开发环境配置指南

## 概述
本文档介绍如何在 Debian 系统上配置开发环境，包括软件源配置、Go 语言安装和 C2 服务器编译。

## 📁 目录
- [软件源配置](#软件源配置)
- [基础工具安装](#基础工具安装)
- [Go 语言安装](#go-语言安装)
- [项目编译](#项目编译)
- [SSL 证书生成](#ssl-证书生成)

## 软件源配置

### 备份原有软件源
```bash
sudo cp /etc/apt/sources.list /etc/apt/sources.list.backup
```

### 配置 Debian Stretch 软件源
```bash
sudo nano /etc/apt/sources.list
```

添加以下内容：
```
deb http://archive.debian.org/debian/ stretch main contrib non-free
deb http://archive.debian.org/debian-security/ stretch/updates main contrib non-free
deb http://archive.debian.org/debian/ stretch-updates main contrib non-free
```

## 基础工具安装

### 更新软件包列表
```bash
sudo apt update
```

### 安装开发工具
```bash
# 安装基础开发工具
sudo apt install libcap2-bin -y
sudo apt install make git -y
sudo apt install mingw-w64 -y
sudo apt install gcc -y
```

## Go 语言安装

### 下载 Go 语言
```bash
cd /tmp
wget https://dl.google.com/go/go1.23.6.linux-amd64.tar.gz
```

### 安装 Go 语言
```bash
# 删除旧版本（如果存在）
sudo rm -rf /usr/local/go

# 解压安装
sudo tar -C /usr/local -xzf /tmp/go1.23.6.linux-amd64.tar.gz
```

### 配置环境变量
```bash
# 创建 Go 环境变量配置文件
sudo bash -c 'cat > /etc/profile.d/golang.sh <<EOF
# Go lang environment variables
export GOROOT=/usr/local/go
export GOPATH=\$HOME/go
export PATH=\$PATH:\$GOROOT/bin:\$GOPATH/bin
EOF'

# 赋予执行权限
sudo chmod +x /etc/profile.d/golang.sh

# 重新加载环境变量
source /etc/profile.d/golang.sh
```

### 验证安装
```bash
go version
```

## 项目编译

### AdaptixC2 项目编译
```bash
# 进入项目目录
cd ~/projects/AdaptixC2

# 编译服务器
CGO_ENABLED=1 make server

# 编译扩展
CGO_ENABLED=1 make extenders
```

## SSL 证书生成

### 生成自签名证书
```bash
openssl req -x509 -nodes -newkey rsa:2048 \
    -keyout server.rsa.key \
    -out server.rsa.crt \
    -days 3650
```

### 启动服务器
```bash
./adaptixserver -profile profile.json
```

## 注意事项
- Debian Stretch 已经停止支持，建议升级到更新版本
- 确保系统有足够的磁盘空间进行编译
- 编译大型项目时注意内存使用情况
- 定期更新 Go 语言版本

## 故障排除

### 常见问题
1. **软件源无法访问**: 检查网络连接或更换软件源镜像
2. **编译失败**: 确保安装了所有必需的依赖包
3. **Go 环境变量未生效**: 重新登录或手动 source 配置文件

## 相关文档
- [Go 编译指南](../编程开发/Go/Go编译指南.md)
- [Linux 常用命令](./Linux常用命令.md)
- [MinGW 安装指南](../编程开发/编译工具/MinGW安装指南.md)

