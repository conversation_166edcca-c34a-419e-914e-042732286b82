# Linux 常用命令集合

## 概述
本文档收集了 Linux 系统管理、网络配置、日志清理等常用命令。

## 📁 目录
- [网络工具](#网络工具)
- [后台进程管理](#后台进程管理)
- [日志清理](#日志清理)
- [防火墙管理](#防火墙管理)
- [历史记录管理](#历史记录管理)
- [系统信息](#系统信息)

## 网络工具

### 获取公网 IP
```bash
curl cip.cc
```

### 其他网络命令
```bash
# 查看网络连接
netstat -tulpn

# 查看路由表
route -n

# 测试网络连通性
ping -c 4 google.com
```

## 后台进程管理

### 启动后台服务
```bash
# 启动 vshell 服务
nohup ./vshell_linux_amd64 >/dev/null 2>&1 &

# 启动 adaptix 服务器
nohup ./adaptixserver -profile profile.json >/dev/null 2>&1 &

# 启动通用服务器
nohup ./serverscersion3 >/dev/null 2>&1 &

# 启动 teamserver
nohup ./teamserver *************** FryBH3UEkxrVKwdKczb8CUsuPrj4Ec one.profile >/dev/null 2>&1 &
```

### 进程管理
```bash
# 查看后台任务
jobs

# 查看进程
ps aux | grep process_name

# 杀死进程
kill -9 PID
killall process_name
```

## 日志清理

### ⚠️ 安全警告
**以下命令用于清除系统日志痕迹，仅供安全研究和合法测试使用！**

### 清除历史记录
```bash
# 禁用历史记录
unset HISTORY HISTFILE HISTSAVE HISTZONE HISTLOG
export HISTFILE=/dev/null
export HISTSIZE=0
export HISTFILESIZE=0
export PROMPT_COMMAND=0

# 清除当前会话历史
history -c
history -w
```

### 清除系统日志
```bash
# 清除登录日志
sudo echo "" > /var/log/wtmp
sudo echo "" > /var/log/lastlog
sudo echo "" > /var/log/btmp

# 删除日志文件最后一行
sed -i '$d' /var/log/btmp 
sed -i '$d' /var/log/lastlog
sed -i '$d' /var/log/secure
sed -i '$d' /var/log/messages
```

### 高级日志清理
```bash
# 备份和修改 wtmp 日志
utmpdump /var/log/wtmp > /var/log/wtmp.file
sed -i '/$d/d' /var/log/wtmp.file
utmpdump -r < /var/log/wtmp.file > /var/log/wtmp
rm -f /var/log/wtmp.file

# 查看最近登录记录
last -n 10
```

## 防火墙管理

### CentOS/RHEL (firewalld)
```bash
# 开放端口
sudo firewall-cmd --zone=public --add-port=8888/tcp --permanent

# 重载防火墙配置
sudo firewall-cmd --reload

# 查看开放端口
sudo firewall-cmd --list-ports

# 关闭防火墙
sudo systemctl stop firewalld
sudo systemctl disable firewalld
```

### Ubuntu (ufw)
```bash
# 开放端口
sudo ufw allow 8888/tcp

# 启用防火墙
sudo ufw enable

# 查看状态
sudo ufw status

# 关闭防火墙
sudo ufw disable
```

## 系统信息

### 系统状态查看
```bash
# 系统信息
uname -a
cat /etc/os-release

# 内存使用
free -h

# 磁盘使用
df -h

# CPU 信息
lscpu
cat /proc/cpuinfo

# 运行时间
uptime
```

### 用户和权限
```bash
# 当前用户
whoami
id

# 切换用户
su - username
sudo -u username command

# 查看用户列表
cat /etc/passwd
```

## 文件操作

### 文件权限
```bash
# 修改权限
chmod +x file.sh
chmod 755 file.sh

# 修改所有者
chown user:group file.txt

# 查找文件
find /path -name "filename"
locate filename
```

### 文件内容操作
```bash
# 查看文件
cat file.txt
less file.txt
head -n 10 file.txt
tail -f file.txt

# 搜索内容
grep "pattern" file.txt
grep -r "pattern" /path/
```

## 服务管理

### systemd 服务
```bash
# 启动服务
sudo systemctl start service_name

# 停止服务
sudo systemctl stop service_name

# 重启服务
sudo systemctl restart service_name

# 查看服务状态
sudo systemctl status service_name

# 设置开机自启
sudo systemctl enable service_name

# 禁用开机自启
sudo systemctl disable service_name
```

## 注意事项
⚠️ **重要提醒**
- 日志清理命令可能违反某些组织的安全政策
- 仅在授权的测试环境中使用
- 清理系统日志可能影响故障排查
- 某些操作需要 root 权限
- 建议在执行前备份重要数据

## 相关文档
- [Windows 系统管理命令](../Windows/Windows系统管理命令集合.md)
- [网络工具使用指南](../../网络工具/)
- [安全工具使用指南](../../安全工具/)
