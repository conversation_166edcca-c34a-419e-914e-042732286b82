# Windows Defender 管理指南

## ⚠️ 安全警告
**本文档仅供系统管理和安全研究使用！**
- 请勿用于绕过合法的安全防护
- 仅在授权的测试环境中使用
- 使用者需承担相应法律责任

## 概述
本文档介绍如何查看和管理 Windows Defender 的排除列表和相关设置。

## 📁 目录
- [查看排除列表](#查看排除列表)
- [添加排除项](#添加排除项)
- [UAC 设置查询](#uac-设置查询)
- [PowerShell 管理](#powershell-管理)

## 查看排除列表

### 通过图形界面查看
1. 打开 Windows 设置
2. 选择 **更新和安全**
3. 选择 **Windows 安全中心**
4. 点击 **病毒和威胁防护**
5. 在"病毒和威胁防护设置"下点击 **管理设置**
6. 滚动到 **排除项** 部分

### 通过注册表查看
```cmd
# 查看所有排除项
reg query "HKLM\SOFTWARE\Microsoft\Windows Defender\Exclusions" /s

# 查看路径排除
reg query "HKLM\SOFTWARE\Microsoft\Windows Defender\Exclusions\Paths" /s

# 查看扩展名排除
reg query "HKLM\SOFTWARE\Microsoft\Windows Defender\Exclusions\Extensions" /s

# 查看进程排除
reg query "HKLM\SOFTWARE\Microsoft\Windows Defender\Exclusions\Processes" /s
```

### 通过 PowerShell 查看
```powershell
# 查看排除路径
Get-MpPreference | Select-Object ExclusionPath

# 查看排除扩展名
Get-MpPreference | Select-Object ExclusionExtension

# 查看排除进程
Get-MpPreference | Select-Object ExclusionProcess

# 查看所有排除设置
Get-MpPreference | Select-Object Exclusion*
```

## 添加排除项

### 通过 PowerShell 添加排除路径
```powershell
# 添加单个路径
Add-MpPreference -ExclusionPath "C:\MyFolder"

# 添加多个路径
Add-MpPreference -ExclusionPath @("C:\Folder1", "C:\Folder2")

# 添加扩展名排除
Add-MpPreference -ExclusionExtension ".tmp"

# 添加进程排除
Add-MpPreference -ExclusionProcess "myapp.exe"
```

### 通过 WMI 添加排除项
```powershell
# 添加路径排除（高级方法）
try {
    $null = New-CimInstance -Namespace "root/Microsoft/Windows/Defender" -ClassName "MSFT_MpPreference" -Property @{
        ExclusionPath = @("C:\MyPath")
        Force = $True
    }
} catch {
    Write-Error "Failed to add exclusion: $($_.Exception.Message)"
}
```

### 批处理脚本添加排除
```batch
@echo off
REM 添加当前目录到排除列表
powershell "try {$null = New-CimInstance MSFT_MpPreference @{ExclusionPath = @('%~dp0'); Force = $True} -Namespace root/Microsoft/Windows/Defender -EA 1} catch {$host.SetShouldExit($_.Exception.HResult)}"

REM 添加临时目录到排除列表
powershell "try {$null = New-CimInstance MSFT_MpPreference @{ExclusionPath = @('%temp%\_temp_folder'); Force = $True} -Namespace root/Microsoft/Windows/Defender -EA 1} catch {$host.SetShouldExit($_.Exception.HResult)}"
```

## UAC 设置查询

### 查询 UAC 状态
```cmd
# 查询 UAC 是否启用
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v EnableLUA

# 查询管理员提示行为
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v ConsentPromptBehaviorAdmin

# 查询标准用户提示行为
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" /v ConsentPromptBehaviorUser
```

## PowerShell 管理

### 获取 Defender 状态
```powershell
# 获取实时保护状态
Get-MpComputerStatus

# 获取威胁检测历史
Get-MpThreatDetection

# 获取扫描历史
Get-MpThreat
```

### 管理 Defender 服务
```powershell
# 检查 Defender 服务状态
Get-Service WinDefend

# 获取 Defender 首选项
Get-MpPreference

# 更新威胁定义
Update-MpSignature
```

## 移除排除项

### 通过 PowerShell 移除
```powershell
# 移除路径排除
Remove-MpPreference -ExclusionPath "C:\MyFolder"

# 移除扩展名排除
Remove-MpPreference -ExclusionExtension ".tmp"

# 移除进程排除
Remove-MpPreference -ExclusionProcess "myapp.exe"
```

## 注意事项
- 添加排除项需要管理员权限
- 过多的排除项可能降低系统安全性
- 建议定期审查排除列表
- 某些操作可能需要重启系统生效
- 企业环境中可能受组策略限制

## 相关命令参考
```powershell
# 完整的 Defender 管理命令列表
Get-Command -Module Defender

# 获取特定命令的帮助
Get-Help Add-MpPreference -Full
```

## 相关文档
- [Windows 系统管理命令](../../常用命令/Windows系统管理命令集合.md)
- [安全工具使用指南](../../安全工具/)
- [Linux 系统管理](../Linux/Linux常用命令.md)
