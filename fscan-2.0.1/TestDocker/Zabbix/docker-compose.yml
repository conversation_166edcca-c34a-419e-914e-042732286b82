version: '3'

services:
  mysql:
    image: mysql:8.0
    container_name: zabbix-mysql
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: zabbix
      MYSQL_USER: zabbix
      MYSQL_PASSWORD: zabbix123
    ports:
      - "3306:3306"
    volumes:
      - ./mysql_data:/var/lib/mysql
    networks:
      - zabbix-net

  zabbix-server:
    image: zabbix/zabbix-server-mysql:ubuntu-6.0.23
    container_name: zabbix-server
    environment:
      DB_SERVER_HOST: mysql
      MYSQL_DATABASE: zabbix
      MYSQL_USER: zabbix
      MYSQL_PASSWORD: zabbix123
      MYSQL_ROOT_PASSWORD: root123
    ports:
      - "10051:10051"
    depends_on:
      - mysql
    networks:
      - zabbix-net

  zabbix-web:
    image: zabbix/zabbix-web-nginx-mysql:ubuntu-6.0.23
    container_name: zabbix-web
    environment:
      DB_SERVER_HOST: mysql
      MYSQL_DATABASE: zabbix
      MYSQL_USER: zabbix
      MYSQL_PASSWORD: zabbix123
      MYSQL_ROOT_PASSWORD: root123
      ZBX_SERVER_HOST: zabbix-server
      PHP_TZ: Asia/Shanghai
    ports:
      - "80:8080"
      - "443:8443"
    depends_on:
      - mysql
      - zabbix-server
    networks:
      - zabbix-net

networks:
  zabbix-net:
    driver: bridge