name: 测试构建

on:
  push:
    branches:
      - dev
      - develop
      - feature/*
  pull_request:
    branches:
      - main
      - master
      - dev
  workflow_dispatch:
    inputs:
      branch:
        description: '测试分支'
        required: false
        default: 'dev'

permissions:
  contents: read

jobs:
  test-build:
    name: 测试构建
    runs-on: ubuntu-latest
    timeout-minutes: 30

    # 设置作业级别的环境变量
    env:
      GITHUB_OWNER: ${{ github.repository_owner }}
      GITHUB_REPO: ${{ github.event.repository.name }}
      PROJECT_NAME: ${{ github.event.repository.name }}

    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: 🔍 获取项目信息
        id: project
        run: |
          echo "owner=${GITHUB_REPOSITORY_OWNER}" >> $GITHUB_OUTPUT
          echo "repo=${GITHUB_REPOSITORY#*/}" >> $GITHUB_OUTPUT
          echo "branch=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
          echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT
          echo "full_sha=${GITHUB_SHA}" >> $GITHUB_OUTPUT
          echo "build_date=$(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_OUTPUT
          echo "timestamp=$(date +%s)" >> $GITHUB_OUTPUT

      - name: 🐹 设置 Go 环境
        uses: actions/setup-go@v5
        with:
          go-version: '1.20'
          cache: true

      - name: 📦 下载依赖
        run: |
          go mod download
          go mod verify

      - name: 🗜️ 安装 UPX 压缩工具
        uses: crazy-max/ghaction-upx@v3
        with:
          install-only: true

      - name: ℹ️ 显示构建环境信息
        run: |
          echo "Go 版本: $(go version)"
          echo "UPX 版本: $(upx --version)"
          echo "分支: ${{ steps.project.outputs.branch }}"
          echo "提交: ${{ steps.project.outputs.short_sha }}"
          echo "仓库: ${{ steps.project.outputs.owner }}/${{ steps.project.outputs.repo }}"
          echo "构建时间: ${{ steps.project.outputs.build_date }}"
          echo "环境变量:"
          echo "- GITHUB_OWNER: $GITHUB_OWNER"
          echo "- GITHUB_REPO: $GITHUB_REPO"
          echo "- PROJECT_NAME: $PROJECT_NAME"

      - name: 📊 记录构建开始时间
        id: build_start
        run: |
          echo "start_time=$(date +%s)" >> $GITHUB_OUTPUT
          echo "start_readable=$(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_OUTPUT

      - name: 🚀 测试构建 (Snapshot 模式)
        uses: goreleaser/goreleaser-action@v5
        with:
          distribution: goreleaser
          version: latest
          args: release --snapshot --clean -f .github/conf/.goreleaser.yml
          workdir: .
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 📊 记录构建结束时间
        id: build_end
        run: |
          echo "end_time=$(date +%s)" >> $GITHUB_OUTPUT
          echo "end_readable=$(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_OUTPUT
          start_time=${{ steps.build_start.outputs.start_time }}
          end_time=$(date +%s)
          duration=$((end_time - start_time))
          echo "duration=${duration}" >> $GITHUB_OUTPUT
          echo "duration_readable=$(printf '%02d:%02d:%02d' $((duration/3600)) $((duration%3600/60)) $((duration%60)))" >> $GITHUB_OUTPUT

      - name: 📋 上传测试产物
        uses: actions/upload-artifact@v4
        with:
          name: 测试构建-${{ steps.project.outputs.branch }}-${{ steps.project.outputs.short_sha }}
          path: |
            dist/
          retention-days: 7

      - name: 📊 统计构建产物
        id: build_stats
        run: |
          if [ -d "dist" ]; then
            total_files=$(find dist/ -type f | wc -l)
            executable_files=$(find dist/ -type f -executable | wc -l)
            config_files=$(find dist/ -name "*.json" -o -name "*.yaml" -o -name "*.yml" -o -name "*.txt" | wc -l)
          
            echo "total_files=$total_files" >> $GITHUB_OUTPUT
            echo "executable_files=$executable_files" >> $GITHUB_OUTPUT
            echo "config_files=$config_files" >> $GITHUB_OUTPUT
          else
            echo "total_files=0" >> $GITHUB_OUTPUT
            echo "executable_files=0" >> $GITHUB_OUTPUT
            echo "config_files=0" >> $GITHUB_OUTPUT
          fi

      - name: 📊 生成构建报告
        if: always()
        run: |
          echo "# 🎯 测试构建报告" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 基本信息表格
          echo "## 📋 构建基本信息" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| 项目 | 值 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|-----|" >> $GITHUB_STEP_SUMMARY
          echo "| 🏷️ **项目名称** | ${{ steps.project.outputs.repo }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 👤 **拥有者** | ${{ steps.project.outputs.owner }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 🌿 **分支** | ${{ steps.project.outputs.branch }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 📝 **提交SHA** | \`${{ steps.project.outputs.short_sha }}\` |" >> $GITHUB_STEP_SUMMARY
          echo "| 📅 **构建时间** | ${{ steps.project.outputs.build_date }} |" >> $GITHUB_STEP_SUMMARY
          echo "| ⏱️ **构建耗时** | ${{ steps.build_end.outputs.duration_readable }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 🚀 **触发方式** | ${{ github.event_name }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 🔧 **Go版本** | $(go version | cut -d' ' -f3) |" >> $GITHUB_STEP_SUMMARY
          echo "| 🗜️ **UPX版本** | $(upx --version | head -1 | cut -d' ' -f2) |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 构建环境信息
          echo "## 🖥️ 构建环境" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| 环境变量 | 值 |" >> $GITHUB_STEP_SUMMARY
          echo "|----------|-----|" >> $GITHUB_STEP_SUMMARY
          echo "| **GITHUB_OWNER** | $GITHUB_OWNER |" >> $GITHUB_STEP_SUMMARY
          echo "| **GITHUB_REPO** | $GITHUB_REPO |" >> $GITHUB_STEP_SUMMARY
          echo "| **PROJECT_NAME** | $PROJECT_NAME |" >> $GITHUB_STEP_SUMMARY
          echo "| **RUNNER_OS** | $RUNNER_OS |" >> $GITHUB_STEP_SUMMARY
          echo "| **RUNNER_ARCH** | $RUNNER_ARCH |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 构建时间统计
          echo "## ⏰ 构建时间统计" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| 阶段 | 时间 |" >> $GITHUB_STEP_SUMMARY
          echo "|------|------|" >> $GITHUB_STEP_SUMMARY
          echo "| 🚀 **开始时间** | ${{ steps.build_start.outputs.start_readable }} |" >> $GITHUB_STEP_SUMMARY
          echo "| 🏁 **结束时间** | ${{ steps.build_end.outputs.end_readable }} |" >> $GITHUB_STEP_SUMMARY
          echo "| ⏱️ **总耗时** | ${{ steps.build_end.outputs.duration_readable }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 构建产物统计
          if [ -d "dist" ]; then
            echo "## 📦 构建产物统计" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          
            # 文件类型统计
            echo "### 📊 文件类型统计" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          
            echo "| 文件类型 | 数量 |" >> $GITHUB_STEP_SUMMARY
            echo "|----------|------|" >> $GITHUB_STEP_SUMMARY
            echo "| 📁 **总文件数** | ${{ steps.build_stats.outputs.total_files }} |" >> $GITHUB_STEP_SUMMARY
            echo "| 🔧 **可执行文件** | ${{ steps.build_stats.outputs.executable_files }} |" >> $GITHUB_STEP_SUMMARY
            echo "| 📄 **配置文件** | ${{ steps.build_stats.outputs.config_files }} |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          
            # 总产物大小
            echo "### 📦 产物大小" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          
            total_size=$(du -sh dist/ 2>/dev/null | cut -f1 || echo "未知")
            echo "**总产物大小**: $total_size" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
          fi
          
          # 总结
          echo "## 📈 构建总结" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ job.status }}" == "success" ]; then
            echo "🎉 **构建状态**: ✅ 成功" >> $GITHUB_STEP_SUMMARY
          else
            echo "🎉 **构建状态**: ❌ 失败" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "📊 **可执行文件**: ${{ steps.build_stats.outputs.executable_files }} 个" >> $GITHUB_STEP_SUMMARY
          echo "⏱️ **构建耗时**: ${{ steps.build_end.outputs.duration_readable }}" >> $GITHUB_STEP_SUMMARY
          echo "📦 **产物大小**: $(du -sh dist/ 2>/dev/null | cut -f1 || echo "未知")" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 添加快速链接
          echo "## 🔗 快速链接" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- 📋 [查看产物列表](https://github.com/${{ steps.project.outputs.owner }}/${{ steps.project.outputs.repo }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "- 📥 [下载产物](https://github.com/${{ steps.project.outputs.owner }}/${{ steps.project.outputs.repo }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
          echo "- 🔍 [查看提交](https://github.com/${{ steps.project.outputs.owner }}/${{ steps.project.outputs.repo }}/commit/${{ steps.project.outputs.full_sha }})" >> $GITHUB_STEP_SUMMARY
          echo "- 🌿 [查看分支](https://github.com/${{ steps.project.outputs.owner }}/${{ steps.project.outputs.repo }}/tree/${{ steps.project.outputs.branch }})" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          echo "---" >> $GITHUB_STEP_SUMMARY
          echo "*报告生成时间: $(date -u +"%Y-%m-%d %H:%M:%S UTC")*" >> $GITHUB_STEP_SUMMARY