package main

import (
	"fmt"
	"math/rand"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/security-tools/network-scanner/Common"
	"github.com/security-tools/network-scanner/Core"
)

// 工具名称和版本信息混淆

// 反调试检测
func antiDebugCheck() {
	if runtime.GOOS == "windows" {
		start := time.Now()
		time.Sleep(time.Millisecond * 10)
		elapsed := time.Since(start)

		if elapsed > time.Millisecond*50 {
			fmt.Println("System check completed.")
			os.Exit(0)
		}

		if os.Getppid() == 1 {
			fmt.Println("System check completed.")
			os.Exit(0)
		}
	} else {
		if os.Getenv("TERM") == "" {
			fmt.Println("System check completed.")
			os.Exit(0)
		}
	}
}

// 动态Banner生成
func generateBanner() {
	banners := [][]string{
		{
			"   ___                              _    ",
			"  / _ \\     ___  ___ _ __ __ _  ___| | __ ",
			" / /_\\/____/ __|/ __| '__/ _` |/ __| |/ /",
			"/ /_\\\\_____\\__ \\ (__| | | (_| | (__|   <    ",
			"\\____/     |___/\\___|_|  \\__,_|\\___|_|\\_\\   ",
		},
		{
			"╔═══════════════════════════════════════╗",
			"║          Network Security Tool        ║",
			"║              Version 2.0              ║",
			"╚═══════════════════════════════════════╝",
		},
	}

	// 随机选择Banner
	banner := banners[rand.Intn(len(banners))]
	for _, line := range banner {
		fmt.Println(line)
	}

	versions := []string{
		"Network Scanner v2.1.0",
		"Security Tool v1.5.3",
		"Port Scanner v3.0.1",
		"System Analyzer v2.0.5",
	}
	fmt.Printf("      %s\n\n", versions[rand.Intn(len(versions))])
}

// 混淆的主函数
func executeMainLogic() {
	// 虚假操作
	dummy := rand.Intn(100)
	if dummy > 50 {
		_ = strings.ToUpper("dummy")
	}

	Common.InitLogger()

	var Info Common.HostInfo
	Common.Flag(&Info)

	// 解析 CLI 参数
	if err := Common.Parse(&Info); err != nil {
		os.Exit(1)
	}

	// 初始化输出系统，如果失败则直接退出
	if err := Common.InitOutput(); err != nil {
		Common.LogError(fmt.Sprintf("初始化输出系统失败: %v", err))
		os.Exit(1)
	}
	defer Common.CloseOutput()

	// 执行 CLI 扫描逻辑
	Core.Scan(Info)
}

func main() {
	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())

	// 反调试检测
	antiDebugCheck()

	// 显示Banner
	generateBanner()

	// 执行主逻辑
	executeMainLogic()
}
