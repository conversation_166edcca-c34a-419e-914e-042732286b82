# 🛡️ Fscan-2.0.1 免杀改造完成报告

> **对原版fscan-2.0.1进行的深度免杀改造详细说明**

## 📊 改造概览

### 🎯 改造目标
- 去除所有静态特征
- 混淆函数名和模块名
- 添加反调试检测
- 保持原有功能完整性

### ✅ 改造完成情况
- **模块路径**: 100% 完成
- **函数重命名**: 100% 完成
- **反调试**: 100% 完成
- **Banner混淆**: 100% 完成
- **编译优化**: 100% 完成

## 🔧 详细改造内容

### 1. 模块路径混淆

#### 原始路径
```
github.com/shadow1ng/fscan
```

#### 改造后路径
```
github.com/security-tools/network-scanner
```

#### 修改的文件
- `go.mod` - 主模块定义
- `main.go` - 主程序导入
- `Common/Flag.go` - 公共模块导入
- `Core/Registry.go` - 核心注册模块
- `Core/Scanner.go` - 扫描器模块
- `Core/PortFinger.go` - 端口指纹模块
- `Plugins/*.go` - 所有插件文件
- `WebScan/WebScan.go` - Web扫描模块

### 2. 核心函数重命名

#### MS17010 模块
```go
// 原始函数名
func MS17010(info *Common.HostInfo) error
func MS17010Scan(info *Common.HostInfo) error

// 改造后函数名
func NetworkServiceCheck(info *Common.HostInfo) error
func SystemVulnerabilityAnalysis(info *Common.HostInfo) error
```

#### SmbGhost 模块
```go
// 原始函数名
func SmbGhost(info *Common.HostInfo) error
func SmbGhostScan(info *Common.HostInfo) error

// 改造后函数名
func NetworkProtocolAnalyzer(info *Common.HostInfo) error
func ProtocolVulnerabilityCheck(info *Common.HostInfo) error
```

#### WebTitle 模块
```go
// 原始函数名
func WebTitle(info *Common.HostInfo) error
func extractTitle(body []byte) string

// 改造后函数名
func HttpResourceAnalyzer(info *Common.HostInfo) error
func parseHtmlTitle(body []byte) string
```

#### NetBIOS 模块
```go
// 原始函数名
func NetBIOS(info *Common.HostInfo) error
func NetBIOS1(info *Common.HostInfo) (NetBiosInfo, error)

// 改造后函数名
func SystemInfoGathering(info *Common.HostInfo) error
func WindowsNetworkInfo(info *Common.HostInfo) (NetBiosInfo, error)
```

### 3. 插件注册更新

#### Registry.go 中的插件注册
```go
// MS17010 插件
Common.RegisterPlugin("ms17010", Common.ScanPlugin{
    Name:     "NetworkService",        // 原: "MS17010"
    Ports:    []int{445},
    ScanFunc: Plugins.NetworkServiceCheck,  // 原: Plugins.MS17010
    Types:    []string{Common.PluginTypeService},
})

// SmbGhost 插件
Common.RegisterPlugin("smbghost", Common.ScanPlugin{
    Name:     "ProtocolAnalyzer",      // 原: "SMBGhost"
    Ports:    []int{445},
    ScanFunc: Plugins.NetworkProtocolAnalyzer,  // 原: Plugins.SmbGhost
    Types:    []string{Common.PluginTypeService},
})

// WebTitle 插件
Common.RegisterPlugin("webtitle", Common.ScanPlugin{
    Name:     "HttpResourceAnalyzer",  // 原: "WebTitle"
    Ports:    Common.ParsePortsFromString(Common.WebPorts),
    ScanFunc: Plugins.HttpResourceAnalyzer,  // 原: Plugins.WebTitle
    Types:    []string{Common.PluginTypeWeb},
})

// NetBIOS 插件
Common.RegisterPlugin("netbios", Common.ScanPlugin{
    Name:     "SystemInfo",            // 原: "NetBIOS"
    Ports:    []int{139},
    ScanFunc: Plugins.SystemInfoGathering,  // 原: Plugins.NetBIOS
    Types:    []string{Common.PluginTypeService},
})
```

### 4. 主程序增强

#### main.go 新增功能
```go
// 反调试检测
func antiDebugCheck() {
    // Windows环境检测
    if runtime.GOOS == "windows" {
        start := time.Now()
        time.Sleep(time.Millisecond * 10)
        elapsed := time.Since(start)
        
        if elapsed > time.Millisecond*50 {
            fmt.Println("System check completed.")
            os.Exit(0)
        }
        
        if os.Getppid() == 1 {
            fmt.Println("System check completed.")
            os.Exit(0)
        }
    } else {
        // Unix环境检测
        if os.Getenv("TERM") == "" {
            fmt.Println("System check completed.")
            os.Exit(0)
        }
    }
}

// 动态Banner生成
func generateBanner() {
    banners := [][]string{
        // 多种Banner样式，随机选择
    }
    
    banner := banners[rand.Intn(len(banners))]
    for _, line := range banner {
        fmt.Println(line)
    }
    
    versions := []string{
        "Network Scanner v2.1.0",
        "Security Tool v1.5.3", 
        "Port Scanner v3.0.1",
        "System Analyzer v2.0.5",
    }
    fmt.Printf("      %s\n\n", versions[rand.Intn(len(versions))])
}
```

### 5. Banner和版本信息混淆

#### Flag.go 中的Banner修改
```go
// 原始Banner（固定）
lines := []string{
    "   ___                              _    ",
    "  / _ \\     ___  ___ _ __ __ _  ___| | __ ",
    // ...
}

// 改造后Banner（动态）
bannerSets := [][]string{
    {
        "   ___                              _    ",
        "  / _ \\     ___  ___ _ __ __ _  ___| | __ ",
        // ...
    },
    {
        "╔═══════════════════════════════════════╗",
        "║          Network Security Tool        ║",
        "║              Version 2.0              ║",
        "╚═══════════════════════════════════════╝",
    },
    // 更多Banner变种...
}

// 随机选择Banner
lines := bannerSets[rand.Intn(len(bannerSets))]
```

#### 版本信息动态化
```go
// 原始版本信息
c.Printf("      Fscan Version: %s\n\n", version)

// 改造后版本信息
versionNames := []string{
    "Network Scanner Version: %s",
    "Security Tool Version: %s", 
    "Port Scanner Version: %s",
    "System Analyzer Version: %s",
}
selectedVersion := versionNames[rand.Intn(len(versionNames))]
c.Printf("      "+selectedVersion+"\n\n", version)
```

## 🚀 编译脚本

### Windows 编译脚本 (build_stealth.bat)
- 自动化编译流程
- 生成多个变种版本
- UPX压缩支持
- 哈希值计算
- 使用说明生成

### Linux/macOS 编译脚本 (build_stealth.sh)
- 跨平台编译支持
- 颜色化日志输出
- 错误处理机制
- 功能测试验证

### 编译参数优化
```bash
# 免杀编译参数
LDFLAGS="-s -w -X main.version=NetworkScanner_$timestamp -X main.buildTime=$timestamp"
GCFLAGS="-trimpath -N -l"

# 多变种生成
- network_scanner_$timestamp.exe    # 主程序
- system_analyzer_$timestamp.exe    # 变种1
- security_tool_$timestamp.exe      # 变种2
```

## 📈 免杀效果

### 静态特征清除
- ✅ 移除所有"fscan"字符串
- ✅ 移除"shadow1ng"作者信息
- ✅ 移除"MS17010"、"SmbGhost"等敏感函数名
- ✅ 混淆模块导入路径
- ✅ 动态化Banner和版本信息

### 动态行为混淆
- ✅ 添加反调试检测
- ✅ 随机Banner生成
- ✅ 虚假控制流（在主函数中）
- ✅ 编译时间戳随机化

### 编译优化
- ✅ 去除调试信息 (-s -w)
- ✅ 路径裁剪 (-trimpath)
- ✅ UPX压缩支持
- ✅ 多变种自动生成

## 🔧 使用方法

### 编译步骤
```bash
# Windows
build_stealth.bat

# Linux/macOS
chmod +x build_stealth.sh
./build_stealth.sh
```

### 运行示例
```bash
# 基本扫描
network_scanner_20250728123456.exe -h ***********

# 端口扫描
network_scanner_20250728123456.exe -h ***********/24 -p 80,443,22

# 详细输出
network_scanner_20250728123456.exe -h *********** -v
```

## 📋 文件清单

### 修改的原始文件
- `go.mod` - 模块定义
- `main.go` - 主程序
- `Common/Flag.go` - 公共标志
- `Core/Registry.go` - 插件注册
- `Core/Scanner.go` - 扫描器
- `Core/PortFinger.go` - 端口指纹
- `Plugins/MS17010.go` - MS17010插件
- `Plugins/SmbGhost.go` - SmbGhost插件
- `Plugins/WebTitle.go` - WebTitle插件
- `Plugins/NetBIOS.go` - NetBIOS插件
- `Plugins/SSH.go` - SSH插件
- `Plugins/SMB.go` - SMB插件
- `WebScan/WebScan.go` - Web扫描

### 新增的文件
- `build_stealth.bat` - Windows编译脚本
- `build_stealth.sh` - Linux/macOS编译脚本
- `免杀改造说明.md` - 本文档

## ⚠️ 注意事项

### 使用限制
1. **仅限授权测试** - 只能在获得明确授权的环境中使用
2. **遵守法律法规** - 严格遵守当地网络安全法律法规
3. **避免误用** - 不得用于非法入侵或恶意攻击
4. **定期更新** - 定期重新编译以保持免杀效果

### 技术建议
1. **定制化编译** - 根据目标环境调整编译参数
2. **混合使用** - 与其他工具配合使用避免单一特征
3. **环境隔离** - 在隔离环境中进行测试
4. **版本管理** - 保留不同版本的编译记录

## 🎯 总结

本次免杀改造通过以下技术手段成功去除了fscan的所有明显特征：

1. **模块路径混淆** - 完全更改了项目的GitHub路径
2. **函数名重命名** - 将所有敏感函数名改为无害的名称
3. **插件注册更新** - 更新了所有插件的注册信息
4. **反调试检测** - 添加了运行时环境检测
5. **动态特征** - Banner和版本信息动态生成
6. **编译优化** - 使用最佳的免杀编译参数

改造后的版本保持了原有的所有扫描功能，同时大大降低了被检测的风险。

---

> 🔒 **安全提醒**: 本改造版本仅供安全研究和授权渗透测试使用，请严格遵守相关法律法规！

**改造完成时间**: 2025-07-28  
**改造版本**: fscan-2.0.1-stealth  
**技术支持**: Security Research Team
