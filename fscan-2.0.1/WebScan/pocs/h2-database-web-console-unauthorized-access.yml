name: poc-yaml-h2-database-web-console-unauthorized-access
rules:
  - method: GET
    path: /h2-console
    follow_redirects: true
    expression: >
      response.status == 200 && response.body.bcontains(b"Welcome to H2")
    search: |
      location.href = '(?P<token>.+?)'
  - method: GET
    path: /h2-console/{{token}}
    expression: |
      response.status == 200 && response.body.bcontains(b"Generic H2")
detail:
  author: ju<PERSON><PERSON><PERSON> (https://github.com/jujumanman)
  links:
    - https://blog.csdn.net/zy15667076526/article/details/111413979
    - https://github.com/vulhub/vulhub/tree/master/h2database/h2-console-unacc
