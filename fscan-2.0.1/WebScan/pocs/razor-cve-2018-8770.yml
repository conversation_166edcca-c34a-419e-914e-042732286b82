name: poc-yaml-razor-cve-2018-8770
rules:
  - method: GET
    path: /tests/generate.php
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(b"Fatal error: Class 'PHPUnit_Framework_TestCase' not found in ") && response.body.bcontains(b"/application/third_party/CIUnit/libraries/CIUnitTestCase.php on line")
detail:
  author: we1x4n(https://we1x4n.github.io/)
  links:
    - http://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-8770
    - https://www.exploit-db.com/exploits/44495/
