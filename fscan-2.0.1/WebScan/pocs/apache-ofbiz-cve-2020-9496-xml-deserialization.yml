name: poc-yaml-apache-ofbiz-cve-2020-9496-xml-deserialization
set:
  rand: randomInt(200000000, 210000000)
rules:
  - method: POST
    path: /webtools/control/xmlrpc
    headers:
      Content-Type: application/xml
    body: >-
      <?xml
      version="1.0"?><methodCall><methodName>{{rand}}</methodName><params><param><value>dwisiswant0</value></param></params></methodCall>
    follow_redirects: false
    expression: >
      response.status == 200 && response.content_type.contains("xml") && response.body.bcontains(bytes("methodResponse")) && response.body.bcontains(bytes("No such service [" + string(rand)))
detail:
  author: su(https://suzzz112113.github.io/#blog)
  links:
    - https://lists.apache.org/thread.html/r84ccbfc67bfddd35dced494a1f1cba504f49ac60a2a2ae903c5492c3%40%3Cdev.ofbiz.apache.org%3E
    - https://github.com/rapid7/metasploit-framework/blob/master/modules/exploits/linux/http/apache_ofbiz_deserialiation.rb
