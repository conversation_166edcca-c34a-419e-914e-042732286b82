name: poc-yaml-vmware-vcenter-unauthorized-rce-cve-2021-21972
rules:
  - method: GET
    path: /ui/vropspluginui/rest/services/uploadova
    follow_redirects: false
    expression: |
      response.status == 405 && response.body.bcontains(b"Method Not Allowed")
  - method: GET
    path: /ui/vropspluginui/rest/services/getstatus
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(b"States") && response.body.bcontains(b"Install Progress")
detail:
  author: B1anda0(https://github.com/B1anda0)
  links:
    - https://swarm.ptsecurity.com/unauth-rce-vmware/