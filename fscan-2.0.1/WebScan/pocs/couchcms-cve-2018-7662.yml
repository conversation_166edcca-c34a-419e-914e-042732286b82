name: poc-yaml-couchcms-cve-2018-7662
rules:
  - method: GET
    path: /includes/mysql2i/mysql2i.func.php
    follow_redirects: false
    expression: >
      response.status == 200 && response.body.bcontains(b"mysql2i.func.php on line 10") && response.body.bcontains(b"Fatal error: Cannot redeclare mysql_affected_rows() in")
  - method: GET
    path: /addons/phpmailer/phpmailer.php
    follow_redirects: false
    expression: >
      response.status == 200 && response.body.bcontains(b"phpmailer.php on line 10") && response.body.bcontains(b"Fatal error: Call to a menber function add_event_listener() on a non-object in")
detail:
  author: we1x4n(https://we1x4n.github.io/)
  links:
    - https://github.com/CouchCMS/CouchCMS/issues/46
