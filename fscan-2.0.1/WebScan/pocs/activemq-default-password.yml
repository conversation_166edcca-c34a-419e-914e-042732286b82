name: poc-yaml-activemq-default-password
rules:
  - method: GET
    path: /admin/
    expression: |
      response.status == 401 && response.body.bcontains(b"Unauthorized")
  - method: GET
    path: /admin/
    headers:
      Authorization: Basic YWRtaW46YWRtaW4=
    expression: |
      response.status == 200 && response.body.bcontains(b"Welcome to the Apache ActiveMQ Console of") && response.body.bcontains(b"<h2>Broker</h2>")
detail:
  author: pa55w0rd(www.pa55w0rd.online/)
  links:
    - https://blog.csdn.net/ge00111/article/details/72765210