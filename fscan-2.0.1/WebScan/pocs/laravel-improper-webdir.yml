name: poc-yaml-laravel-improper-webdir
rules:
  - method: GET
    path: /storage/logs/laravel.log
    follow_redirects: false
    expression: >
      response.status == 200 && (response.content_type.contains("plain") || response.content_type.contains("octet-stream")) && (response.body.bcontains(b"vendor\\laravel\\framework") || response.body.bcontains(b"vendor/laravel/framework")) && (response.body.bcontains(b"stacktrace") || response.body.bcontains(b"Stack trace"))
detail:
  author: Dem0ns (https://github.com/dem0ns)
  links:
    - https://github.com/dem0ns/improper
