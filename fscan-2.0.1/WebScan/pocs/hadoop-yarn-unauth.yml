name: poc-yaml-hadoop-yarn-unauth
rules:
  - method: GET
    path: /ws/v1/cluster/info
    follow_redirects: true
    headers:
      Content-Type: application/json
    expression: |
      response.status == 200 && response.content_type.contains("json") && response.body.bcontains(b"resourceManagerVersionBuiltOn") && response.body.bcontains(b"hadoopVersion")
detail:
  author: p0wd3r,sharecast
  links:
    - https://github.com/vulhub/vulhub/tree/master/hadoop/unauthorized-yarn
