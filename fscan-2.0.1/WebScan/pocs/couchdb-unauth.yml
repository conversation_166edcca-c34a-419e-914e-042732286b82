name: poc-yaml-couchdb-unauth
rules:
  - method: GET
    path: /_config
    follow_redirects: false
    expression: >
      response.status == 200 && response.body.bcontains(b"httpd_design_handlers") && response.body.bcontains(b"external_manager") && response.body.bcontains(b"replicator_manager")
detail:
  author: FiveAourThe(https://github.com/FiveAourThe)
  links:
    - https://www.seebug.org/vuldb/ssvid-91597