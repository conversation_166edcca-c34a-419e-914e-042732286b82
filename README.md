# 📚 技术笔记知识库

欢迎来到我的技术笔记知识库！这里收集了编程开发、系统管理、网络工具、安全工具等各个领域的实用知识和命令。

## 🗂️ 目录结构

### 📁 [编程开发](./编程开发/)
编程语言和开发工具相关的文档和指南。

#### 🔧 [C++](./编程开发/C++/)
- [恶意软件开发技术示例](./编程开发/C++/恶意软件开发技术示例.md) ⚠️ *仅供安全研究*

#### 🐹 [Go](./编程开发/Go/)
- [Go 编译指南](./编程开发/Go/Go编译指南.md) - 跨平台编译、CGO 配置和编译参数详解

#### 🛠️ [编译工具](./编程开发/编译工具/)
- [Clang 编译指南](./编程开发/编译工具/clang编译指南.md) - MSVC 编译器使用指南
- [MinGW 安装指南](./编程开发/编译工具/MinGW安装指南.md) - 完整的 MinGW-w64 安装配置教程

### 🖥️ [系统管理](./系统管理/)
操作系统管理和配置相关的文档。

#### 🪟 [Windows](./系统管理/Windows/)
- [Windows Defender 管理指南](./系统管理/Windows/WindowsDefender管理指南.md) - 排除列表管理和配置

#### 🐧 [Linux](./系统管理/Linux/)
- [Linux 常用命令](./系统管理/Linux/Linux常用命令.md) - 系统管理、网络配置、日志清理等
- [Debian 开发环境配置](./系统管理/Linux/Debian开发环境配置.md) - Go 语言和开发工具安装

### 🌐 [网络工具](./网络工具/)
网络相关的工具和配置文档。

- [Windows 防火墙端口管理](./网络工具/Windows防火墙端口管理.md) - 防火墙规则配置和管理
- [文件下载命令集合](./网络工具/文件下载命令集合.md) - Windows 和 Linux 文件下载方法

### 🔒 [安全工具](./安全工具/)
安全测试和研究工具的使用指南。

- [Cobalt Strike 证书配置](./安全工具/CobaltStrike证书配置.md) ⚠️ *仅供授权测试*
- [Donut 使用指南](./安全工具/Donut使用指南.md) ⚠️ *Shellcode 生成工具*
- [Windows 权限维持技术](./安全工具/Windows权限维持技术.md) ⚠️ *权限维持技术*
- [安全工具资源索引](./安全工具/安全工具资源索引.md) - 500+ 安全工具大全

### ⌨️ [常用命令](./常用命令/)
各种常用的系统命令和脚本。

- [Windows 系统管理命令集合](./常用命令/Windows系统管理命令集合.md) - 注册表、服务、网络等管理命令

## 🚀 快速导航

### 按用途分类

#### 🔨 开发相关
- [Go 编译指南](./编程开发/Go/Go编译指南.md) - 跨平台编译配置
- [Clang 编译指南](./编程开发/编译工具/clang编译指南.md) - C++ 编译参数
- [MinGW 安装指南](./编程开发/编译工具/MinGW安装指南.md) - Windows 下的 GCC 环境

#### 🛡️ 系统安全
- [Windows Defender 管理](./系统管理/Windows/WindowsDefender管理指南.md) - 防病毒软件配置
- [Cobalt Strike 配置](./安全工具/CobaltStrike证书配置.md) - 渗透测试工具配置
- [权限维持技术](./安全工具/Windows权限维持技术.md) - Windows 权限维持方法

#### 💻 系统管理
- [Windows 命令集合](./常用命令/Windows系统管理命令集合.md) - Windows 系统管理
- [Linux 命令集合](./系统管理/Linux/Linux常用命令.md) - Linux 系统管理
- [Windows 防火墙管理](./网络工具/Windows防火墙端口管理.md) - 端口规则配置
- [文件下载方法](./网络工具/文件下载命令集合.md) - 各种文件下载技巧

## ⚠️ 重要声明

本知识库中的某些内容涉及安全工具和技术，请注意：

- 🔴 **仅供学习和研究使用**
- 🔴 **请勿用于非法用途**
- 🔴 **仅在授权的测试环境中使用**
- 🔴 **使用者需承担相应法律责任**

标有 ⚠️ 符号的文档包含敏感内容，使用前请仔细阅读相关警告。

## 📝 文档规范

### 格式标准
- 所有文档使用 Markdown 格式
- 代码块使用适当的语法高亮
- 包含清晰的目录结构
- 提供相关文档的交叉引用

### 内容组织
- 每个文档都有明确的概述和目录
- 重要操作包含详细的步骤说明
- 提供实用的示例和参考
- 包含注意事项和安全警告

## 🔄 更新日志

### 2025-07-28
- 📁 重新组织目录结构
- 📝 统一文档格式为 Markdown
- 🔧 整理编程开发相关文档
- 🖥️ 完善系统管理指南
- 🔒 添加安全工具配置文档
- 📚 创建完整的索引系统

## 🤝 贡献指南

如果您想为这个知识库贡献内容：

1. 确保内容的准确性和实用性
2. 遵循现有的文档格式规范
3. 为敏感内容添加适当的警告
4. 更新相关的索引和交叉引用

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 Email: [您的邮箱]
- 💬 Issues: [项目 Issues 页面]

---

**最后更新**: 2025-07-28
**文档版本**: v1.0
**总文档数**: 14 个主要文档
