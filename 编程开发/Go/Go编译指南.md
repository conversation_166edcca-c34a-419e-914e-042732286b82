# Go 编程编译指南

## 概述
本文档包含 Go 语言跨平台编译、CGO 配置和常用编译参数的详细说明。

## 📁 目录
- [环境变量配置](#环境变量配置)
- [跨平台编译](#跨平台编译)
- [CGO 编译](#cgo-编译)
- [编译参数详解](#编译参数详解)
- [实用示例](#实用示例)

## 环境变量配置

### Windows 平台编译设置

#### 32位 Windows 编译环境
```powershell
$env:GOOS = "windows"
$env:GOARCH = "386"
$env:CGO_ENABLED = "1"
$env:PATH += ";C:\msys64\mingw32\bin"  
$env:CC = "C:\msys64\mingw32\bin\gcc.exe"
```

#### 64位 Windows 编译环境
```powershell
$env:GOOS = "windows"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "1"
$env:PATH += ";C:\msys64\mingw64\bin"  
$env:CC = "C:\msys64\mingw64\bin\gcc.exe"
```

### Linux 平台编译设置
```powershell
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"
```

## 跨平台编译

### 支持的平台组合
```bash
# Windows
GOOS=windows GOARCH=amd64  # Windows 64位
GOOS=windows GOARCH=386    # Windows 32位

# Linux
GOOS=linux GOARCH=amd64    # Linux 64位
GOOS=linux GOARCH=386      # Linux 32位
GOOS=linux GOARCH=arm64    # Linux ARM64

# macOS
GOOS=darwin GOARCH=amd64   # macOS Intel
GOOS=darwin GOARCH=arm64   # macOS Apple Silicon
```

### 查看支持的平台
```bash
go tool dist list
```

## CGO 编译

### CGO 配置说明
- `CGO_ENABLED=1` - 启用 CGO，可以调用 C 代码
- `CGO_ENABLED=0` - 禁用 CGO，纯 Go 编译

### MinGW 工具链配置
确保安装了 MinGW-w64 工具链：
```bash
# 检查 GCC 版本
gcc --version

# 检查工具链路径
where gcc
```

## 编译参数详解

### 基本编译参数
```bash
go build                    # 基本编译
go build -o output.exe      # 指定输出文件名
go build .                  # 编译当前目录
```

### 优化参数

#### `-ldflags` 链接器参数
```bash
-s                         # 去除符号表
-w                         # 去除调试信息
-H=windowsgui             # Windows GUI 程序（无控制台）
-X 'main.version=1.0'     # 设置字符串变量值
```

#### `-gcflags` 编译器参数
```bash
-N                        # 禁用优化
-l                        # 禁用内联
"all=-N -l"              # 对所有包禁用优化和内联
```

#### 其他参数
```bash
-trimpath                 # 移除文件路径信息
-race                     # 启用竞态检测
-tags                     # 构建标签
```

## 实用示例

### GUI 应用程序编译
```bash
# 基本 GUI 程序
go build -ldflags="-H=windowsgui" -o app.exe main.go

# 优化的 GUI 程序
go build -trimpath -ldflags="-s -w -H=windowsgui" -o app.exe main.go

# 带调试信息的 GUI 程序
go build -gcflags="all=-N -l" -ldflags="-s -w -H=windowsgui" -o app.exe main.go
```

### 控制台应用程序编译
```bash
# Linux 控制台程序
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -trimpath -ldflags="-s -w" -o app main.go

# Windows 控制台程序
GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o app.exe main.go
```

### 带版本信息的编译
```bash
go build -ldflags="-s -w -X 'main.version=1.0.0' -X 'main.buildTime=$(date)'" -o app.exe main.go
```

### 静态链接编译
```bash
# 完全静态链接
CGO_ENABLED=0 go build -a -ldflags="-s -w -extldflags '-static'" -o app main.go
```

## 高级技巧

### 条件编译
使用构建标签进行条件编译：

```go
// +build windows

package main

// Windows 特定代码
```

```go
// +build linux

package main

// Linux 特定代码
```

### 嵌入资源
Go 1.16+ 支持嵌入文件：

```go
//go:embed static/*
var staticFiles embed.FS
```

### 模块代理设置
```bash
# 设置模块代理
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn
```

## 常见问题

### CGO 编译错误
1. 确保安装了正确的 C 编译器
2. 检查 PATH 环境变量
3. 验证 CC 环境变量指向正确的编译器

### 交叉编译问题
1. 某些包不支持交叉编译
2. CGO 代码需要目标平台的工具链
3. 使用 `CGO_ENABLED=0` 禁用 CGO

### 文件大小优化
```bash
# 最小化文件大小
go build -ldflags="-s -w" -trimpath -o app.exe main.go

# 使用 UPX 压缩（可选）
upx --best app.exe
```

## 相关工具

### 有用的第三方工具
- **UPX** - 可执行文件压缩
- **GoReleaser** - 自动化发布工具
- **govvv** - 版本信息注入工具

### 构建脚本示例
```bash
#!/bin/bash
# build.sh

VERSION=$(git describe --tags --always)
BUILDTIME=$(date -u '+%Y-%m-%d_%H:%M:%S')

go build -ldflags="-s -w -X main.version=${VERSION} -X main.buildTime=${BUILDTIME}" -o app.exe main.go
```

## 注意事项
- 交叉编译时注意目标平台的兼容性
- CGO 代码会增加编译复杂度
- 使用 `-trimpath` 可以提高安全性
- GUI 程序记得使用 `-H=windowsgui` 参数

## 相关文档
- [C++ 编译指南](../C++/)
- [编译工具使用指南](../编译工具/)
- [Go 官方文档](https://golang.org/doc/)
