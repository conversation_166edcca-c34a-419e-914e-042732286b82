# Clang/MSVC 编译指南

## 概述
本文档包含使用 Microsoft Visual C++ 编译器 (cl.exe) 编译 C++ 程序的常用命令和参数说明。

## 编译命令

### GUI 应用程序编译
用于编译 Windows GUI 应用程序（无控制台窗口）：

```bash
cl main.cpp /nologo /O2 /Ot /Ob2 /Oi /GL /Gy /MT /GS- /GR- /DNDEBUG /EHsc /DUNICODE /D_UNICODE /link /OPT:REF /OPT:ICF /INCREMENTAL:NO /SUBSYSTEM:WINDOWS
```

### 控制台应用程序编译
用于编译控制台应用程序：

```bash
cl main.cpp /nologo /O2 /Ot /Ob2 /Oi /GL /Gy /MT /GS- /GR- /DNDEBUG /EHsc /link /OPT:REF /OPT:ICF /INCREMENTAL:NO /SUBSYSTEM:CONSOLE
```

## 编译参数说明

### 优化参数
- `/O2` - 最大化速度优化
- `/Ot` - 优化代码速度
- `/Ob2` - 内联函数展开
- `/Oi` - 启用内置函数
- `/GL` - 全程序优化
- `/Gy` - 启用函数级链接

### 运行时库
- `/MT` - 多线程静态运行时库

### 安全和调试
- `/GS-` - 禁用缓冲区安全检查
- `/GR-` - 禁用运行时类型信息
- `/DNDEBUG` - 定义 NDEBUG 宏（发布版本）

### 异常处理
- `/EHsc` - 启用 C++ 异常处理

### Unicode 支持
- `/DUNICODE` - 定义 UNICODE 宏
- `/D_UNICODE` - 定义 _UNICODE 宏

### 链接器参数
- `/OPT:REF` - 移除未引用的函数和数据
- `/OPT:ICF` - 启用相同 COMDAT 折叠
- `/INCREMENTAL:NO` - 禁用增量链接
- `/SUBSYSTEM:WINDOWS` - Windows GUI 子系统
- `/SUBSYSTEM:CONSOLE` - 控制台子系统

## 使用建议
1. GUI 应用程序使用 `/SUBSYSTEM:WINDOWS` 避免显示控制台窗口
2. 发布版本建议使用所有优化参数以获得最佳性能
3. 调试版本可以移除优化参数并添加 `/Zi` 生成调试信息
